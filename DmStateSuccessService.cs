﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public class DmStateSuccessService
    {
        public static SuccessModel ReadSuccessState(XElement successElement)
        {
            //var successModel = new SuccessModel();
            var successModel = new SuccessModel
            {
                ActionList = new List<SuccessActionModel>() // List of Actions
            };

            foreach (var actionElement in successElement.Elements("action"))
            {
                var actionModel = new SuccessActionModel
                {
                    Name = actionElement.Attribute("label")?.Value ?? "always",
                    Next = actionElement.Attribute("next")?.Value,
                    ConditionTransitionsList = new List<List<ConditionModel>>(), // List of condition lists
                    SessionMappingList = new List<SessionMappingModel>(),
                    PromptList = new List<PromptModel>(),
                };
                var gotoElement = actionElement.Element("gotodialog");

                if (gotoElement != null)
                {
                    //26638
                    var nextAttr = gotoElement.Attribute("next")?.Value;
                    if (!string.IsNullOrEmpty(nextAttr)){
                        if (nextAttr.Contains(".dvxml#")){
                            var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                            if (parts.Length == 2)
                            {
                                actionModel.Next = parts[0]+ ".dvxml";      // before .dvxml#
                                SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                    key = "nextState",
                                    value = parts[1]
                                };
                                actionModel.SessionMappingList.Add(sessionMappingModel);
                            //transition.sessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                            }
                        }
                        else{
                        actionModel.Next = nextAttr+ ".dvxml";
                        }
                    }
                    //26638
                    //actionModel.Next = gotoElement.Attribute("next")?.Value + ".dvxml";
                }

                var sessionMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("session-mapping"));
                actionModel.SessionMappingList.AddRange(sessionMappings);
                var varMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("var"));
                actionModel.SessionMappingList.AddRange(varMappings);

                //process audio element
                var audioElements = actionElement.Elements("audio");
                if (audioElements != null)
                {
                    foreach (var audioElement in audioElements)
                    {

                        foreach (var promptElement in audioElement.Elements("prompt"))
                        {
                            var promptModel = ExtractPromptModel(promptElement);
                            if (promptModel != null)
                            {
                                actionModel.PromptList.Add(promptModel);
                            }
                        }
                    }

                }

                var ifElements = actionElement.Elements("if");
                if(ifElements.Any())
                {
                    foreach (var ifElement in ifElements)
                    {
                        var conditionList = new List<ConditionModel>(); // List to store conditions for this 'if' block
                        ProcessTopIfCondition(ifElement, conditionList);
                        actionModel.ConditionTransitionsList.Add(conditionList); // Add this list to the model
                    }
                }
                else {

                    var innerActionElement = actionElement.Element("action");
                    if (innerActionElement != null && actionModel.Next == null)
                    {
                        actionModel.Next = innerActionElement.Attribute("next")?.Value;
                    }
                }

                successModel.ActionList.Add(actionModel);
            }

            return successModel;
        }
        private static void ProcessTopIfCondition(XElement ifElement, List<ConditionModel> conditionList)
        {
            string condition = ifElement.Attribute("cond")?.Value;
            var transition = ProcessConditionElement(ifElement);

            // Create a ConditionModel for the top-level 'if'
            var topCondition = new ConditionModel
            {
                Condition = condition,
                Transitions = transition
            };

            // Process nested conditions
            ProcessNestedConditions(ifElement, topCondition);

            // Add the top condition to the list
            conditionList.Add(topCondition);

            // Process sibling 'elseif' and 'else' conditions
            ProcessSiblingConditions(ifElement, conditionList);
        }

        private static void ProcessNestedConditions(XElement parentElement, ConditionModel parentCondition)
        {
            // Extract nested 'if' condition from the top 'if' element
            var nestedIfElement = parentElement.Element("if");
            if (nestedIfElement != null)
            {
                // Add nested 'if' condition to the parent's InnerConditionTransitions
                string nestedCondition = nestedIfElement.Attribute("cond")?.Value;
                var nestedTransition = ProcessConditionElement(nestedIfElement);

                ConditionModel nestedConditionModel = new ConditionModel
                {
                    Condition = nestedCondition,
                    Transitions = nestedTransition
                };

                parentCondition.Transitions.InnerConditionTransitions.Add(nestedConditionModel);

                //Adding the below code for nested if condition
                ProcessNestedConditions(nestedIfElement, parentCondition);

                // Process nested 'elseif' and 'else' within the nested 'if'
                ProcessSiblingConditions(nestedIfElement, parentCondition.Transitions);
            }
        }

        private static void ProcessSiblingConditions(XElement parentElement, List<ConditionModel> conditionList)
        {
            // Process 'elseif' elements
            var elseifElements = parentElement.Elements("elseif");
            foreach (var elseifElement in elseifElements)
            {
                string condition = elseifElement.Attribute("cond")?.Value;
                var transition = ProcessConditionElement(elseifElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };
                ProcessNestedConditions(elseifElement, condObj);
                // Add to conditionList
                conditionList.Add(condObj);
            }

            // Process 'else' element
            var elseElement = parentElement.Element("else");
            if (elseElement != null)
            {
                string condition = "else";
                var transition = ProcessConditionElement(elseElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };
                ProcessNestedConditions(elseElement, condObj);
                // Add to conditionList
                conditionList.Add(condObj);
            }
        }

        private static void ProcessSiblingConditions(XElement parentElement, TransitionModel parentTransition)
        {
            // Process 'elseif' elements
            var elseifElements = parentElement.Elements("elseif");
            foreach (var elseifElement in elseifElements)
            {
                string condition = elseifElement.Attribute("cond")?.Value;
                var transition = ProcessConditionElement(elseifElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };

                // Add to InnerConditionTransitions
                parentTransition.InnerConditionTransitions.Add(condObj);
            }

            // Process 'else' element
            var elseElement = parentElement.Element("else");
            if (elseElement != null)
            {
                string condition = "else";
                var transition = ProcessConditionElement(elseElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };

                //Adding the below code for transition in else
                ProcessNestedConditions(elseElement, condObj);

                // Add to InnerConditionTransitions
                parentTransition.InnerConditionTransitions.Add(condObj);
            }
        }

        private static TransitionModel ProcessConditionElement(XElement element)
        {
            var transition = new TransitionModel
            {
                sessionMappings = new List<SessionMappingModel>(),
                InnerConditionTransitions = new List<ConditionModel>() ,// Ensure it's empty for flat conditions
                PromptList = new List<PromptModel>() 
            };

            // Use SessionMappingHelper to process session-mapping elements directly under the condition element
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(element.Elements("session-mapping"));
            transition.sessionMappings.AddRange(sessionMappings);
            var varMappings = SessionMappingHelper.ProcessSessionMappings(element.Elements("var"));
            transition.sessionMappings.AddRange(varMappings);

            //process audio element
            var audioElements = element.Elements("audio");
            if (audioElements != null)
            {
                foreach (var audioElement in audioElements)
                {

                    foreach (var promptElement in audioElement.Elements("prompt"))
                    {
                        var promptModel = ExtractPromptModel(promptElement);
                        if (promptModel != null)
                        {
                            transition.PromptList.Add(promptModel);
                        }
                    }
                }

            }

            // Process action element if present
            var actionElement = element.Element("action");
            if (actionElement != null)
            {
                transition.label = actionElement.Attribute("label")?.Value ?? "fixedtransition";
                transition.next = actionElement.Attribute("next")?.Value;
                var gotoElementAction = actionElement.Element("gotodialog");

                if (gotoElementAction != null)
                {
                    //26638
                    var nextAttr = gotoElementAction.Attribute("next")?.Value;
                    if (!string.IsNullOrEmpty(nextAttr)){
                        if (nextAttr.Contains(".dvxml#")){
                            var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                            if (parts.Length == 2)
                            {
                                transition.next = parts[0]+ ".dvxml";      // before .dvxml#
                                SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                    key = "nextState",
                                    value = parts[1]
                                };
                                transition.sessionMappings.Add(sessionMappingModel);
                                //transition.sessionMappings.Add(sessionMappingModel);
                                //model.gotoDialogReference = parts[1];    // after #
                            }
                        }
                        else{
                        transition.next = nextAttr+ ".dvxml";
                        }
                    }

                    //26638
                    //transition.next = gotoElement.Attribute("next")?.Value + ".dvxml";
                }

                // Process session-mapping elements inside the action
                var actionSessionMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("session-mapping"));
                transition.sessionMappings.AddRange(actionSessionMappings);
                var actionVarMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("var"));
                transition.sessionMappings.AddRange(actionVarMappings);
            }

            var gotoElement = element.Element("gotodialog");

            if (gotoElement != null)
            {
                //26638
                var nextAttr = gotoElement.Attribute("next")?.Value;
                if (!string.IsNullOrEmpty(nextAttr)){
                    if (nextAttr.Contains(".dvxml#")){
                        var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                        if (parts.Length == 2)
                        {
                            transition.next = parts[0]+ ".dvxml";      // before .dvxml#
                            SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                key = "nextState",
                                value = parts[1]
                            };
                            transition.sessionMappings.Add(sessionMappingModel);
                            //transition.sessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                        }
                    }
                    else{
                    transition.next = nextAttr+ ".dvxml";
                    }
                }

                    //26638
                    //transition.next = gotoElement.Attribute("next")?.Value + ".dvxml";
            }

            return transition;
        }
       /* private static void ProcessActionElement(XElement actionElement, Dictionary<string, TransitionModel> conditionTransitions, Dictionary<string, List<PromptModel>> conditionPrompts, string parentCondition = null)
        {
            foreach (var ifElement in actionElement.Elements("if"))
            {
                string condition = ifElement.Attribute("cond")?.Value;
                if (!string.IsNullOrEmpty(parentCondition))
                {
                    condition = $"{parentCondition} && ({condition})";
                }

                if (condition != null)
                {
                    foreach (var innerActionElement in ifElement.Elements("action"))
                    {
                        var transitionModel = new TransitionModel
                        {
                            next = innerActionElement.Attribute("next")?.Value
                        };

                        foreach (var sessionMappingElement in innerActionElement.Elements("session-mapping"))
                        {
                            string expr = sessionMappingElement.Attribute("expr")?.Value;
                            string path = sessionMappingElement.Attribute("path")?.Value;
                            string value = sessionMappingElement.Attribute("value")?.Value;
                            string type = sessionMappingElement.Attribute("type")?.Value ?? "String";
                            string key = sessionMappingElement.Attribute("key")?.Value;

                            if (value == null)
                            {
                                if (path == null)
                                {
                                    value = expr;
                                    type = "expression";
                                }
                                else
                                {

                                    value = "Global." + path;
                                    type = "expression";
                                }
                            }

                            var sessionMapping = new SessionMappingModel
                            {
                                key = key,
                                value = value,
                                type = type
                            };

                            transitionModel.sessionMappings.Add(sessionMapping);

                        }

                        conditionTransitions[condition] = transitionModel;

                        var promptList = new List<PromptModel>();
                        foreach (var promptElement in innerActionElement.Descendants("prompt"))
                        {
                            var promptModel = new PromptModel
                            {
                                PromptId = promptElement.Attribute("id")?.Value,
                                Text = promptElement.Descendants("audiofile").FirstOrDefault()?.Attribute("text")?.Value
                            };
                            promptList.Add(promptModel);
                        }
                        if (promptList.Any())
                        {
                            conditionPrompts[condition] = promptList;
                        }
                    }

                    foreach (var elseElement in ifElement.Elements("else"))
                    {
                        var elseCondition = "else";
                        if (!string.IsNullOrEmpty(parentCondition))
                        {
                            elseCondition = $"{parentCondition} && !({ifElement.Attribute("cond")?.Value})";
                        }

                        foreach (var innerActionElement in elseElement.Elements("action"))
                        {
                            var transitionModel = new TransitionModel
                            {
                                next = innerActionElement.Attribute("next")?.Value
                            };

                            foreach (var sessionMappingElement in innerActionElement.Elements("session-mapping"))
                            {
                                string expr = sessionMappingElement.Attribute("expr")?.Value;
                                string path = sessionMappingElement.Attribute("path")?.Value;
                                string value = sessionMappingElement.Attribute("value")?.Value;
                                string type = sessionMappingElement.Attribute("type")?.Value ?? "String";
                                string key = sessionMappingElement.Attribute("key")?.Value;

                                if (value == null)
                                {
                                    if (path == null)
                                    {
                                        value = expr;
                                        type = "expression";
                                    }
                                    else
                                    {

                                        value = "Global." + path;
                                        type = "expression";
                                    }
                                }

                                var sessionMapping = new SessionMappingModel
                                {
                                    key = key,
                                    value = value,
                                    type = type
                                };

                                transitionModel.sessionMappings.Add(sessionMapping);

                            }

                            conditionTransitions[elseCondition] = transitionModel;

                            var promptList = new List<PromptModel>();
                            foreach (var promptElement in innerActionElement.Descendants("prompt"))
                            {
                                var promptModel = new PromptModel
                                {
                                    PromptId = promptElement.Attribute("id")?.Value,
                                    Text = promptElement.Descendants("audiofile").FirstOrDefault()?.Attribute("text")?.Value
                                };
                                promptList.Add(promptModel);
                            }
                            if (promptList.Any())
                            {
                                conditionPrompts[elseCondition] = promptList;
                            }
                        }
                    }
                }
            }

            var audioElement = actionElement.Element("audio");
            if (audioElement != null)
            {
                bool hasIfOrElse = false;
                var conditionPrompt = new Dictionary<string, List<PromptModel>>();

                foreach (var ifElement in audioElement.Elements("if"))
                {
                    hasIfOrElse = true;
                    string condition = ParseCondition(ifElement);
                    if (string.IsNullOrEmpty(condition))
                    {
                        condition = "default";
                    }

                    var promptList = new List<PromptModel>();
                    foreach (var promptElement in ifElement.Descendants("prompt"))
                    {
                        var promptModel = new PromptModel
                        {
                            PromptId = promptElement.Attribute("id")?.Value,
                            Text = promptElement.Descendants("audiofile").FirstOrDefault()?.Attribute("text")?.Value
                        };
                        promptList.Add(promptModel);
                    }

                    if (promptList.Any())
                    {
                        conditionPrompt[condition] = promptList;
                    }

                    // Handle nested <else> tag within the <if> element
                    var elseElement = ifElement.Element("else");
                    if (elseElement != null)
                    {
                        var elsePromptList = new List<PromptModel>();
                        foreach (var promptElement in elseElement.Descendants("prompt"))
                        {
                            var promptModel = new PromptModel
                            {
                                PromptId = promptElement.Attribute("id")?.Value,
                                Text = promptElement.Descendants("audiofile").FirstOrDefault()?.Attribute("text")?.Value
                            };
                            elsePromptList.Add(promptModel);
                        }

                        if (elsePromptList.Any())
                        {
                            conditionPrompt["else"] = elsePromptList;
                        }
                    }
                }

                if (!hasIfOrElse)
                {
                    var promptList = new List<PromptModel>();
                    foreach (var promptElement in audioElement.Descendants("prompt"))
                    {
                        var promptModel = new PromptModel
                        {
                            PromptId = promptElement.Attribute("id")?.Value,
                            Text = promptElement.Descendants("audiofile").FirstOrDefault()?.Attribute("text")?.Value
                        };
                        promptList.Add(promptModel);
                    }
                    if (promptList.Any())
                    {
                        conditionPrompts["default"] = promptList;
                    }
                }
            }
        }

            // Continue processing with conditionPrompts dictionary


            private static string ParseCondition(XElement ifElement)
        {
            var conditionString = ifElement.Attribute("cond")?.Value;

            if (string.IsNullOrEmpty(conditionString))
            {
                var conditionElement = ifElement.Element("condition");
                if (conditionElement != null)
                {
                    var leftParam = conditionElement.Descendants("param").FirstOrDefault(p => p.Attribute("name")?.Value == "value")?.Attribute("value")?.Value;
                    var rightParam = conditionElement.Descendants("param").FirstOrDefault(p => p.Attribute("name")?.Value == "compare")?.Attribute("value")?.Value;
                    var operatorElement = conditionElement.Descendants("param").FirstOrDefault(p => p.Attribute("name")?.Value == "operator");
                    var operatorValue = operatorElement?.Attribute("value")?.Value;

                    if (leftParam != null && rightParam != null)
                    {
                        if (operatorValue == "NOT")
                        {
                            conditionString = $"{leftParam} != {rightParam}";
                        }
                        else
                        {
                            conditionString = $"{leftParam} == {rightParam}";
                        }
                    }
                }
            }

            return conditionString;
        }*/
        private static PromptModel ExtractPromptModel(XElement promptElement)
        {
            // Extract prompt segments
            String promptType = promptElement.Attribute("type")?.Value;

            if ((promptType == "custom"))
            {
                String customText = "custom text from java class";
                return new PromptModel
                {
                    PromptId = "custom_prompt_id",
                    Text = customText
                };
            }
            var promptSegments = promptElement.Element("prompt-segments");
            if (promptSegments != null)
            {
                // Extract audiofile elements
                var audiofiles = promptSegments.Elements("audiofile");
                foreach (var audiofile in audiofiles)
                {
                    string text = audiofile.Attribute("text")?.Value;
                    if (!string.IsNullOrEmpty(text))
                    {
                        // Successfully created PromptModel from audiofile
                        return new PromptModel
                        {
                            PromptId = promptElement.Attribute("id")?.Value,
                            Text = text
                        };
                    }
                }
            }
            else
            {
                // Use expr attribute from prompt element
                string exprValue = promptElement.Attribute("expr")?.Value;
                if (!string.IsNullOrEmpty(exprValue))
                {
                    string text = "Global." + StateUtility.SimplifyCondition(exprValue);
                    // Successfully created PromptModel from expr attribute
                    return new PromptModel
                    {
                        PromptId = "customPrompt", // Assigning a default ID for custom prompts
                        Text = text
                    };
                }
            }

            return null;
        }
    }

}
