﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class SuccessActionModel
    {
        public string Name { get; set; }
        public string Next { get; set; }
        public List<SessionMappingModel> SessionMappingList { get; set; }
        //public Dictionary<string, TransitionModel> ConditionTransitions { get; set; }
        public List<List<ConditionModel>> ConditionTransitionsList { get; set; }
        public List<PromptModel> PromptList { get; set; }
        // public Dictionary<string, List<PromptModel>> ConditionPrompts { get; set; }
        public bool isCommand = false;
        public SuccessActionModel()
        {
            SessionMappingList = new List<SessionMappingModel>();
            ConditionTransitionsList = new List<List<ConditionModel>>();
            
        }

    }
}
