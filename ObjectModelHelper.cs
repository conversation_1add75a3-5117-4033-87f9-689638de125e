﻿using Microsoft.Bot.ObjectModel.Yaml;
using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using Microsoft.PowerFx.Types;
using System.Text.RegularExpressions;
using System.Linq.Expressions;
using System.IO;

//rename namespace from MixToCopilotStudioConverter to NDFToCopilotStudioConverter
namespace NDFToCopilotStudioConverter
{

    public class ObjectModelHelper
    {
        public static MessageActivityTemplate ParseTemplate(string text)
        {
            return YamlSerializer.Deserialize<MessageActivityTemplate>($"text:\n  - \"{text.Replace("\"", "\\\"")}\"") ?? throw new ArgumentException($"Line {text} parsed to null.");
        }

        public static SendActivity GetStaticMessage(string text, string ttsText)
        {
            SendActivity.Builder sendActivity = new SendActivity.Builder()
            {
                Id = "sendActivity_" + GenerateRandomID(),
                Activity = GetSingleLineActivity(text),
            };
            return sendActivity.Build();
        }
        public static MessageActivityTemplate GetSingleLineActivity(string text)
        {
            return new MessageActivityTemplate.Builder()
            {
                Text =
                {
                    new TemplateLine.Builder()
                    {
                        Segments =
                        {
                            new TextSegment(text)
                        }
                    }
                }
            }.Build();
        }

        public static void AddSegmentsFromPromptToTemplateLine(string input, IList<TemplateLine.Builder> templateLineList)
        {
            String replacementString = input;
            string pattern = @"\[(?<value>[^\[\]\\|]+)\|(?<uuid>[0-9a-fA-F-]{36})\]";
            Regex regex = new Regex(pattern);

            TemplateLine.Builder templateLine = new TemplateLine.Builder();
            //TextSegment until a match. Match is an Expression Segment. Text until the next Expression Segment. Repeat.
            if (input.Length == 0)
            {
                //TODO: handle empty input?
            }
            int currentIndex = 0;
            //TODO: handle no match

            foreach (Match match in regex.Matches(input))
            {
                if (match.Success)
                {
                    if (match.Groups.TryGetValue("uuid", out Group uuid))
                    {
                        // This UUID could be a variable ID or a variable expression id. The reference could also be to a ConceptId, ConceptIdLiteral, ConceptIdFormattedliteral, currentEntity, or Intent
                        //string newString = findInAnnotations(uuid.Value, annotations);
                        //Console.WriteLine("input:");
                        //Console.WriteLine(input);
                        if (input.Contains("{"))
                        {
                            templateLine.Segments.Add(new ExpressionSegment(ValueExpression.Expression(input.Substring(currentIndex, match.Index - currentIndex))));
                        }
                        if (match.Index > currentIndex)
                        {
                            //TODO: handle text between matches
                            templateLine.Segments.Add(new TextSegment(input.Substring(currentIndex, match.Index - currentIndex)));
                        }
                        // templateLine.Segments.Add(new ExpressionSegment(ValueExpression.Expression(newString)));
                        currentIndex = match.Index + match.Length;
                    }
                    if (match.Groups.TryGetValue("value", out Group value))
                    {
                        // value is the UI text in Mix for the variable reference. It is not used in the actual rendered prompt
                    }
                }
            }
            //Console.WriteLine("INPUT :");
            if (input.Contains("Global"))
            {
                //Console.WriteLine("Inside if");
                string[] inputArray = input.Split("<br>");
                foreach (string str in inputArray)
                {

                    if (str.Trim().StartsWith("Global"))
                    {
                        //Console.WriteLine("Inside if :"+str);
                        //str.Replace("<br>", "");
                        templateLine.Segments.Add(new ExpressionSegment(ValueExpression.Expression(str.Substring(currentIndex))));
                    }
                    else
                    {
                        //Console.WriteLine("Inside else :" + str);
                        //templateLine.Segments.Add(new TextSegment(" <br> " + str.Substring(currentIndex)));
                        templateLine.Segments.Add(new TextSegment(str.Substring(currentIndex)));
                    }
                }

            }
            else if (currentIndex < input.Length)
            {
                templateLine.Segments.Add(new TextSegment(input.Substring(currentIndex)));
            }
            templateLineList.Add(templateLine.Build());
            return;
        }

        public static void AddSegmentsFromPromptToTemplateLineSpeak(string input, IList<TemplateLine.Builder> templateLineList)
        {
            string pattern = @"<audio src=""(?<src>[^""]+)"">(?<content>.*?)</audio>(?<break><br>)?";
            Regex regex = new Regex(pattern);

            TemplateLine.Builder templateLine = new TemplateLine.Builder();
            int currentIndex = 0;

            foreach (Match match in regex.Matches(input))
            {
                if (match.Success)
                {
                    // Handle text before the match
                    if (match.Index > currentIndex)
                    {
                        templateLine.Segments.Add(new TextSegment(input.Substring(currentIndex, match.Index - currentIndex)));
                    }

                    // Extract audio attributes and content
                    string audioSrc = match.Groups["src"].Value;
                    string audioContent = match.Groups["content"].Value.Trim();
                    string breakTag = match.Groups["break"].Success ? "<br>" : string.Empty;

                    // Check if the content inside <audio> is a Global variable
                    if (audioContent.StartsWith("Global."))
                    {
                         audioContent = $"{{{audioContent}}}"; // Wrap with curly braces
                        
                    }
                  //  string updatedaudioSrc = Regex.Replace(audioSrc, @"\bGlobal\.[a-zA-Z0-9_]+\b", match => "{" + match.Value + "}");
                    // Build the new <audio> tag with modified content
                    string modifiedAudioTag = $"<audio src=\"{audioSrc}\"> {audioContent} </audio> {breakTag}";
                    Console.WriteLine("----------------------------" + modifiedAudioTag+ "-------------------------------------");
                    templateLine.Segments.Add(new TextSegment(modifiedAudioTag));

                    currentIndex = match.Index + match.Length;
                }
            }

            // Handle any remaining text after the last match
            if (currentIndex < input.Length)
            {
                templateLine.Segments.Add(new TextSegment(input.Substring(currentIndex)));
            }

            templateLineList.Add(templateLine.Build());
        }



        public static MessageActivityTemplate GetSingleLineActivityWithValueExpression(string text, string expression, string? separator = " ")
        {
            return new MessageActivityTemplate.Builder()
            {
                Text =
                {
                    new TemplateLine.Builder()
                    {
                        Segments =
                        {
                            new TextSegment(text + separator),
                            new ExpressionSegment(ValueExpression.Expression(expression))
                        }
                    }
                }
            }.Build();
        }
        // Might not be consumable by Copilot Studio.
        public static GlobalVariableComponent GetGlobalVariableComponent(string variableName, string schemaName)
        {
            return new GlobalVariableComponent.Builder()
            {
                SchemaName = schemaName,
                Variable = new Variable.Builder()
                {
                    Name = variableName,
                    Scope = VariableScope.Conversation,
                    IsExternalInitializationAllowed = true,
                    AIVisibility = VariableAIVisibility.UseInAIContext

                }.Build()

            }.Build();
        }
        public static SetVariable GetSetVariableWithDefaultJSON(string variableName, string defaultJSON, Boolean global = true)
        {
            string scope = "Global.";
            if (!global)
            {
                scope = "Topic.";
            }

            return new SetVariable.Builder()
            {
                Id = "setVariable_" + GenerateRandomID(),
                Variable = InitializablePropertyPath.Create(scope + variableName),
                Value = ValueExpression.Expression("=" + defaultJSON),
                DisplayName = getLoggingStateName(variableName,"VM")
            }.Build();
        }
        public static string GenerateRandomID()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            var id = new string(Enumerable.Repeat(chars, 5)
                .Select(s => s[random.Next(s.Length)]).ToArray());
            return id;
        }

        public static SetVariable SetVariableToExpression(string leftHandSideVariablePath, string rightHandSideExpression)
        {
            SetVariable setVariable = new SetVariable.Builder()
            {
                Id = "setVariable_" + GenerateRandomID(),
                Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
                Value = ValueExpression.Expression(rightHandSideExpression),
                DisplayName = getLoggingStateName(leftHandSideVariablePath,"VM")
            }.Build();
            return setVariable;
        }
        public static SetVariable SetVariableToExpression(string leftHandSideVariablePath, string rightHandSideExpression, string mcsNodeId)
        {
            //Console.WriteLine("SetVariableToExpression.mcsNodeId: " + mcsNodeId + " = " + rightHandSideExpression);
            bool isVariable = false;
            // Store the original value of rightHandSideConstant
            string originalValue = rightHandSideExpression;

            // Apply the regex to replace "GlobalVars." with "Global."
            rightHandSideExpression = Regex.Replace(rightHandSideExpression, @"\bGlobalVars\.", "Global.");

            // Check if the value was updated and set isVariable to true if it was
            isVariable = !string.Equals(originalValue, rightHandSideExpression, StringComparison.Ordinal);
            

            SetVariable setVariable = new SetVariable.Builder()
            {
                Id = mcsNodeId + "_"+ GenerateRandomID(),
                Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
                Value = ValueExpression.Expression(rightHandSideExpression),
                DisplayName = getLoggingStateName(mcsNodeId, "VM")
            }.Build();
            return setVariable;
        }
        public static SetVariable SetVariable(string leftHandSideVariablePath, string rightHandSideConstant, string type, string stateId)
        {
            SetVariable setVariable = null;
            rightHandSideConstant = RemoveSurroundingQuotes(rightHandSideConstant);
            //type = "String";
            if (type == "expression")
            {
                return SetVariableToExpression(leftHandSideVariablePath, rightHandSideConstant, stateId);
            }
            if (rightHandSideConstant != null && (type == null || type == "String"))
            {
                //if (rightHandSideConstant.StartsWith("GlobalVars") || rightHandSideConstant.Contains("GlobalVars") || rightHandSideConstant.Contains("(GlobalVars"))
                //{
                //    //this is a variable. LHS variable is being assigned with a value of a RHS variable
                //    rightHandSideConstant = rightHandSideConstant.Replace("GlobalVars.", "Global.GlobalVars.");
                //    type = "";

                //}
                bool isVariable = false;
                // Store the original value of rightHandSideConstant
                string originalValue = rightHandSideConstant;

                // Apply the regex to replace "GlobalVars." with "Global."
                rightHandSideConstant = Regex.Replace(rightHandSideConstant, @"\bGlobalVars\.", "Global.GlobalVars.");

                // Check if the value was updated and set isVariable to true if it was
                isVariable = !string.Equals(originalValue, rightHandSideConstant, StringComparison.Ordinal);
                if (isVariable)
                {
                    type = "";
                }
                else if (rightHandSideConstant == "true" || rightHandSideConstant == "false")
                {
                    type = "Boolean";

                }
                else if (int.TryParse(rightHandSideConstant, out _))
                {
                    type = "Integer";

                }
                else
                {
                    type = "String"; // If it's neither boolean nor integer, treat as string

                }
            }
            if (type == "String")
            {
                // "\"aditi\""
              //  rightHandSideConstant = "\"" + rightHandSideConstant + "\"";

                setVariable = new SetVariable.Builder()
                {
                    Id = "setVariable_" + GenerateRandomID(),
                    //DisplayName = "Assign_test",
                    Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
                    Value = new StringDataValue(rightHandSideConstant),
                    DisplayName = getLoggingStateName(stateId, "VM")
                }.Build();
                return setVariable;
            }
            else if (type == "Integer")
            {
                setVariable = new SetVariable.Builder()
                {
                    Id = "setVariable_" + GenerateRandomID(),
                    Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
                    Value = new NumberDataValue(int.Parse(rightHandSideConstant)) ,// Assuming int.Parse is the intended conversion
                    DisplayName =   getLoggingStateName(stateId, "VM")
                }.Build();
                return setVariable;
            }
            else if (type == "Boolean" || type == "Bool")
            {
                if (rightHandSideConstant == null)
                {
                    rightHandSideConstant = "false";
                }
                setVariable = new SetVariable.Builder()
                {

                    Id = "setVariable_" + GenerateRandomID(),
                    Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
                    Value = new BooleanDataValue(String.Equals(rightHandSideConstant.ToLower(),"true")),
                    DisplayName = getLoggingStateName(stateId, "VM")

                }.Build();

                return setVariable;
            }
            else
            {
                setVariable = new SetVariable.Builder()
                {
                    Id = "setVariable_" + GenerateRandomID(),
                    Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
                    Value = ValueExpression.Literal(DataValue.Create(rightHandSideConstant)),
                    DisplayName = getLoggingStateName(stateId, "VM")
                }.Build();
                // Return the created instance of SetVariable
                return setVariable;
            }
        }
    


    /*public static SetVariable SetVariableToConstantNumber(string leftHandSideVariablePath, string rightHandSideConstant)
    {
        //make sure rightHandSideConstant is a numeric value
        if (!IsNumericValue(rightHandSideConstant))
        {
            return SetVariableToConstantString(leftHandSideVariablePath, rightHandSideConstant);
        }
        SetVariable setVariable = new SetVariable.Builder()
        {
            Id = "setVariable_" + GenerateRandomID(),
            Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
            Value = new NumberDataValue(NumericValue(rightHandSideConstant))
        }.Build();
        return setVariable;
    }
*/
    /// <summary>
    /// Sets the value to the PowerFX Blank() function. This is equivalent to null. Subsequent operations on this variable will result in an error.
    /// </summary>
    /// <param name="leftHandSideVariablePath"></param>
    /// <returns></returns>
    public static SetVariable SetVariableToBlank(string leftHandSideVariablePath)
    {
        SetVariable setVariable = new SetVariable.Builder()
        {
            Id = "setVariable_" + GenerateRandomID(),
            Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
            Value = ValueExpression.Expression("Blank()"),
            DisplayName = getLoggingStateName(leftHandSideVariablePath, "VM")
        }.Build();
        return setVariable;
    }

    /// <summary>
    /// Sets the value to {}. An existing record object will be emptied but not null (not Blank). You can set fields of a record afterward.
    /// </summary>
    /// <param name="leftHandSideVariablePath"></param>
    /// <returns></returns>
    public static SetVariable SetVariableToEmptyObject(string leftHandSideVariablePath)
    {
        SetVariable setVariable = new SetVariable.Builder()
        {
            Id = "setVariable_" + GenerateRandomID(),
            Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
            Value = ValueExpression.Expression("{}"),
            DisplayName = getLoggingStateName(leftHandSideVariablePath, "VM")
        }.Build();
        return setVariable;
    }
    public static MessageActivityTemplate GetSingleLineActivityWithTextAndSpeech(string textExpression, string speechExpression)
    {
        return new MessageActivityTemplate.Builder()
        {
            Text =
                {
                    new TemplateLine.Builder()
                    {
                        Segments =
                        {
                            new ExpressionSegment(ValueExpression.Expression(textExpression))
                        }
                    }
                },
            Speak =
                {
                    new TemplateLine.Builder()
                    {
                        Segments =
                        {
                            new ExpressionSegment(ValueExpression.Expression(speechExpression))
                        }
                    }
                }
        }.Build();
    }
    public static ParseValue ParseValueFromJSON(string leftHandSideVariablePath, string json)
    {

        DataValue dataValue = JsonSerializer.Deserialize<DataValue>(json, ElementSerializer.CreateOptions());
        DataType dataType = dataValue.GetDataType();
        ParseValue parseValue = new ParseValue.Builder()
        {
            Id = "parseValue_" + GenerateRandomID(),
            Variable = InitializablePropertyPath.Create(leftHandSideVariablePath),
            ValueType = dataType,
            Value = new StringDataValue(json),
            DisplayName = leftHandSideVariablePath + "_VM"
        }.Build();
        return parseValue;
    }

    public static DataType GetDataTypeFromJSON(string json)
    {
        DataValue dataValue = JsonSerializer.Deserialize<DataValue>(json, ElementSerializer.CreateOptions());
        return dataValue.GetDataType();
    }
    public static RecordDataType GetRecordDataTypeFromJSON(string json)
    {
        try
        {
            RecordDataValue dataValue = JsonSerializer.Deserialize<RecordDataValue>(json, ElementSerializer.CreateOptions());
            return (RecordDataType)dataValue.GetDataType();
        }
        catch
        {
            Console.WriteLine("Error: JSON is not a record type: " + json);
            return RecordDataType.EmptyRecord;
        }
    }
    private static bool IsNumericValue(string value)
    {
        decimal result;
        return decimal.TryParse(value, out result);
    }
    private static decimal NumericValue(string value)
    {
        if (decimal.TryParse(value, out decimal result))
        {
            return result;
        }
        else
        {
            return 0;
        }
    }
        public static string RemoveCharacters(string input)
        {
            // Use LINQ to filter out only digits
            return new string(input.Where(char.IsDigit).ToArray());
        }

        public static string RemoveSurroundingQuotes(string rightHandSideConstant)
        {
            // Check if the string is wrapped in single or double quotes
            if (!string.IsNullOrEmpty(rightHandSideConstant) &&
                ((rightHandSideConstant.StartsWith("'") && rightHandSideConstant.EndsWith("'")) ||
                (rightHandSideConstant.StartsWith("\"") && rightHandSideConstant.EndsWith("\""))))
            {
                // Remove the surrounding quotes
                rightHandSideConstant = rightHandSideConstant.Substring(1, rightHandSideConstant.Length - 2);
            }

            return rightHandSideConstant;
        }

        public static string getLoggingStateName(string id , string suffix)
        {
            if (id.StartsWith("Global.GlobalVars"))
            {
                id = id.Replace("Global.GlobalVars.", ""); 
            }

            if (id.StartsWith("Global."))
            {
                id = id.Replace("Global.", "");   
            }
            if (string.IsNullOrEmpty(id))
                return id;

            // List of suffixes to remove
            string[] suffixes = { "_SD", "_DM", "_DS", "_PP", "_DA", "_DB" };

            if (string.IsNullOrEmpty(id))
                return id;

            // Pattern matches only the suffix at the end, not digits or underscores after
            string pattern = "(_SD|_DM|_DS|_PP|_DA|_DB)$";
            //removed below line to resolve duplicate action id issue statename_LG would become statename_DA/DS/MS_LG
           // id = Regex.Replace(id, pattern, "", RegexOptions.IgnoreCase);
            return id+"_"+suffix;
        }           
    }
}
