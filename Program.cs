﻿using NDFToCopilotStudioConverter;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System.Text.RegularExpressions;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddEndpointsApiExplorer();

// Load feature flags from configuration
var configuration = builder.Configuration;
bool isPowerFxQA = configuration.GetValue<bool>("FeatureFlags:isPowerFxQA");
bool isPlaceholderDA = configuration.GetValue<bool>("FeatureFlags:isPlaceholderDA");

// Register the flags as singleton services
builder.Services.AddSingleton(new FeatureFlagOptions
{
    IsPowerFxQA = configuration.GetValue<bool>("FeatureFlags:isPowerFxQA"),
    IsPlaceholderDA = configuration.GetValue<bool>("FeatureFlags:isPlaceholderDA")
});

var app = builder.Build();

app.UseHttpsRedirection();

app.MapPost("/getyamloutput", async (HttpContext httpContext) =>
    {
        try
        {
            // Read the body content as a string
        using var reader = new StreamReader(httpContext.Request.Body);
        var bodyContent = await reader.ReadToEndAsync();

            // EntryClass entryClass = new EntryClass();
            //String yamlContent = entryClass.Entry(bodyContent); // Pass the body content to the Entry method

            // Call the processXml method
            // string yamlContent = XmlProcessingClass.processXml(bodyContent);
            string yamlContent = YamlSuffixReplacer.ReplaceYamlSuffix(XmlProcessingClass.processXml(bodyContent));

            string outputFileName  = "Output.yml";
            string tempDirectory = Path.GetTempPath();
            string tempFilePath = Path.Combine(tempDirectory, outputFileName);
            // Write the response to the file
            await File.WriteAllTextAsync(tempFilePath, yamlContent);
            Console.WriteLine($"File written to: {tempFilePath}");

            // Replace ToLowerCase() with ToLower() in the output file
            if (yamlContent != null)
            {
                
                yamlContent = yamlContent.Replace(".toLowerCase()", "");
                yamlContent = yamlContent.Replace(".toLowerCase", "");
                yamlContent = yamlContent.Replace(".toUpperCase()", "");
                yamlContent = yamlContent.Replace(".toUpperCase", "");
                yamlContent = yamlContent.Replace("Global.undefined", "undefined");
                yamlContent = yamlContent.Replace("Global.null", "null");
                yamlContent = yamlContent.Replace("(GlobalVars.!IsBlank(", "Not(IsBlank(GlobalVars.");
               // yamlContent = yamlContent.Replace("\"true)\"", "true)");
                //yamlContent = yamlContent.Replace("\"false)\"", "false)");
                // Add this line to replace \" with ""
                yamlContent = yamlContent.Replace("\\\"", "");
                yamlContent = Regex.Replace(yamlContent, @"\\\w+", "");
                yamlContent = yamlContent.Replace("\\","");
                yamlContent = yamlContent.Replace("/", "");

            }
            //Uncomment to generate file locally
            File.WriteAllText(outputFileName, yamlContent);
            
            // Check if the file exists before returning it
            if (File.Exists(tempFilePath))
            {
                return Results.File(tempFilePath, "application/x-yaml", outputFileName);
            }
            else
            {
                Console.WriteLine("File was not found after writing.");
                return Results.Problem("File not found after creation.");
            }
        }
        catch (Exception ex)
        {
          Console.WriteLine($"Error: {ex.Message}");
        return Results.Problem("An error occurred while processing the request.");
        }
    })
    .WithOpenApi();

app.Run();

// Create a simple options class (add this to your project)
public class FeatureFlagOptions
{
    public bool IsPowerFxQA { get; set; }
    public bool IsPlaceholderDA { get; set; }
}