﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class DataAccessModel : StateModel
    {
        public string baseUrl = "http://currencyconverter-h0are5cgf4dwg9e8.eastus-01.azurewebsites.net/currency/";
        public List<string> InputVariableList { get; set; }
        public List<DAOutputVariable> OutputVariableList { get; set; }
        public List<SessionMappingModel> sessionMappings { get; set; }
        //public List<TransitionModel> ActionList { get; set; }

        public List<List<ConditionModel>> ConditionTransitionsList { get; set; }
        public string dataaccessId { get; set; }

        public DataAccessModel()
        {
            InputVariableList = new List<string>();
            OutputVariableList = new List<DAOutputVariable>();
            sessionMappings = new List<SessionMappingModel>();
            //ActionList = new List<TransitionModel>();
            ConditionTransitionsList = new List<List<ConditionModel>>();
        }

        public override void DisplayDetails()
        {
            Console.WriteLine($"DataAccessModel ID: {Id}");

            Console.WriteLine("Input Variables:");
            foreach (var input in InputVariableList)
            {
                Console.WriteLine($"- {input}");
            }

            Console.WriteLine("Output Variables:");
            foreach (var output in OutputVariableList)
            {
                Console.WriteLine($"- {output.Value}");
            }

            Console.WriteLine("Conditions and Transitions:");
            for (int i = 0; i < ConditionTransitionsList.Count; i++)
            {
                Console.WriteLine($"-- If Block {i + 1} --");
                foreach (var conditionModel in ConditionTransitionsList[i])
                {
                    DisplayConditionDetails(conditionModel, 1); // Start with level 1 indentation
                }
            }

        }
        private void DisplayConditionDetails(ConditionModel conditionModel, int level)
        {
            var indent = new string(' ', level * 2); // Indentation for nested levels

            Console.WriteLine($"{indent}Condition: {conditionModel.Condition}"); // Raw condition string

            // Ensure Transitions is not null and process each transition
            var transition = conditionModel.Transitions;
            if (transition != null)
            {
                Console.WriteLine($"{indent}- Label: {transition.label}, Next: {transition.next}");

                if (transition.sessionMappings != null && transition.sessionMappings.Count > 0)
                {
                    Console.WriteLine($"{indent}  Session Mappings:");
                    foreach (var mapping in transition.sessionMappings)
                    {
                        Console.WriteLine($"{indent}  - {mapping.key}: {mapping.value}: {mapping.type}");
                    }
                }

                if (transition.InnerConditionTransitions != null && transition.InnerConditionTransitions.Count > 0)
                {
                    Console.WriteLine($"{indent}  Inner Conditions:");
                    foreach (var innerConditionModel in transition.InnerConditionTransitions)
                    {
                        DisplayConditionDetails(innerConditionModel, level + 1); // Recurse for inner conditions
                    }
                }
            }
        }
    }
}
