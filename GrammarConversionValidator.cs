using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.Bot.ObjectModel;

namespace NDFToCopilotStudioConverter
{
    /// <summary>
    /// Validation utility for grammar conversion logic
    /// Provides methods to validate and test the grammar-to-entity conversion process
    /// </summary>
    public static class GrammarConversionValidator
    {
        /// <summary>
        /// Validates the grammar conversion logic with sample data
        /// </summary>
        /// <param name="dialogProject">The dialog project to validate</param>
        /// <returns>Validation results</returns>
        public static ValidationResult ValidateGrammarConversion(DialogList dialogProject)
        {
            var result = new ValidationResult();
            
            try
            {
                // Test grammar mapping generation
                var grammarToEntityMap = ExternalGrammarUtil.GetGrammarNameToEntityMap(dialogProject);
                result.GrammarMappingCount = grammarToEntityMap.Count;
                result.GrammarMappings = grammarToEntityMap;

                // Test grammar mapping from JSON
                var jsonGrammarMap = ExternalGrammarUtil.GetGrammarNameToEntityMapFromJson("grammar_entities.json");
                result.JsonGrammarMappingCount = jsonGrammarMap.Count;

                // Validate each DM state
                if (dialogProject?.dialogModels != null)
                {
                    foreach (var dialogModel in dialogProject.dialogModels)
                    {
                        if (dialogModel?.States != null)
                        {
                            foreach (var state in dialogModel.States)
                            {
                                if (state is DmStateModel dmState)
                                {
                                    ValidateDmState(dmState, grammarToEntityMap, result);
                                }
                            }
                        }
                    }
                }

                result.IsValid = result.Errors.Count == 0;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Validation failed with exception: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// Validates a single DM state for grammar conversion
        /// </summary>
        /// <param name="dmState">The DM state to validate</param>
        /// <param name="grammarToEntityMap">The grammar-to-entity mapping</param>
        /// <param name="result">The validation result to update</param>
        private static void ValidateDmState(DmStateModel dmState, Dictionary<string, string> grammarToEntityMap, ValidationResult result)
        {
            try
            {
                result.ProcessedStates++;

                // Check if state has grammar configuration
                bool hasGrammarConfig = ExternalGrammarUtil.HasGrammarConfiguration(dmState);
                if (hasGrammarConfig)
                {
                    result.StatesWithGrammar++;

                    // Get all grammar names
                    var grammarNames = ExternalGrammarUtil.GetAllGrammarNames(dmState);
                    result.TotalGrammarReferences += grammarNames.Count;

                    // Check if grammar-based resolution should be used
                    bool shouldUseGrammar = ExternalGrammarUtil.ShouldUseGrammarBasedResolution(dmState, grammarToEntityMap);
                    if (shouldUseGrammar)
                    {
                        result.StatesUsingGrammarResolution++;

                        // Test entity creation
                        string entityName = dmState.Id;
                        var entity = ExternalGrammarUtil.GetEntityWithGrammarPriority(dmState, entityName, grammarToEntityMap);
                        
                        if (entity != null)
                        {
                            result.SuccessfulEntityCreations++;
                            result.EntityDetails.Add(new EntityCreationDetail
                            {
                                StateId = dmState.Id,
                                EntityName = entityName,
                                EntityType = entity.GetType().Name,
                                GrammarNames = grammarNames,
                                UsedGrammarResolution = true
                            });
                        }
                        else
                        {
                            result.Errors.Add($"Failed to create entity for state {dmState.Id} despite having grammar configuration");
                        }
                    }
                    else
                    {
                        result.Warnings.Add($"State {dmState.Id} has grammar configuration but no matching grammar mappings");
                    }
                }
                else
                {
                    // Test standard entity creation
                    string entityName = dmState.Id;
                    var entity = ExternalGrammarUtil.GetEntityWithGrammarPriority(dmState, entityName, grammarToEntityMap);
                    
                    if (entity != null)
                    {
                        result.SuccessfulEntityCreations++;
                        result.EntityDetails.Add(new EntityCreationDetail
                        {
                            StateId = dmState.Id,
                            EntityName = entityName,
                            EntityType = entity.GetType().Name,
                            GrammarNames = new List<string>(),
                            UsedGrammarResolution = false
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Error validating state {dmState.Id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Prints a detailed validation report
        /// </summary>
        /// <param name="result">The validation result</param>
        public static void PrintValidationReport(ValidationResult result)
        {
            Console.WriteLine("=== Grammar Conversion Validation Report ===");
            Console.WriteLine($"Overall Status: {(result.IsValid ? "VALID" : "INVALID")}");
            Console.WriteLine($"Processed States: {result.ProcessedStates}");
            Console.WriteLine($"States with Grammar Config: {result.StatesWithGrammar}");
            Console.WriteLine($"States Using Grammar Resolution: {result.StatesUsingGrammarResolution}");
            Console.WriteLine($"Successful Entity Creations: {result.SuccessfulEntityCreations}");
            Console.WriteLine($"Grammar Mappings Found: {result.GrammarMappingCount}");
            Console.WriteLine($"JSON Grammar Mappings: {result.JsonGrammarMappingCount}");
            Console.WriteLine($"Total Grammar References: {result.TotalGrammarReferences}");

            if (result.Errors.Count > 0)
            {
                Console.WriteLine("\n=== ERRORS ===");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"ERROR: {error}");
                }
            }

            if (result.Warnings.Count > 0)
            {
                Console.WriteLine("\n=== WARNINGS ===");
                foreach (var warning in result.Warnings)
                {
                    Console.WriteLine($"WARNING: {warning}");
                }
            }

            if (result.EntityDetails.Count > 0)
            {
                Console.WriteLine("\n=== ENTITY CREATION DETAILS ===");
                foreach (var detail in result.EntityDetails.Take(10)) // Show first 10
                {
                    Console.WriteLine($"State: {detail.StateId}");
                    Console.WriteLine($"  Entity: {detail.EntityName} ({detail.EntityType})");
                    Console.WriteLine($"  Grammar Resolution: {detail.UsedGrammarResolution}");
                    if (detail.GrammarNames.Count > 0)
                    {
                        Console.WriteLine($"  Grammar Names: {string.Join(", ", detail.GrammarNames)}");
                    }
                    Console.WriteLine();
                }
                
                if (result.EntityDetails.Count > 10)
                {
                    Console.WriteLine($"... and {result.EntityDetails.Count - 10} more entities");
                }
            }

            Console.WriteLine("=== End Report ===");
        }
    }

    /// <summary>
    /// Validation result data structure
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public int ProcessedStates { get; set; }
        public int StatesWithGrammar { get; set; }
        public int StatesUsingGrammarResolution { get; set; }
        public int SuccessfulEntityCreations { get; set; }
        public int GrammarMappingCount { get; set; }
        public int JsonGrammarMappingCount { get; set; }
        public int TotalGrammarReferences { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public Dictionary<string, string> GrammarMappings { get; set; } = new Dictionary<string, string>();
        public List<EntityCreationDetail> EntityDetails { get; set; } = new List<EntityCreationDetail>();
    }

    /// <summary>
    /// Details about entity creation for validation
    /// </summary>
    public class EntityCreationDetail
    {
        public string StateId { get; set; }
        public string EntityName { get; set; }
        public string EntityType { get; set; }
        public List<string> GrammarNames { get; set; } = new List<string>();
        public bool UsedGrammarResolution { get; set; }
    }
}
