﻿using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class DialogList
    {
        public List<DialogModel> dialogModels { get; set; }
        public List<CustomStateModel> customStates { get; set; }

        public DialogList()
        {
            dialogModels = new List<DialogModel>();
            customStates = new List<CustomStateModel>();
        }

        public void DisplayDetails()
        {
            foreach (var dialogModel in dialogModels)
            {
                dialogModel.DisplayDetails();
            }
            Console.WriteLine();
        }
    }
}
