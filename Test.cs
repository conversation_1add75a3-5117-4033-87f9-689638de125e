﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;

namespace XmlProcessing
{
    // Model classes
    public class PlayState
    {
        public string Id { get; set; }
        public Prompt Prompt { get; set; }
        public ActionRoot Action { get; set; }
    }

    public class Prompt
    {
        public string Id { get; set; }
        public List<PromptSegment> PromptSegments { get; set; }
    }

    public class PromptSegment
    {
        public string Src { get; set; }
        public string Text { get; set; }
    }

    public class ActionRoot
    {
        public string Label { get; set; }
        public string Next { get; set; }
        public ConditionNode Condition { get; set; }
    }

    public class ConditionNode
    {
        public string Condition { get; set; }
        public ActionRoot Action { get; set; }
        public List<ConditionNode> ElseIfConditions { get; set; } = new List<ConditionNode>();
        public ActionRoot ElseAction { get; set; }
    }
}