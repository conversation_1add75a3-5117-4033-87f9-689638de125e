﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using YamlDotNet.Core.Tokens;

namespace NDFToCopilotStudioConverter
{
    public static class DataAccessStateService
    {
        public static DataAccessModel ProcessState(XElement dataAccessStateElement)
        {
            var dataAccessModel = new DataAccessModel
            {
                Id = dataAccessStateElement.Attribute("id")?.Value,
                ConditionTransitionsList = new List<List<ConditionModel>>(), // List of condition lists
                sessionMappings = new List<SessionMappingModel>()
            };

            var dataAccessElement = dataAccessStateElement.Element("data-access");
            if (dataAccessElement != null)
            {
                // Process class name and inputs/outputs
                dataAccessModel.Type = dataAccessElement.Attribute("classname")?.Value;
                dataAccessModel.dataaccessId = dataAccessElement.Attribute("id")?.Value;

                // Process inputs and outputs of the current data-access element
                ParseInputsAndOutputs(dataAccessElement, dataAccessModel);
            }

            // Process session mappings
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(dataAccessStateElement.Elements("session-mapping"));
            dataAccessModel.sessionMappings.AddRange(sessionMappings);
            var varMappings = SessionMappingHelper.ProcessSessionMappings(dataAccessStateElement.Elements("var"));
            dataAccessModel.sessionMappings.AddRange(varMappings);

            // Process actions
            foreach (var actionElement in dataAccessStateElement.Elements("action"))
            {
                var innerActionElements = actionElement.Elements("action");
                var defaultTransition = new TransitionModel
                {
                    sessionMappings = new List<SessionMappingModel>(),
                    InnerConditionTransitions = new List<ConditionModel>() // Ensure it's empty for flat conditions
                };
                if (innerActionElements != null)
                { 
                    foreach (var innerActionElement in actionElement.Elements("action"))
                    {
                       
                        defaultTransition.label = innerActionElement.Attribute("label")?.Value ?? "fixedtransition";
                        defaultTransition.next = innerActionElement.Attribute("next")?.Value;
                    }
                }


                var ifElements = actionElement.Elements("if");
                if (ifElements != null)
                {
                    foreach (var ifElement in ifElements)
                    {
                        var conditionList = new List<ConditionModel>(); // List to store conditions for this 'if' block
                        ProcessTopIfCondition(ifElement, conditionList);
                        
                        dataAccessModel.ConditionTransitionsList.Add(conditionList); // Add this list to the model
                    }
                }
                // If no <if> elements are present, process the default action
                if (!ifElements.Any())
                {
                    var conditionList = new List<ConditionModel>();
                    var conditionalModel = new ConditionModel();
                    conditionalModel.Condition = "default";
                    conditionalModel.Transitions = ProcessActionElement(actionElement);
                    conditionList.Add(conditionalModel);
                    dataAccessModel.ConditionTransitionsList.Add(conditionList);
                }

                if (defaultTransition.next != null)
                {
                    foreach (List<ConditionModel> cndModelList in dataAccessModel.ConditionTransitionsList)
                    {
                        foreach (ConditionModel cndModel in cndModelList)
                        {
                            if (cndModel.Transitions.next == null)
                            {
                               cndModel.Transitions.next = defaultTransition.next;
                               cndModel.Transitions.label = defaultTransition.label;
                            }

                        }
                    }
                }
              
              }
            return dataAccessModel;
        }

        public static void ParseInputsAndOutputs(XElement dataAccessElement, DataAccessModel dataAccessModel)
        {
            // Process inputs
            var inputsElement = dataAccessElement.Element("inputs");
            if (inputsElement != null)
            {
                foreach (var inputVar in inputsElement.Elements("input-variable"))
                {
                    var inputName = inputVar.Attribute("name")?.Value;
                    if (!string.IsNullOrEmpty(inputName))
                    {
                        dataAccessModel.InputVariableList.Add(inputName);
                    }
                }
            }

            // Process outputs
            var outputsElement = dataAccessElement.Element("outputs");
            if (outputsElement != null)
            {
                foreach (var outputVar in outputsElement.Elements("output-variable"))
                {
                    var outputNameValue = outputVar.Attribute("name")?.Value;
                    var outputNameType = outputVar.Attribute("type")?.Value;
                    if (!string.IsNullOrEmpty(outputNameValue))
                    {
                        DAOutputVariable outputVariable = new DAOutputVariable();
                        outputVariable.Value = outputNameValue;
                        if (outputNameType is null)
                        {
                            outputVariable.Type = "string";
                        }
                        else
                        {
                            outputVariable.Type = outputNameType;
                        }
                        dataAccessModel.OutputVariableList.Add(outputVariable);
                    }
                }
            }


        }

        public static TransitionModel ProcessActionElement(XElement actionElement)
        {
            var gotoElement = actionElement.Element("gotodialog");

            var transitionModel = new TransitionModel
            {
                label = actionElement.Attribute("label")?.Value,  // Add label if available
                next = actionElement.Attribute("next")?.Value
            };
            if (gotoElement != null)
            {

                //26638
                var nextAttr = gotoElement.Attribute("next")?.Value;
                if (!string.IsNullOrEmpty(nextAttr)){
                    if (nextAttr.Contains(".dvxml#")){
                        var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                        if (parts.Length == 2)
                        {
                           transitionModel.next = parts[0]+ ".dvxml";      // before .dvxml#
                            SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                key = "nextState",
                                value = parts[1]
                            };
                            transitionModel.sessionMappings.Add(sessionMappingModel);
                            //transition.sessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                        }
                    }
                    else{
                        transitionModel.next = nextAttr+ ".dvxml";
                    }
                }
                //26638
                //transitionModel.next = gotoElement.Attribute("next")?.Value + ".dvxml";
            }

            // Check for session mappings (if any)
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("session-mapping"));
            transitionModel.sessionMappings.AddRange(sessionMappings);
            var varMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("var"));
            transitionModel.sessionMappings.AddRange(varMappings);

            return transitionModel;
        }

        private static void ProcessTopIfCondition(XElement ifElement, List<ConditionModel> conditionList)
        {
            string condition = ifElement.Attribute("cond")?.Value;
            var transition = ProcessConditionElement(ifElement);

            // Create a ConditionModel for the top-level 'if'
            if(condition != null)
            {
                var topCondition = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };

                // Process nested conditions
                ProcessNestedConditions(ifElement, topCondition);

                // Add the top condition to the list
                conditionList.Add(topCondition);

            }

            // Process sibling 'elseif' and 'else' conditions
            ProcessSiblingConditions(ifElement, conditionList);
        }

        private static void ProcessNestedConditions(XElement parentElement, ConditionModel parentCondition)
        {
            // Extract nested 'if' condition from the top 'if' element
            var nestedIfElement = parentElement.Element("if");
            if (nestedIfElement != null)
            {
                // Add nested 'if' condition to the parent's InnerConditionTransitions
                string nestedCondition = nestedIfElement.Attribute("cond")?.Value;
                var nestedTransition = ProcessConditionElement(nestedIfElement);

                ConditionModel nestedConditionModel = new ConditionModel
                {
                    Condition = nestedCondition,
                    Transitions = nestedTransition
                };

                parentCondition.Transitions.InnerConditionTransitions.Add(nestedConditionModel);

                //Adding the below code for nested if condition
                ProcessNestedConditions(nestedIfElement, parentCondition);

                // Process nested 'elseif' and 'else' within the nested 'if'
                ProcessSiblingConditions(nestedIfElement, parentCondition.Transitions);
            }
        }

        private static void ProcessSiblingConditions(XElement parentElement, List<ConditionModel> conditionList)
        {
            // Process 'elseif' elements
            var elseifElements = parentElement.Elements("elseif");
            foreach (var elseifElement in elseifElements)
            {
                string condition = elseifElement.Attribute("cond")?.Value;
                var transition = ProcessConditionElement(elseifElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };
                ProcessNestedConditions(elseifElement, condObj);
                // Add to conditionList
                conditionList.Add(condObj);
            }

            // Process 'else' element
            var elseElement = parentElement.Element("else");
            if (elseElement != null)
            {
                string condition = "else";
                var transition = ProcessConditionElement(elseElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };
                ProcessNestedConditions(elseElement, condObj);
                // Add to conditionList
                conditionList.Add(condObj);
            }
        }

        private static void ProcessSiblingConditions(XElement parentElement, TransitionModel parentTransition)
        {
            // Process 'elseif' elements
            var elseifElements = parentElement.Elements("elseif");
            foreach (var elseifElement in elseifElements)
            {
                string condition = elseifElement.Attribute("cond")?.Value;
                var transition = ProcessConditionElement(elseifElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };

                // Add to InnerConditionTransitions
                parentTransition.InnerConditionTransitions.Add(condObj);
            }

            // Process 'else' element
            var elseElement = parentElement.Element("else");
            if (elseElement != null)
            {
                string condition = "else";
                var transition = ProcessConditionElement(elseElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };

                //Adding the below code for transition in else
                ProcessNestedConditions(elseElement, condObj);

                // Add to InnerConditionTransitions
                parentTransition.InnerConditionTransitions.Add(condObj);
            }
        }

        private static TransitionModel ProcessConditionElement(XElement element)
        {
            var transition = new TransitionModel
            {
                sessionMappings = new List<SessionMappingModel>(),
                InnerConditionTransitions = new List<ConditionModel>() // Ensure it's empty for flat conditions
            };

            // Use SessionMappingHelper to process session-mapping elements directly under the condition element
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(element.Elements("session-mapping"));
            transition.sessionMappings.AddRange(sessionMappings);
            var varMappings = SessionMappingHelper.ProcessSessionMappings(element.Elements("var"));
            transition.sessionMappings.AddRange(varMappings);

            // Process action element if present
            var actionElement = element.Element("action");
            //var gotoElement = element.Element("NextNode");
            //Changed gotoElement as It is a XNode not a Xname same we have to check for actionElement
            var gotoElement = element.NextNode as XElement;
            if(gotoElement==null){
                if(element.FirstNode !=null)
                gotoElement = element.FirstNode.NextNode as XElement;
            }
            if(gotoElement==null){
                gotoElement = element.LastNode as XElement;
            }
            if (actionElement != null)
            {
                transition.label = actionElement.Attribute("label")?.Value ?? "fixedtransition";
                transition.next = actionElement.Attribute("next")?.Value;


                // Process session-mapping elements inside the action
                var actionSessionMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("session-mapping"));
                transition.sessionMappings.AddRange(actionSessionMappings);
                var actionVarMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("var"));
                transition.sessionMappings.AddRange(actionVarMappings);
            }
            if (gotoElement != null)
            {
                //26638
                var nextAttr = gotoElement.Attribute("next")?.Value;
                if (!string.IsNullOrEmpty(nextAttr) && nextAttr.Contains(".dvxml")){
                    
                    if (nextAttr.Contains(".dvxml#")){
                        var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                        if (parts.Length == 2)
                        {
                           transition.next = parts[0]+ ".dvxml";      // before .dvxml#
                            SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                key = "nextState",
                                value = parts[1]
                            };
                            transition.sessionMappings.Add(sessionMappingModel);
                            //transition.sessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                        }
                    }
                    else{
                        transition.next = nextAttr+ ".dvxml";
                    }
                }

                //26638
                //transition.next = gotoElement.Attribute("next")?.Value + ".dvxml";
            }

            return transition;
        }
    }
}
