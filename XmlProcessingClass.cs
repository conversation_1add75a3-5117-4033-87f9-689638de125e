﻿
using System.Text.RegularExpressions;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public class XmlProcessingClass
    {
        public static string processXml(string xmlContent)
        {
            // Load the XML content from the provided string
            XElement xml = XElement.Parse(xmlContent);

            UpdatePromptsWithIfCondition(xml);

            // Process grammar configurations
            ProcessGrammarConfigurations(xml);
/*
            string outputFilePath = "modified_audio.xml";  // Specify your desired file path
            xml.Save(outputFilePath);*/

            // Remove existing declareVariables dialogs
            var existingDialogs = xml.Descendants("dialog")
                .Where(d => d.Attribute("id")?.Value.StartsWith("declareVariables") == true)
                .ToList();
            foreach (var dialog in existingDialogs)
            {
                dialog.Remove();
            }

            // Extract session-mapping keys and var names
            HashSet<string> declaredValues = new HashSet<string>();

            // Extract session-mapping keys
            var sessionMappings = xml.Descendants("session-mapping")
                .Select(sm => StateUtility.SimplifyCondition(sm.Attribute("key")?.Value))
                .Where(key => !string.IsNullOrEmpty(key));

            // Extract var names
            var varNames = xml.Descendants("var")
                .Select(v => StateUtility.SimplifyCondition(v.Attribute("name")?.Value))
                .Where(name => !string.IsNullOrEmpty(name));

            // Extract and handle conditions from <if> elements
            var condVariables = xml.Descendants().Where(x => x.Name == "if" || x.Name == "elseif")
                .SelectMany(cond => ExtractLHSAndRHSVariables(StateUtility.SimplifyCondition(cond.Attribute("cond")?.Value)))
                .Where(lhs => !string.IsNullOrEmpty(lhs.LHS)).ToList();

            for (int i = 0; i < condVariables.Count; i++)
            {
                var item = condVariables[i]; // Get current tuple
                char[] charsToTrim = new char[] { ' ', '!' };

                // Check if LHS contains "!"
                if (item.LHS.Contains("!") || item.LHS.StartsWith(" "))
                {
                    // Create a new tuple with modified LHS
                    condVariables[i] = (item.LHS.TrimStart(charsToTrim), item.RHS, item.Type);
                }
            }
            var inputVariables = xml.Descendants("inputs")
                .SelectMany(inputs => inputs.Descendants("input-variable")
                    .Select(input => input.Attribute("name")?.Value))
                .Where(name => !string.IsNullOrEmpty(name));

            /*var outputVariables = xml.Descendants("outputs")
                .SelectMany(outputs => outputs.Descendants("output-variable")
                    .Select(output => output.Attribute("name")?.Value))
                .Where(name => !string.IsNullOrEmpty(name));*/
            var outputVariables = xml.Descendants("outputs")
    .SelectMany(outputs => outputs.Descendants("output-variable")
        .Select(output => new
        {
            Name = output.Attribute("name")?.Value,
            Type = output.Attribute("type")?.Value
        }))
    .Where(o => !string.IsNullOrEmpty(o.Name))
    .ToList();

            // Collect variables from conditions
            var conditionVariables = xml.Descendants("condition")
                .Select(condition => new
                {
                    Name = condition.Descendants("param")
                        .FirstOrDefault(p => p.Attribute("scope")?.Value == "session")?.Attribute("value")?.Value,
                    ClassName = condition.Attribute("classname")?.Value,
                    CompareValue = condition.Descendants("param")
                        .FirstOrDefault(p => p.Attribute("name")?.Value == "compare")?.Attribute("value")?.Value
                })
                .Where(c => !string.IsNullOrEmpty(c.Name))
                .Select(c => new
                {
                    c.Name,
                    Type = DetermineTypeFromCondition(c.CompareValue)
                });

            // Combine all extracted values into a single collection
            var declaredValue = sessionMappings
                .Union(varNames)
                .Union(condVariables.Select(c => c.LHS))
                .Union(inputVariables)
                .Union(outputVariables.Select(o => o.Name))
                .Union(conditionVariables.Select(c => c.Name))
                .Distinct()
                .ToList();

            // Prepare the output XML structure
            XElement outputXml = new XElement("variables");

            // Split session mappings into chunks of 220
            var dialogChunks = declaredValue.Select((value, index) => new { value, index })
                                              .GroupBy(x => x.index / 220)
                                              .Select(g => g.Select(x => x.value).ToList())
                                              .ToList();
            int flag = 0;
            // Iterate through each chunk and create dialogs
            //Doing dialogChunks.Count+1 so that after variable declaration i can create BackendInitialization custom state 
            for (int i = 0; i <= dialogChunks.Count; i++)
            {
                string dialogId = $"declareVariables_{i}";
                XElement customState = new XElement("custom-state", new XAttribute("id", "set_all_variables"));
                XElement dialogElement;

                //code for ConversationStart
                if(flag ==0){
                    dialogElement = new XElement("dialog", new XAttribute("id", "ConversationStart"));
                    XElement customState1 = new XElement("custom-state", new XAttribute("id", "Conversation Start"));
                    XElement gotoDialogElement = new XElement("gotodialog");
                    gotoDialogElement.SetAttributeValue("next", "ApplicationRoot");
                    customState1.Add(gotoDialogElement);
                    dialogElement.Add(customState1);
                    xml.AddFirst(dialogElement);
                    flag =1;
                }

                //code for BackendInitialization
                //XElement dialogElement1 = new XElement("dialog", new XAttribute("id", "BackendInitialization"));
                if(i==dialogChunks.Count){
                    dialogElement = new XElement("dialog", new XAttribute("id", "BackendInitialization"));
                    XElement labelSessionMappingElement = new XElement("session-mapping");
                    labelSessionMappingElement.SetAttributeValue("key", "label");
                    labelSessionMappingElement.SetAttributeValue("value", "ar");
                    labelSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(labelSessionMappingElement);

                    XElement nextStateSessionMappingElement = new XElement("session-mapping");
                    nextStateSessionMappingElement.SetAttributeValue("key", "nextState");
                    nextStateSessionMappingElement.SetAttributeValue("value", "ar");
                    nextStateSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(nextStateSessionMappingElement);

                    XElement SessionIdMappingElement = new XElement("session-mapping");
                    SessionIdMappingElement.SetAttributeValue("key", "backendSessionId");
                    SessionIdMappingElement.SetAttributeValue("value", "ar");
                    SessionIdMappingElement.SetAttributeValue("type", "String");
                    customState.Add(SessionIdMappingElement);

                    XElement InitVarsMappingElement = new XElement("session-mapping");
                    InitVarsMappingElement.SetAttributeValue("key", "initVars");
                    InitVarsMappingElement.SetAttributeValue("value", "ar");
                    InitVarsMappingElement.SetAttributeValue("type", "String");
                    customState.Add(InitVarsMappingElement);

                    XElement passwordElement = new XElement("session-mapping");
                    passwordElement.SetAttributeValue("key", "password");
                    passwordElement.SetAttributeValue("value", "admin123");
                    passwordElement.SetAttributeValue("type", "String");
                    customState.Add(passwordElement);

                    XElement usernameElement = new XElement("session-mapping");
                    usernameElement.SetAttributeValue("key", "username");
                    usernameElement.SetAttributeValue("value", "admin");
                    usernameElement.SetAttributeValue("type", "String");
                    customState.Add(usernameElement);

                    /*https://varuntestingstorage.blob.core.windows.net/%7BProjectname%7D/%7Blanguage%7D/promtps/default/IVRchannel */


                    XElement AudioBasePartOneUrlSessionMappingElement = new XElement("session-mapping");
                    AudioBasePartOneUrlSessionMappingElement.SetAttributeValue("key", "AudiobaseUrlStart");
                    AudioBasePartOneUrlSessionMappingElement.SetAttributeValue("value", "https://varuntestingstorage.blob.core.windows.net/");
                    AudioBasePartOneUrlSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(AudioBasePartOneUrlSessionMappingElement);

                    XElement AudioBaseLastPartUrlSessionMappingElement = new XElement("session-mapping");
                    AudioBaseLastPartUrlSessionMappingElement.SetAttributeValue("key", "AudiobaseUrlEnd");
                    AudioBaseLastPartUrlSessionMappingElement.SetAttributeValue("value", "/prompts/default/IVRchannel");
                    AudioBaseLastPartUrlSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(AudioBaseLastPartUrlSessionMappingElement);

                    XElement projectNameSessionMappingElement = new XElement("session-mapping");
                    projectNameSessionMappingElement.SetAttributeValue("key", "projectName");
                    projectNameSessionMappingElement.SetAttributeValue("value", "change_project_name");
                    projectNameSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(projectNameSessionMappingElement);

                    XElement AudioLanguageSessionMappingElement = new XElement("session-mapping");
                    AudioLanguageSessionMappingElement.SetAttributeValue("key", "audioLanguage");
                    AudioLanguageSessionMappingElement.SetAttributeValue("value", "change_audio_language");
                    AudioLanguageSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(AudioLanguageSessionMappingElement);

                    XElement AudioBaseUrlSessionMappingElement = new XElement("session-mapping");
                    AudioBaseUrlSessionMappingElement.SetAttributeValue("key", "audioBaseUrl");
                    AudioBaseUrlSessionMappingElement.SetAttributeValue("value", "audioBaseUrl");
                    AudioBaseUrlSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(AudioBaseUrlSessionMappingElement);

                    XElement APIBaseUrlSessionMappingElement = new XElement("session-mapping");
                    APIBaseUrlSessionMappingElement.SetAttributeValue("key", "APIbaseUrl");
                    APIBaseUrlSessionMappingElement.SetAttributeValue("value", "https://daframework-delta-gt.azurewebsites.net/");
                    APIBaseUrlSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(APIBaseUrlSessionMappingElement);

                    XElement CreateSessionUrlSessionMappingElement = new XElement("session-mapping");
                    CreateSessionUrlSessionMappingElement.SetAttributeValue("key", "sessionUrl");
                    CreateSessionUrlSessionMappingElement.SetAttributeValue("value", "https://daframeworkwest-h7aredefabatf6eh.canadacentral-01.azurewebsites.net/session/create");
                    CreateSessionUrlSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(CreateSessionUrlSessionMappingElement);

                    XElement InitSessionUrlSessionMappingElement = new XElement("session-mapping");
                    InitSessionUrlSessionMappingElement.SetAttributeValue("key", "initSessionUrl");
                    InitSessionUrlSessionMappingElement.SetAttributeValue("value", "https://daframework-delta-gt.azurewebsites.net/InitSessionData/initVars");
                    InitSessionUrlSessionMappingElement.SetAttributeValue("type", "String");
                    customState.Add(InitSessionUrlSessionMappingElement);


                    XElement CreateBackendSessionRequestMappingElement = new XElement("session-mapping");
                    CreateBackendSessionRequestMappingElement.SetAttributeValue("key", "CreateBackendSessionRequest");
                    CreateBackendSessionRequestMappingElement.SetAttributeValue("value", "createBackend");
                    CreateBackendSessionRequestMappingElement.SetAttributeValue("type", "String");
                    customState.Add(CreateBackendSessionRequestMappingElement);

                    XElement CreatInitVarsRequestMappingElement = new XElement("session-mapping");
                    CreatInitVarsRequestMappingElement.SetAttributeValue("key", "CreatInitVarsRequest");
                    CreatInitVarsRequestMappingElement.SetAttributeValue("value", "createInitVars");
                    CreatInitVarsRequestMappingElement.SetAttributeValue("type", "String");
                    customState.Add(CreatInitVarsRequestMappingElement);

                    
                    dialogElement.Add(customState);

                    XElement actionElement = new XElement("action");
                    // actionElement.SetAttributeValue("label", "default");
                   // actionElement.SetAttributeValue("next", "Initialization.dvxml");
                    //actionElement.SetAttributeValue("next", "ApplicationRoot.dvxml");
                    //customState.Add(actionElement);
                    xml.AddFirst(dialogElement);
                    continue;

                }

                foreach (var value in dialogChunks[i])
                {
                    XElement sessionMappingElement = new XElement("session-mapping");
                    sessionMappingElement.SetAttributeValue("key", value);

                    var sessionMapping = xml.Descendants("session-mapping")
                                            .FirstOrDefault(sm => sm.Attribute("key")?.Value == value);

                    string type = sessionMapping?.Attribute("type")?.Value;
                    string sessionMappingValue = sessionMapping?.Attribute("value")?.Value;

                    var condVariable = condVariables.FirstOrDefault(cv => cv.LHS == value);
                    if (condVariable != default)
                    {
                        type = condVariable.Type;
                    }

                    var conditionVariable = conditionVariables.FirstOrDefault(a => a.Name == value);
                    if (conditionVariable != default)
                    {
                        type = conditionVariable.Type;
                    }
                    if (string.IsNullOrEmpty(type))
                    {
                        var outputVariable = outputVariables.FirstOrDefault(o => o.Name == value);
                        if (outputVariable != default)
                        {
                            type = outputVariable.Type;
                        }
                    }

                    if (type == "Integer")
                    {
                        sessionMappingElement.SetAttributeValue("value", "0");
                        sessionMappingElement.SetAttributeValue("type", "Integer");
                    }
                    else if (type == "Boolean")
                    {
                        sessionMappingElement.SetAttributeValue("value", "false");
                        sessionMappingElement.SetAttributeValue("type", "Boolean");
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(sessionMappingValue))
                        {
                            if (sessionMappingValue == "true" || sessionMappingValue == "false")
                            {
                                sessionMappingElement.SetAttributeValue("value", "false");
                                sessionMappingElement.SetAttributeValue("type", "Boolean");
                            }
                            else if (int.TryParse(sessionMappingValue, out _))
                            {
                                sessionMappingElement.SetAttributeValue("value", "0");
                                sessionMappingElement.SetAttributeValue("type", "Integer");
                            }
                            else
                            {
                                sessionMappingElement.SetAttributeValue("value", "ar");
                                sessionMappingElement.SetAttributeValue("type", "String");
                            }
                        }
                        else
                        {
                            sessionMappingElement.SetAttributeValue("value", "ar");
                            sessionMappingElement.SetAttributeValue("type", "String");
                        }
                    }

                    customState.Add(sessionMappingElement);
                    outputXml.Add(sessionMappingElement);
                }

                if (i==dialogChunks.Count)
                {
                    // XElement labelSessionMappingElement = new XElement("session-mapping");
                    // labelSessionMappingElement.SetAttributeValue("key", "label");
                    // labelSessionMappingElement.SetAttributeValue("value", "ar");
                    // labelSessionMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(labelSessionMappingElement);

                    // XElement SessionIdMappingElement = new XElement("session-mapping");
                    // SessionIdMappingElement.SetAttributeValue("key", "backendSessionId");
                    // SessionIdMappingElement.SetAttributeValue("value", "ar");
                    // SessionIdMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(SessionIdMappingElement);

                    // XElement InitVarsMappingElement = new XElement("session-mapping");
                    // InitVarsMappingElement.SetAttributeValue("key", "initVars");
                    // InitVarsMappingElement.SetAttributeValue("value", "ar");
                    // InitVarsMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(InitVarsMappingElement);

                    // XElement passwordElement = new XElement("session-mapping");
                    // passwordElement.SetAttributeValue("key", "password");
                    // passwordElement.SetAttributeValue("value", "admin123");
                    // passwordElement.SetAttributeValue("type", "String");
                    // customState.Add(passwordElement);

                    // XElement usernameElement = new XElement("session-mapping");
                    // usernameElement.SetAttributeValue("key", "username");
                    // usernameElement.SetAttributeValue("value", "admin");
                    // usernameElement.SetAttributeValue("type", "String");
                    // customState.Add(usernameElement);

                    // /*https://varuntestingstorage.blob.core.windows.net/%7BProjectname%7D/%7Blanguage%7D/promtps/default/IVRchannel */


                    // XElement AudioBasePartOneUrlSessionMappingElement = new XElement("session-mapping");
                    // AudioBasePartOneUrlSessionMappingElement.SetAttributeValue("key", "AudiobaseUrlStart");
                    // AudioBasePartOneUrlSessionMappingElement.SetAttributeValue("value", "https://varuntestingstorage.blob.core.windows.net/");
                    // AudioBasePartOneUrlSessionMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(AudioBasePartOneUrlSessionMappingElement);

                    // XElement AudioBaseLastPartUrlSessionMappingElement = new XElement("session-mapping");
                    // AudioBaseLastPartUrlSessionMappingElement.SetAttributeValue("key", "AudiobaseUrlEnd");
                    // AudioBaseLastPartUrlSessionMappingElement.SetAttributeValue("value", "/prompts/default/IVRchannel");
                    // AudioBaseLastPartUrlSessionMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(AudioBaseLastPartUrlSessionMappingElement);

                    // XElement projectNameSessionMappingElement = new XElement("session-mapping");
                    // projectNameSessionMappingElement.SetAttributeValue("key", "projectName");
                    // projectNameSessionMappingElement.SetAttributeValue("value", "change_project_name");
                    // projectNameSessionMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(projectNameSessionMappingElement);

                    // XElement AudioLanguageSessionMappingElement = new XElement("session-mapping");
                    // AudioLanguageSessionMappingElement.SetAttributeValue("key", "audioLanguage");
                    // AudioLanguageSessionMappingElement.SetAttributeValue("value", "change_audio_language");
                    // AudioLanguageSessionMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(AudioLanguageSessionMappingElement);

                    // XElement AudioBaseUrlSessionMappingElement = new XElement("session-mapping");
                    // AudioBaseUrlSessionMappingElement.SetAttributeValue("key", "audioBaseUrl");
                    // AudioBaseUrlSessionMappingElement.SetAttributeValue("value", "audioBaseUrl");
                    // AudioBaseUrlSessionMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(AudioBaseUrlSessionMappingElement);

                    // XElement APIBaseUrlSessionMappingElement = new XElement("session-mapping");
                    // APIBaseUrlSessionMappingElement.SetAttributeValue("key", "APIbaseUrl");
                    // APIBaseUrlSessionMappingElement.SetAttributeValue("value", "https://daframework-delta-gt.azurewebsites.net/");
                    // APIBaseUrlSessionMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(APIBaseUrlSessionMappingElement);

                    // XElement CreateSessionUrlSessionMappingElement = new XElement("session-mapping");
                    // CreateSessionUrlSessionMappingElement.SetAttributeValue("key", "sessionUrl");
                    // CreateSessionUrlSessionMappingElement.SetAttributeValue("value", "https://daframeworkwest-h7aredefabatf6eh.canadacentral-01.azurewebsites.net/session/create");
                    // CreateSessionUrlSessionMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(CreateSessionUrlSessionMappingElement);

                    // XElement InitSessionUrlSessionMappingElement = new XElement("session-mapping");
                    // InitSessionUrlSessionMappingElement.SetAttributeValue("key", "initSessionUrl");
                    // InitSessionUrlSessionMappingElement.SetAttributeValue("value", "https://daframework-delta-gt.azurewebsites.net/InitSessionData/initVars");
                    // InitSessionUrlSessionMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(InitSessionUrlSessionMappingElement);


                    // XElement CreateBackendSessionRequestMappingElement = new XElement("session-mapping");
                    // CreateBackendSessionRequestMappingElement.SetAttributeValue("key", "CreateBackendSessionRequest");
                    // CreateBackendSessionRequestMappingElement.SetAttributeValue("value", "createBackend");
                    // CreateBackendSessionRequestMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(CreateBackendSessionRequestMappingElement);

                    // XElement CreatInitVarsRequestMappingElement = new XElement("session-mapping");
                    // CreatInitVarsRequestMappingElement.SetAttributeValue("key", "CreatInitVarsRequest");
                    // CreatInitVarsRequestMappingElement.SetAttributeValue("value", "createInitVars");
                    // CreatInitVarsRequestMappingElement.SetAttributeValue("type", "String");
                    // customState.Add(CreatInitVarsRequestMappingElement);

                    // flag = 1;
                }
                else
                {
                    //XElement clearVariableValuesElement = new XElement("session-mapping");
                    //clearVariableValuesElement.SetAttributeValue("key", "clear_variable_values");
                    //clearVariableValuesElement.SetAttributeValue("value", "clear");
                    //clearVariableValuesElement.SetAttributeValue("type", "String");
                    //customState.Add(clearVariableValuesElement);
                }



                dialogElement = new XElement("dialog", new XAttribute("id", dialogId));
                dialogElement.Add(customState);

                if (i == dialogChunks.Count - 1)
                {
                    XElement actionElement = new XElement("action");
                    // actionElement.SetAttributeValue("label", "default");
                    // actionElement.SetAttributeValue("next", "Initialization.dvxml");
                    //    actionElement.SetAttributeValue("next", "BackendInitialization.dvxml");
                    //    customState.Add(actionElement);
                    //
                }
                else if (i == dialogChunks.Count)
                {
                    //     XElement actionElement = new XElement("action");
                    //     // actionElement.SetAttributeValue("label", "default");
                    //    // actionElement.SetAttributeValue("next", "Initialization.dvxml");
                    //     actionElement.SetAttributeValue("next", "ApplicationRoot.dvxml");
                    //     customState.Add(actionElement);
                }
                else
                {
                    //XElement gotoDialogElement = new XElement("gotodialog");
                    //gotoDialogElement.SetAttributeValue("next", $"declareVariables_{i + 1}");
                    //customState.Add(gotoDialogElement);
                }

                //dialogElement.Add(dialogElement1);
                xml.AddFirst(dialogElement);
            }

            //code to create Backendinitialization custom state


            // Create a HashSet to store unique dialog IDs.
            var uniqueDialogIds = new HashSet<string>();

            // Handle custom states and add dialogs.
            var customStates = xml.Descendants("custom-state")
  .Where(cs => (cs.Attribute("id")?.Value.Contains("_SD") == true ||
                cs.Attribute("id")?.Value.Contains("_sd") == true) &&
               !(cs.Attribute("id")?.Value.Contains("_SD_return") == true 
                    || cs.Attribute("id")?.Value.Contains("_SD_Return") == true ||
                 cs.Attribute("id")?.Value.Contains("_sd_return") == true))
  .ToList();

            foreach (var custom in customStates)
            {
                string customStateId = custom.Attribute("id")?.Value;
                string dialogid = string.Empty;

                if (customStateId != null)
                {
                    // Determine the dialog ID based on the number of underscores.
                    if (customStateId.Count(c => c == '_') > 1)
                    {
                        var parts = customStateId.Split('_');
                        dialogid = parts[1];
                    }
                    else
                    {
                        dialogid = customStateId.Split('_')[0];
                    }

                    // Only add a new dialog if it hasn't been processed yet.
                    if (!uniqueDialogIds.Contains(dialogid))
                    {
                        uniqueDialogIds.Add(dialogid);

                        XElement newDialog = new XElement("dialog", new XAttribute("id", dialogid));
                        XElement returnCustomState = new XElement("custom-state", new XAttribute("id", $"return_{dialogid}"));
                        returnCustomState.Add(new XElement("action", new XAttribute("next", "end")));
                        newDialog.Add(returnCustomState);

                        xml.Add(newDialog);
                    }
                }
            }
            string finalOutput = EntryClass.Entry(xml.ToString());

            string outputFilePath = "modified_audio.xml";  // Specify your desired file path
            xml.Save(outputFilePath);
            // Return the modified XML as a string
            return finalOutput;
        }

        public static List<(string LHS, string RHS, string Type)> ExtractLHSAndRHSVariables(string condition)
        {
            var result = new List<(string LHS, string RHS, string Type)>();

            if (string.IsNullOrEmpty(condition))
                return result;

            condition = condition.Replace("&amp;&amp;", "&&").Replace("&amp;", "&");
            var conditions = condition.Split(new[] { "&&", "||", "&", "|" }, StringSplitOptions.None);

            foreach (var cond in conditions)
            {
                var cleanCondition = cond.Replace("(", "").Replace(")", "").Trim();

                // Check if the condition is a simple boolean variable check
                if (!cleanCondition.Contains("==") && !cleanCondition.Contains("!=") &&
                    !cleanCondition.Contains(">") && !cleanCondition.Contains("<"))
                {
                    // Treat it as a boolean variable being checked for true
                    result.Add((cleanCondition, "true", "Boolean"));
                }
                else
                {
                    var match = Regex.Match(cleanCondition, @"(['\""][^'\""]*['\""]|[a-zA-Z_][a-zA-Z0-9_\.]*)\s*([!=><]=?)\s*(.*)");

                    if (match.Success)
                    {
                        string lhs = match.Groups[1].Value;
                        string rhs = match.Groups[3].Value.Trim();

                        if ((lhs.StartsWith("'") && lhs.EndsWith("'")) || (lhs.StartsWith("\"") && lhs.EndsWith("\"")))
                        {
                            string temp = rhs;
                            rhs = lhs;
                            lhs = temp;
                        }

                        string type = DetermineType(rhs);
                        if(lhs== "!reentry" || rhs== "!reentry")
                        {
                            Console.WriteLine(lhs);
                        }
                        result.Add((lhs, rhs, type));
                    }
                }
            }
            // New logic to extract RHS variables that are not quoted, numeric, or boolean
            foreach (var cond in conditions)
            {
                var cleanCondition = cond.Replace("(", "").Replace(")", "").Trim();
                var match = Regex.Match(cleanCondition, @"(['\""][^'\""]*['\""]|[a-zA-Z_][a-zA-Z0-9_\.]*)\s*([!=><]=?)\s*(.*)");

                if (match.Success)
                {
                    string rhs = match.Groups[3].Value.Trim();
                    if (!rhs.StartsWith("'") && !rhs.StartsWith("\"") && !IsNumeric(rhs) &&
                        !rhs.Equals("true", StringComparison.OrdinalIgnoreCase) &&
                        !rhs.Equals("false", StringComparison.OrdinalIgnoreCase))
                    {
                        // Add to result with RHS as "default"
                        result.Add((rhs, "default", "String"));
                    }
                }
            }
            return result;
        }

        public static string DetermineType(string operand)
        {
            operand = operand.Trim().Trim('\'', '"');
            if (operand.Equals("true", StringComparison.OrdinalIgnoreCase) || operand.Equals("false", StringComparison.OrdinalIgnoreCase))
            {
                return "Boolean";
            }

            if (IsNumeric(operand))
            {
                return "Integer";
            }

            return "String";
        }

        public static bool IsNumeric(string operand)
        {
            if (int.TryParse(operand, out _)) return true;
            return double.TryParse(operand, out _);
        }
        public static string DetermineTypeFromCondition(string value)
        {
            // Simple logic to determine type based on the class name
            if (!string.IsNullOrEmpty(value))
            {
                if (value == "true" || value == "false")
                {
                    return "Boolean";
                }
                else if (int.TryParse(value, out _))
                {
                    return "Integer";
                }
                else
                {
                    return "String";
                }
            }
            return "String"; // Default fallback
        }

        // Method to update prompts with if condition based on the cond attribute
        static void UpdatePromptsWithIfCondition(XElement root)
        {
            // Find all <prompt> elements that have a 'cond' attribute
            var promptsWithCond = root.Descendants("prompt")
                                      .Where(p => p.Attribute("cond") != null)
                                      .ToList();

            foreach (var prompt in promptsWithCond)
            {
                // Get the condition from the 'cond' attribute
                string condValue = prompt.Attribute("cond").Value;

                // Create the <if> element with the condition
                XElement ifElement = new XElement("if",
                    new XAttribute("cond", condValue)
                );

                // Remove the 'cond' attribute from the original <prompt>
                prompt.Attribute("cond")?.Remove();

                // Add the <prompt> as a child of the <if> element
                ifElement.Add(prompt);

                // Replace the original <prompt> with the new <if> element
                prompt.ReplaceWith(ifElement);
            }
        }

        // Method to process grammar configurations from collection-configuration elements
        static void ProcessGrammarConfigurations(XElement root)
        {
            // Find all collection-configuration elements (note: underscore vs hyphen)
            var collectionConfigs = root.Descendants("collection-configuration").ToList();
            collectionConfigs.AddRange(root.Descendants("collection_configuration").ToList());

            foreach (var collectionConfig in collectionConfigs)
            {
                // Process vxml-properties elements (with property children)
                var vxmlPropertiesElement = collectionConfig.Element("vxml-properties");
                if (vxmlPropertiesElement != null)
                {
                    ProcessVxmlPropertiesWithPropertyElements(vxmlPropertiesElement, collectionConfig);
                }

                // Also check for vxml_properties elements (with attributes) - existing format
                var vxmlPropsElement = collectionConfig.Element("vxml_properties");
                if (vxmlPropsElement != null)
                {
                    ProcessVxmlPropertiesWithAttributes(vxmlPropsElement, collectionConfig);
                }

                // NEW: Process grammar_configuration elements (your XML format)
                var grammarConfigElement = collectionConfig.Element("grammar_configuration");
                if (grammarConfigElement != null)
                {
                    ProcessGrammarConfigurationElement(grammarConfigElement, collectionConfig);
                }
            }
        }

        // Process vxml-properties that contain property elements (like your test XML)
        static void ProcessVxmlPropertiesWithPropertyElements(XElement vxmlPropertiesElement, XElement collectionConfig)
        {
            var properties = vxmlPropertiesElement.Elements("property").ToList();

            string speechGrammarName = null;
            string dtmfGrammarName = null;

            foreach (var property in properties)
            {
                string name = property.Attribute("name")?.Value;
                string value = property.Attribute("value")?.Value;

                if (name == "speechgrammarname" && !string.IsNullOrEmpty(value))
                {
                    speechGrammarName = value;
                }
                else if (name == "dtmfgrammarname" && !string.IsNullOrEmpty(value))
                {
                    dtmfGrammarName = value;
                }
            }

            // Store grammar information as attributes on the collection-configuration element
            // This makes it available for later processing by the conversion pipeline
            if (!string.IsNullOrEmpty(speechGrammarName))
            {
                collectionConfig.SetAttributeValue("speechgrammarname", speechGrammarName);
                Console.WriteLine($"Found speech grammar: {speechGrammarName}");
            }

            if (!string.IsNullOrEmpty(dtmfGrammarName))
            {
                collectionConfig.SetAttributeValue("dtmfgrammarname", dtmfGrammarName);
                Console.WriteLine($"Found DTMF grammar: {dtmfGrammarName}");
            }
        }

        // Process vxml_properties that have attributes directly (existing format)
        static void ProcessVxmlPropertiesWithAttributes(XElement vxmlPropsElement, XElement collectionConfig)
        {
            string speechGrammarName = vxmlPropsElement.Attribute("speech-grammar-name")?.Value ??
                                     vxmlPropsElement.Attribute("speechgrammarname")?.Value;
            string dtmfGrammarName = vxmlPropsElement.Attribute("dtmf-grammar-name")?.Value ??
                                   vxmlPropsElement.Attribute("dtmfgrammarname")?.Value;

            // Store grammar information as attributes on the collection-configuration element
            if (!string.IsNullOrEmpty(speechGrammarName))
            {
                collectionConfig.SetAttributeValue("speechgrammarname", speechGrammarName);
                Console.WriteLine($"Found speech grammar: {speechGrammarName}");
            }

            if (!string.IsNullOrEmpty(dtmfGrammarName))
            {
                collectionConfig.SetAttributeValue("dtmfgrammarname", dtmfGrammarName);
                Console.WriteLine($"Found DTMF grammar: {dtmfGrammarName}");
            }
        }

        // Process grammar_configuration elements (your XML format)
        static void ProcessGrammarConfigurationElement(XElement grammarConfigElement, XElement collectionConfig)
        {
            string speechGrammarName = null;
            string dtmfGrammarName = null;

            // Extract from grammars element
            var grammarsElement = grammarConfigElement.Element("grammars");
            if (grammarsElement != null)
            {
                speechGrammarName = grammarsElement.Attribute("filename")?.Value;
            }

            // Extract from dtmfgrammars element
            var dtmfGrammarsElement = grammarConfigElement.Element("dtmfgrammars");
            if (dtmfGrammarsElement != null)
            {
                dtmfGrammarName = dtmfGrammarsElement.Attribute("filename")?.Value;
            }

            // Store grammar information as attributes on the collection-configuration element
            if (!string.IsNullOrEmpty(speechGrammarName))
            {
                collectionConfig.SetAttributeValue("speechgrammarname", speechGrammarName);
                Console.WriteLine($"Found speech grammar: {speechGrammarName}");
            }

            if (!string.IsNullOrEmpty(dtmfGrammarName))
            {
                collectionConfig.SetAttributeValue("dtmfgrammarname", dtmfGrammarName);
                Console.WriteLine($"Found DTMF grammar: {dtmfGrammarName}");
            }
        }

    }
}