#!/usr/bin/env python3
"""
Branch Guard – configurable Git hook
-----------------------------------
Blocks unsafe commits / pushes and enforces commit‑message style before data
leaves your machine. Everything is driven from an optional YAML file so your
team can tweak behaviour without editing code.

Install (one‑time per repo)
~~~~~~~~~~~~~~~~~~~~~~~~~~~
1. Place this script in the repo root (or anywhere in PATH).
2. Run:   `python branch_guard.py --install pre-commit`
   Add `pre-push` instead if you prefer that stage.
3. `pip install pyyaml` (and GitHub CLI if you want PR auto‑creation).
4. Create `.branchguard.yml` (sample below) or rely on defaults.

Sample .branchguard.yml
~~~~~~~~~~~~~~~~~~~~~~~
blocked_branches:
  - main
  - master
required_prefix: feature/
enforce_conventional_commits: true
auto_push: prompt   # true | false | prompt

Key features
~~~~~~~~~~~~
* Blocks commits to **blocked_branches**.
* Ensures branch name starts with **required_prefix**.
* Enforces Conventional Commits header when enabled.
* Optionally pushes and opens a PR via GitHub CLI.

Run `python branch_guard.py --install pre-commit` and you’re covered.
"""

import sys, subprocess, yaml, re, os, pathlib, textwrap

DEFAULT_CONFIG = {
    "blocked_branches": ["main", "master"],
    "required_prefix": "feature/",
    "enforce_conventional_commits": True,
    "auto_push": "prompt",  # true / false / prompt
}

CONVENTIONAL_REGEX = re.compile(
    r"^(feat|fix|docs|style|refactor|perf|test|chore)(\([\w\-]+\))?: .+"
)

def load_config():
    repo_root = subprocess.run(
        ["git", "rev-parse", "--show-toplevel"], capture_output=True, text=True
    ).stdout.strip()
    path = pathlib.Path(repo_root) / ".branchguard.yml"
    if path.exists():
        with open(path, "r", encoding="utf-8") as f:
            return {**DEFAULT_CONFIG, **yaml.safe_load(f)}
    return DEFAULT_CONFIG


def current_branch():
    return (
        subprocess.run(["git", "symbolic-ref", "--short", "HEAD"], capture_output=True, text=True)
        .stdout.strip()
    )


def get_commit_msg():
    # commit-msg hook passes file path; pre-commit passes none
    if len(sys.argv) == 2 and os.path.isfile(sys.argv[1]):
        with open(sys.argv[1], "r", encoding="utf-8") as f:
            return f.readline().strip()
    return None


def guard():
    cfg = load_config()
    branch = current_branch()
    if branch in cfg["blocked_branches"]:
        print(
            f"🛑  Commit blocked – branch '{branch}' is protected.\nCreate a branch with '{cfg['required_prefix']}<ticket>'"
        )
        sys.exit(1)
    if not branch.startswith(cfg["required_prefix"]):
        print(
            f"🛑  Commit blocked – branch name must start with '{cfg['required_prefix']}'."
        )
        sys.exit(1)
    if cfg["enforce_conventional_commits"]:
        msg = get_commit_msg()
        if msg and not CONVENTIONAL_REGEX.match(msg):
            print(
                "🛑  Commit blocked – message must follow Conventional Commits (e.g., 'feat(login): add SSO')."
            )
            sys.exit(1)


def install_hook(hook_name):
    repo_root = subprocess.run(
        ["git", "rev-parse", "--show-toplevel"], capture_output=True, text=True
    ).stdout.strip()
    hook_path = pathlib.Path(repo_root) / ".git" / "hooks" / hook_name
    script = textwrap.dedent(
        f"""\
        #!/usr/bin/env bash
        python \"{pathlib.Path(__file__).absolute()}\" "$@"
        """
    )
    hook_path.write_text(script)
    hook_path.chmod(0o755)
    print(f"Installed {hook_name} hook.")


def auto_push(cfg):
    branch = current_branch()
    if cfg["auto_push"] is True:
        subprocess.run(["git", "push", "-u", "origin", branch])
    elif cfg["auto_push"] == "prompt":
        if (
            input(f"Push '{branch}' to origin and open PR? [y/N]: ").strip().lower()
            == "y"
        ):
            subprocess.run(["git", "push", "-u", "origin", branch])
            subprocess.run(["gh", "pr", "create", "--fill", "--web"])


def main():
    if "--install" in sys.argv:
        idx = sys.argv.index("--install")
        hook = sys.argv[idx + 1] if idx + 1 < len(sys.argv) else "pre-commit"
        install_hook(hook)
        return
    guard()
    cfg = load_config()
    auto_push(cfg)


if __name__ == "__main__":
    main()
