﻿using System;
using System.Collections.Generic;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public class DmStateService
    {
        public static DmStateModel ProcessState(XElement dmStateElement)
        {
            var dmStateModel = new DmStateModel
            {
                Id = dmStateElement.Attribute("id")?.Value,
                dmType = dmStateElement.Attribute("type")?.Value,
                SessionMappings = new List<SessionMappingModel>(),
                Type = "dm-state"
            };

            ProcessAssignments(dmStateElement, dmStateModel);
            // Check for <success> tag and call DmStateSuccessService
            var successElement = dmStateElement.Element("success");
            if (successElement != null)
            {
                dmStateModel.success = DmStateSuccessService.ReadSuccessState(successElement);

                /*var commandElement = dmStateElement.Element("command");

                if (commandElement != null)
                {
                    *//* Console.WriteLine("-----------------"+dmStateModel.Id + "------------------------");
                     dmStateModel.success = DmStateSuccessService.ReadSuccessState(commandElement);*//*
                    var commandSuccess = DmStateSuccessService.ReadSuccessState(commandElement);

                    if (dmStateModel.success == null)
                    {
                        dmStateModel.success = commandSuccess;
                    }
                    else
                    {
                        foreach (var action in commandSuccess.ActionList)
                        {
                            action.isCommand = true;
                            dmStateModel.success.ActionList.Add(action);
                        }
                    }
                }*/

            }
            var commandElement = dmStateElement.Element("command");

            if (commandElement != null)
            {
              
                var commandSuccess = DmStateSuccessService.ReadSuccessState(commandElement);

                if (dmStateModel.success == null)
                {
                    dmStateModel.success = commandSuccess;
                }
                else
                {
                    foreach (var action in commandSuccess.ActionList)
                    {
                        action.isCommand = true;
                        dmStateModel.success.ActionList.Add(action);
                    }
                }
            }

            // Process <event> tags
            var eventElements = dmStateElement.Elements("event");
            if (eventElements.Any())
            {
                dmStateModel.EventList = DmStateEventService.ReadEventState(eventElements);
            }
            // Process <collection_configuration> tag
            var collectionConfigurationElement = dmStateElement.Element("collection_configuration");
            if (collectionConfigurationElement != null)
            {
                dmStateModel.CollectionConfiguration = CollectionConfigurationService.ReadCollectionConfiguration(collectionConfigurationElement);
            }

            // Process <collection_configuration> tag
            var globalConfigurationElement = dmStateElement.Element("global_configuration");
            if (globalConfigurationElement != null)
            {
                dmStateModel.GlobalConfiguration = GlobalConfigurationService.ReadCollectionConfiguration(globalConfigurationElement);
            }


            return dmStateModel;
        }

        private static void ProcessAssignments(XElement stateElement, DmStateModel model)
        {
            // Use SessionMappingHelper to process session-mapping elements
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(stateElement.Elements("session-mapping"));
            model.SessionMappings.AddRange(sessionMappings);
            var varMapping = SessionMappingHelper.ProcessSessionMappings(stateElement.Elements("var"));
            model.SessionMappings.AddRange(varMapping);
        }

    }
}
