using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Bot.ObjectModel;

namespace MixToCopilotStudioConverter
{
    public class DataValueJsonConverter : JsonConverter<DataValue>
    {
        public override DataValue Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            // Implement custom deserialization logic hereS
            // For demonstration, let's assume DataValue has a string property called 'Value'
            // You must adjust this logic to match the actual structure of DataValue
            if (reader.TokenType == JsonTokenType.String)
            {
                string value = reader.GetString();
                // DataValue is an abstract class, so we cannot instantiate it directly.
                // This converter is likely for a specific scenario where DataValue is expected to be a StringDataValue.
                return new StringDataValue(value);
            }
            throw new JsonException($"Unexpected token when deserializing DataValue: {reader.TokenType}");
        }

        public override void Write(Utf8JsonWriter writer, DataValue value, JsonSerializerOptions options)
        {
            // Implement custom serialization logic here
            // For demonstration, let's assume DataValue has a string property called 'Value'
            writer.WriteStringValue(value.ToString()); // Adjust as needed
        }
    }
}
