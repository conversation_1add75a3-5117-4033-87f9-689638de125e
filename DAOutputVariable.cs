﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class DAOutputVariable
    {
        public string Value { get; set; }
        public string Type { get; set; }

        public DAOutputVariable()
        {
        }

        public void DisplayDetails()
        {
            Console.WriteLine($"Prompt ID: {Value}");
            Console.WriteLine($"Text: {Type}");
        }
    }
}
