using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Bot.ObjectModel;
using Microsoft.PS.Tools;

namespace NDFToCopilotStudioConverter
{
    public static class ExternalGrammarUtil
    {
        public static string ExtractEntityNameFromDmState(DmStateModel dmState)
        {
            if (dmState?.Id == null)
                return null;

            // Entity name is the DM state ID without "_Entity" suffix
            return dmState.Id;
        }

        public static Dictionary<string, CopilotStudioEntity> ConvertJsonPathToEntities(string externalGrammarJsonPath, Dictionary<string, string> grammarNameToEntityMap)
        {
            var externalGrammarToEntityMap = new Dictionary<string, CopilotStudioEntity>();

            try
            {
                // Use ExternalGrammarConverter NuGet package
                externalGrammarToEntityMap = ExternalGrammarConverter.ConvertJsonPathToEntities(externalGrammarJsonPath, grammarNameToEntityMap);
                Console.WriteLine($"Successfully loaded {externalGrammarToEntityMap.Count} entities from ExternalGrammarConverter");
            }
            catch (Exception e)
            {
                Console.WriteLine("No external grammar json file found or error in ExternalGrammarConverter");
                Console.WriteLine($"Error: {e.Message}");
            }

            return externalGrammarToEntityMap;
        }

        /// <summary>
        /// Gets unique custom entities from external grammar entity map
        /// Used to add custom entities to the bot definition
        /// </summary>
        /// <param name="externalGrammarToEntityMap">External grammar to entity mapping</param>
        /// <returns>List of unique custom entities</returns>

        /// <summary>
        /// Original method for backward compatibility - now delegates to ConvertJsonPathToEntities
        /// Converts external grammar JSON to entity map using ExternalGrammarConverter.dll
        /// </summary>
        /// <param name="externalGrammarJsonPath">Path to the external grammar JSON file</param>
        /// <param name="grammarNameToEntityMap">Grammar name to entity mapping from Mix project</param>
        /// <param name="defaultLanguage">Default language for the conversion</param>
        /// <returns>Dictionary mapping grammar names to Copilot Studio entities</returns>
        // public static Dictionary<string, object> ConvertJsonPathToEntities(string externalGrammarJsonPath, Dictionary<string, string> grammarNameToEntityMap, string defaultLanguage)
        // {
        //     try
        //     {
        //         // Try to use ExternalGrammarConverter.dll first
        //         var assembly = System.Reflection.Assembly.LoadFrom("lib/ExternalGrammarConverter.dll");
        //         var converterType = assembly.GetType("ExternalGrammarConverter")
        //                          ?? assembly.GetTypes().FirstOrDefault(t => t.Name.Contains("ExternalGrammarConverter"));

        //         if (converterType != null)
        //         {
        //             var method = converterType.GetMethod("ConvertJsonPathToEntities");
        //             if (method != null)
        //             {
        //                 var result = method.Invoke(null, new object[] { externalGrammarJsonPath, grammarNameToEntityMap, defaultLanguage });
        //                 if (result is Dictionary<string, object> entityMap)
        //                 {
        //                     Console.WriteLine($"Successfully loaded {entityMap.Count} entities from ExternalGrammarConverter.dll");
        //                     return entityMap;
        //                 }
        //             }
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         Console.WriteLine($"Failed to use ExternalGrammarConverter.dll: {ex.Message}");
        //         Console.WriteLine("Falling back to custom implementation...");
        //     }

        //     // Fallback to custom implementation
        //     return ConvertJsonPathToEntitiesFallback(externalGrammarJsonPath, grammarNameToEntityMap, defaultLanguage);
        // }

        /// <summary>
        /// Fallback implementation for external grammar conversion
        /// </summary>
        // private static Dictionary<string, object> ConvertJsonPathToEntitiesFallback(string externalGrammarJsonPath, Dictionary<string, string> grammarNameToEntityMap, string defaultLanguage)
        // {
        //     var entityMap = new Dictionary<string, object>();

        //     try
        //     {
        //         if (!File.Exists(externalGrammarJsonPath))
        //         {
        //             Console.WriteLine($"External grammar JSON file not found: {externalGrammarJsonPath}");
        //             return entityMap;
        //         }

        //         // Load grammar data from JSON
        //         var grammarData = GrammarDataReader.LoadGrammarData(externalGrammarJsonPath);

        //         foreach (var item in grammarData)
        //         {
        //             if (item.Grammars != null)
        //             {
        //                 foreach (var grammar in item.Grammars)
        //                 {
        //                     // Split grammar string by newlines and process each grammar file
        //                     var grammarFiles = grammar.Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries);

        //                     foreach (var grammarFile in grammarFiles)
        //                     {
        //                         var cleanGrammarFile = grammarFile.Trim();
        //                         if (!string.IsNullOrEmpty(cleanGrammarFile) && grammarNameToEntityMap.ContainsKey(cleanGrammarFile))
        //                         {
        //                             // Create entity based on grammar data
        //                             var entity = CreateCopilotStudioEntityFromGrammarData(item, defaultLanguage);
        //                             if (entity != null)
        //                             {
        //                                 entityMap[cleanGrammarFile] = entity;
        //                             }
        //                         }
        //                     }
        //                 }
        //             }
        //         }

        //         Console.WriteLine($"Fallback implementation loaded {entityMap.Count} entities from {externalGrammarJsonPath}");
        //     }
        //     catch (Exception ex)
        //     {
        //         Console.WriteLine($"Error in fallback grammar conversion: {ex.Message}");
        //     }

        //     return entityMap;
        // }
        /// <summary>
        /// Scans through Mix DialogProject.Components and their nodes to extract grammar-to-entity mappings
        /// Uses local implementation since ExternalGrammarConverter package doesn't provide this method
        /// </summary>
        /// <param name="dialogProject">The Mix DialogProject containing components and nodes</param>
        /// <returns>Dictionary mapping grammar names to entity names</returns>
        public static Dictionary<string, string> GetGrammarNameToEntityMap(DialogList dialogProject)
        {
            Console.WriteLine("GetGrammarNameToEntityMap: Starting method");
            Console.WriteLine("GetGrammarNameToEntityMap: Using local implementation");
            var result = GetGrammarNameToEntityMapFallback(dialogProject);
            Console.WriteLine($"GetGrammarNameToEntityMap: Local implementation returned {result.Count} mappings");
            return result;
        }

        /// <summary>
        /// Fallback implementation for grammar-to-entity mapping
        /// </summary>
        private static Dictionary<string, string> GetGrammarNameToEntityMapFallback(DialogList dialogProject)
        {
            var grammarToEntityMap = new Dictionary<string, string>();

            if (dialogProject?.dialogModels == null)
                return grammarToEntityMap;

            // Scan through all dialog models (components)
            foreach (var dialogModel in dialogProject.dialogModels)
            {
                if (dialogModel?.States == null)
                    continue;

                // Scan through all states in the dialog
                foreach (var state in dialogModel.States)
                {
                    // Process DM states (recognition nodes)
                    if (state is DmStateModel dmState)
                    {
                        ProcessDmStateForGrammarMapping(dmState, grammarToEntityMap);
                    }
                }
            }

            return grammarToEntityMap;
        }

        /// <summary>
        /// Creates grammar-to-entity mapping from existing grammar data JSON file
        /// Uses local implementation since ExternalGrammarConverter package doesn't provide this method
        /// </summary>
        /// <param name="grammarDataFilePath">Path to the grammar_entities.json file</param>
        /// <returns>Dictionary mapping grammar names to entity names</returns>
        public static Dictionary<string, string> GetGrammarNameToEntityMapFromJson(string grammarDataFilePath)
        {
            Console.WriteLine($"GetGrammarNameToEntityMapFromJson: Using local implementation for {grammarDataFilePath}");
            return GetGrammarNameToEntityMapFromJsonFallback(grammarDataFilePath);
        }

        /// <summary>
        /// Fallback implementation for JSON-based grammar-to-entity mapping
        /// </summary>
        private static Dictionary<string, string> GetGrammarNameToEntityMapFromJsonFallback(string grammarDataFilePath)
        {
            var grammarToEntityMap = new Dictionary<string, string>();

            try
            {
                var grammarData = GrammarDataReader.LoadGrammarData(grammarDataFilePath);

                foreach (var item in grammarData)
                {
                    if (item.Grammars != null)
                    {
                        foreach (var grammar in item.Grammars)
                        {
                            // Split grammar string by newlines and process each grammar file
                            var grammarFiles = grammar.Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries);

                            foreach (var grammarFile in grammarFiles)
                            {
                                var cleanGrammarFile = grammarFile.Trim();
                                if (!string.IsNullOrEmpty(cleanGrammarFile))
                                {
                                    grammarToEntityMap[cleanGrammarFile] = item.Entity;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading grammar data from JSON: {ex.Message}");
            }

            return grammarToEntityMap;
        }

        /// <summary>
        /// Processes a DM state to extract grammar-to-entity mappings from recognition nodes
        /// </summary>
        /// <param name="dmState">The DM state to process</param>
        /// <param name="grammarToEntityMap">The dictionary to populate with mappings</param>
        private static void ProcessDmStateForGrammarMapping(DmStateModel dmState, Dictionary<string, string> grammarToEntityMap)
        {
            if (dmState == null)
                return;

            // Extract entity name from the DM state ID
            string entityName = ExtractEntityNameFromDmState(dmState);

            if (string.IsNullOrEmpty(entityName))
                return;

            // Extract speech grammar name
            string speechGrammarName = ExtractSpeechGrammarName(dmState);
            if (!string.IsNullOrEmpty(speechGrammarName))
            {
                grammarToEntityMap[speechGrammarName] = entityName;
            }

            // Extract DTMF grammar name
            string dtmfGrammarName = ExtractDtmfGrammarName(dmState);
            if (!string.IsNullOrEmpty(dtmfGrammarName))
            {
                grammarToEntityMap[dtmfGrammarName] = entityName;
            }
        }

        /// <summary>
        /// Extracts the entity name from a DM state
        /// </summary>
        /// <param name="dmState">The DM state</param>
        /// <returns>The entity name</returns>


        /// <summary>
        /// Extracts speech-grammar-name property from recognition node
        /// </summary>
        /// <param name="dmState">The DM state containing recognition configuration</param>
        /// <returns>The speech grammar name</returns>
        private static string ExtractSpeechGrammarName(DmStateModel dmState)
        {
            // Look for speech grammar configuration in collection configuration
            if (dmState?.CollectionConfiguration?.VxmlProperties != null)
            {
                var vxmlProps = dmState.CollectionConfiguration.VxmlProperties;

                // First try to get the actual speech grammar name from XML attributes
                if (!string.IsNullOrEmpty(vxmlProps.speechgrammarname))
                {
                    return vxmlProps.speechgrammarname;
                }

                // Fallback: construct based on DM state ID (common pattern in Mix)
                return dmState.Id + ".grxml";
            }

            return null;
        }

        /// <summary>
        /// Extracts dtmf-grammar-name property from recognition node
        /// </summary>
        /// <param name="dmState">The DM state containing recognition configuration</param>
        /// <returns>The DTMF grammar name</returns>
        private static string ExtractDtmfGrammarName(DmStateModel dmState)
        {
            // Look for DTMF grammar configuration in collection configuration
            if (dmState?.CollectionConfiguration?.VxmlProperties != null)
            {
                var vxmlProps = dmState.CollectionConfiguration.VxmlProperties;

                // First try to get the actual DTMF grammar name from XML attributes
                if (!string.IsNullOrEmpty(vxmlProps.dtmfgrammarname))
                {
                    return vxmlProps.dtmfgrammarname;
                }

                // Fallback: construct based on DM state ID (common pattern in Mix)
                return dmState.Id + "_dtmf.grxml";
            }

            return null;
        }

        /// <summary>
        /// Gets a Copilot Studio entity with grammar priority resolution
        /// Follows the exact priority order: speech grammar -> DTMF grammar -> standard entity lookup
        /// Uses local implementation since ExternalGrammarConverter package doesn't provide this method
        /// </summary>
        /// <param name="dmState">The recognition node (DM state)</param>
        /// <param name="entityName">The entity name to resolve</param>
        /// <param name="grammarToEntityMap">The grammar-to-entity mapping dictionary</param>
        /// <returns>Copilot Studio entity object</returns>
        public static object GetEntityWithGrammarPriority(DmStateModel dmState, string entityName, Dictionary<string, string> grammarToEntityMap)
        {
            Console.WriteLine($"GetEntityWithGrammarPriority: Using local implementation for entity '{entityName}'");
            return GetEntityWithGrammarPriorityFallback(dmState, entityName, grammarToEntityMap);
        }

        /// <summary>
        /// Fallback implementation for grammar priority resolution
        /// </summary>
        private static object GetEntityWithGrammarPriorityFallback(DmStateModel dmState, string entityName, Dictionary<string, string> grammarToEntityMap)
        {
            if (dmState == null || string.IsNullOrEmpty(entityName))
                return null;

            // Extract grammar names from the DM state
            string speechGrammar = ExtractSpeechGrammarName(dmState) ?? "";
            string dtmfGrammar = ExtractDtmfGrammarName(dmState) ?? "";

            // PRIORITY ORDER (exactly as specified in requirements):

            // 1. Check speech grammar first
            if (speechGrammar != "" && grammarToEntityMap.ContainsKey(speechGrammar))
            {
                return CreateCopilotStudioEntityFromGrammar(grammarToEntityMap[speechGrammar], speechGrammar, "speech");
            }
            // 2. Check DTMF grammar second
            else if (dtmfGrammar != "" && grammarToEntityMap.ContainsKey(dtmfGrammar))
            {
                return CreateCopilotStudioEntityFromGrammar(grammarToEntityMap[dtmfGrammar], dtmfGrammar, "dtmf");
            }
            // 3. Fall back to standard entity lookup
            else
            {
                return CreateStandardCopilotStudioEntity(entityName, dmState);
            }
        }

        /// <summary>
        /// Alternative method that uses the existing grammar JSON file for entity resolution
        /// This method loads the grammar data from the JSON file and uses it for entity resolution
        /// </summary>
        /// <param name="dmState">The recognition node (DM state)</param>
        /// <param name="entityName">The entity name to resolve</param>
        /// <param name="grammarDataFilePath">Path to the grammar_entities.json file</param>
        /// <returns>Copilot Studio entity object</returns>
        public static object GetEntityWithGrammarPriorityFromJson(DmStateModel dmState, string entityName, string grammarDataFilePath = "grammar_entities.json")
        {
            var grammarToEntityMap = GetGrammarNameToEntityMapFromJson(grammarDataFilePath);
            return GetEntityWithGrammarPriority(dmState, entityName, grammarToEntityMap);
        }

        /// <summary>
        /// Helper method to validate if a DM state has grammar configuration
        /// </summary>
        /// <param name="dmState">The DM state to check</param>
        /// <returns>True if the DM state has grammar configuration</returns>
        public static bool HasGrammarConfiguration(DmStateModel dmState)
        {
            if (dmState?.CollectionConfiguration?.VxmlProperties == null)
                return false;

            var vxmlProps = dmState.CollectionConfiguration.VxmlProperties;
            return !string.IsNullOrEmpty(vxmlProps.speechgrammarname) ||
                   !string.IsNullOrEmpty(vxmlProps.dtmfgrammarname);
        }

        /// <summary>
        /// Helper method to get all grammar names from a DM state
        /// </summary>
        /// <param name="dmState">The DM state</param>
        /// <returns>List of grammar names (speech and DTMF)</returns>
        public static List<string> GetAllGrammarNames(DmStateModel dmState)
        {
            var grammarNames = new List<string>();

            var speechGrammar = ExtractSpeechGrammarName(dmState);
            if (!string.IsNullOrEmpty(speechGrammar))
                grammarNames.Add(speechGrammar);

            var dtmfGrammar = ExtractDtmfGrammarName(dmState);
            if (!string.IsNullOrEmpty(dtmfGrammar))
                grammarNames.Add(dtmfGrammar);

            return grammarNames;
        }

        /// <summary>
        /// Helper method to determine if an entity should use grammar-based resolution
        /// </summary>
        /// <param name="dmState">The DM state</param>
        /// <param name="grammarToEntityMap">The grammar-to-entity mapping</param>
        /// <returns>True if grammar-based resolution should be used</returns>
        public static bool ShouldUseGrammarBasedResolution(DmStateModel dmState, Dictionary<string, string> grammarToEntityMap)
        {
            if (!HasGrammarConfiguration(dmState) || grammarToEntityMap == null || grammarToEntityMap.Count == 0)
                return false;

            var grammarNames = GetAllGrammarNames(dmState);
            return grammarNames.Any(grammarName => grammarToEntityMap.ContainsKey(grammarName));
        }

        /// <summary>
        /// Creates a Copilot Studio entity from grammar mapping
        /// </summary>
        /// <param name="entityName">The entity name</param>
        /// <param name="grammarName">The grammar name</param>
        /// <param name="grammarType">The type of grammar (speech/dtmf)</param>
        /// <returns>Copilot Studio entity</returns>
        private static object CreateCopilotStudioEntityFromGrammar(string entityName, string grammarName, string grammarType)
        {
            // Load grammar data to get entity details
            var grammarData = LoadGrammarDataForEntity(entityName);

            if (grammarData != null)
            {
                return CreateEntityFromGrammarData(grammarData, grammarType);
            }

            // Fallback: Create a basic closed list entity
            var closedListEntity = new ClosedListEntity.Builder();

            return new EmbeddedEntity.Builder()
            {
                Definition = closedListEntity
            }.Build();
        }

        /// <summary>
        /// Loads grammar data for a specific entity from the JSON file
        /// </summary>
        /// <param name="entityName">The entity name to load data for</param>
        /// <returns>Grammar data model or null if not found</returns>
        private static GrammarDataModel LoadGrammarDataForEntity(string entityName)
        {
            try
            {
                var grammarData = GrammarDataReader.LoadGrammarData("grammar_entities.json");
                return grammarData.FirstOrDefault(g => g.Entity == entityName);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Creates a Bot Framework entity from grammar data
        /// </summary>
        /// <param name="grammarData">The grammar data model</param>
        /// <param name="grammarType">The type of grammar (speech/dtmf)</param>
        /// <returns>Bot Framework entity</returns>
        private static object CreateEntityFromGrammarData(GrammarDataModel grammarData, string grammarType)
        {
            // Determine entity type based on grammar data
            switch (grammarData.EntityType?.ToLowerInvariant())
            {
                case "boolean":
                    return CreateBooleanEntity(grammarData, grammarType);
                case "closedlist":
                    return CreateClosedListEntity(grammarData, grammarType);
                default:
                    return CreateClosedListEntity(grammarData, grammarType);
            }
        }

        /// <summary>
        /// Creates a standard Copilot Studio entity (fallback)
        /// </summary>
        /// <param name="entityName">The entity name</param>
        /// <param name="dmState">The DM state for context</param>
        /// <returns>Copilot Studio entity</returns>
        private static object CreateStandardCopilotStudioEntity(string entityName, DmStateModel dmState)
        {
            // Determine entity type based on DM state type
            string dmType = dmState?.dmType ?? "";

            switch (dmType.ToUpperInvariant())
            {
                case "ZPCD":
                    return new ZipCodePrebuiltEntity();
                case "PHON":
                    return new PhoneNumberPrebuiltEntity();
                case "DIGT":
                    return new NumberPrebuiltEntity();
                case "CRED":
                    return new NumberPrebuiltEntity();
                case "CURR":
                    return new MoneyPrebuiltEntity();
                default:
                    // Default to string entity
                    return new StringPrebuiltEntity();
            }
        }

        /// <summary>
        /// Creates a Boolean entity from grammar data
        /// </summary>
        /// <param name="grammarData">The grammar data model</param>
        /// <param name="grammarType">The type of grammar (speech/dtmf)</param>
        /// <returns>Bot Framework Boolean entity</returns>
        private static object CreateBooleanEntity(GrammarDataModel grammarData, string grammarType)
        {
            var closedListEntity = new ClosedListEntity.Builder();

            // Add Boolean values based on SWI meanings
            if (grammarData.SWI_meaning != null)
            {
                foreach (var meaning in grammarData.SWI_meaning)
                {
                    var item = new ClosedListItem.Builder()
                    {
                        Id = meaning.name,
                        DisplayName = meaning.name
                    };

                    // Add vocabulary entries
                    if (meaning.VocabEnUs != null)
                    {
                        foreach (var vocab in meaning.VocabEnUs)
                        {
                            var vocabEntries = vocab.Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries);
                            foreach (var entry in vocabEntries)
                            {
                                item.Synonyms.Add(entry.Trim());
                            }
                        }
                    }

                    // Add DTMF if this is DTMF grammar type
                    if (grammarType == "dtmf" && !string.IsNullOrEmpty(meaning.dtmf))
                    {
                        item.Synonyms.Add(meaning.dtmf);
                    }

                    closedListEntity.Items.Add(item);
                }
            }

            return new EmbeddedEntity.Builder()
            {
                Definition = closedListEntity
            }.Build();
        }

        /// <summary>
        /// Creates a Closed List entity from grammar data
        /// </summary>
        /// <param name="grammarData">The grammar data model</param>
        /// <param name="grammarType">The type of grammar (speech/dtmf)</param>
        /// <returns>Bot Framework Closed List entity</returns>
        private static object CreateClosedListEntity(GrammarDataModel grammarData, string grammarType)
        {
            var closedListEntity = new ClosedListEntity.Builder();

            // Add items based on SWI meanings
            if (grammarData.SWI_meaning != null)
            {
                foreach (var meaning in grammarData.SWI_meaning)
                {
                    var item = new ClosedListItem.Builder()
                    {
                        Id = meaning.name,
                        DisplayName = meaning.name
                    };

                    // Add vocabulary entries
                    if (meaning.VocabEnUs != null)
                    {
                        foreach (var vocab in meaning.VocabEnUs)
                        {
                            var vocabEntries = vocab.Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries);
                            foreach (var entry in vocabEntries)
                            {
                                item.Synonyms.Add(entry.Trim());
                            }
                        }
                    }

                    // Add DTMF if this is DTMF grammar type
                    if (grammarType == "dtmf" && !string.IsNullOrEmpty(meaning.dtmf))
                    {
                        item.Synonyms.Add(meaning.dtmf);
                    }

                    closedListEntity.Items.Add(item);
                }
            }

            // Configure DTMF options for telephony scenarios
            closedListEntity.DtmfMultipleChoiceOptions = new DtmfMultipleChoiceOptions.Builder()
            {
                GenerateMapping = false,
                ReadOutOptions = false
            };

            return new EmbeddedEntity.Builder()
            {
                Definition = closedListEntity
            }.Build();
        }
    }
}
