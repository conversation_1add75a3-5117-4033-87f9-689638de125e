﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class VxmlPropertiesModel
    {
        public string confidencelevel { get; set; }
        public string timeout { get; set; }
        public string incompletetimeout { get; set; }
        public string maxspeechtimeout { get; set; }
        public string termtimeout { get; set; }
        public string interdigittimeout { get; set; }
        public string inputmodes { get; set; }

        // Grammar-related properties for Bot Framework conversion
        public string speechgrammarname { get; set; }
        public string dtmfgrammarname { get; set; }

        public VxmlPropertiesModel()
        {

        }

    }
}
