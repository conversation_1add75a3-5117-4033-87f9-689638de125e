#!/usr/bin/env python3
"""
Simple script to find duplicate 'entity' keys in YAML that cause parsing errors.
"""

import re
import sys

def find_entity_duplicates(file_path):
    """Find duplicate 'entity' keys in the YAML file."""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    print(f"Analyzing file: {file_path}")
    print("=" * 50)
    
    # Find all 'entity:' keys
    entity_lines = []
    for line_num, line in enumerate(lines, 1):
        if re.match(r'^\s*entity:\s*', line):
            indent = len(line) - len(line.lstrip())
            entity_lines.append((line_num, indent, line.rstrip()))
    
    print(f"Found {len(entity_lines)} 'entity:' keys total")
    print("\nAll entity key locations:")
    for line_num, indent, content in entity_lines:
        print(f"Line {line_num:6d} (indent {indent:2d}): {content}")
    
    # Group by indentation level to find potential duplicates
    by_indent = {}
    for line_num, indent, content in entity_lines:
        if indent not in by_indent:
            by_indent[indent] = []
        by_indent[indent].append((line_num, content))
    
    print(f"\nGrouped by indentation level:")
    for indent in sorted(by_indent.keys()):
        occurrences = by_indent[indent]
        print(f"Indent {indent}: {len(occurrences)} occurrences")
        if len(occurrences) > 1:
            print("  *** POTENTIAL DUPLICATES ***")
        for line_num, content in occurrences:
            print(f"    Line {line_num}: {content}")
    
    # Look for components that might be missing proper structure
    print(f"\nLooking for malformed components:")
    malformed_components = []
    
    for line_num, line in enumerate(lines, 1):
        # Look for displayName with _QA suffix that might be a component
        if re.match(r'^\s{4}displayName:\s+\w+.*_QA\s*$', line):
            # Check if previous lines have proper component structure
            prev_lines = lines[max(0, line_num-5):line_num-1]
            has_component_kind = any(re.match(r'^\s*-\s+kind:\s+\w+Component', prev_line) 
                                   for prev_line in prev_lines)
            
            if not has_component_kind:
                malformed_components.append((line_num, line.rstrip()))
    
    if malformed_components:
        print(f"Found {len(malformed_components)} potentially malformed components:")
        for line_num, content in malformed_components[:10]:  # Show first 10
            print(f"  Line {line_num}: {content}")
        if len(malformed_components) > 10:
            print(f"  ... and {len(malformed_components) - 10} more")
    else:
        print("No malformed components found.")
    
    # Look for components that should have 'entity:' but are missing structure
    print(f"\nAnalyzing component structure issues:")
    
    # Find patterns where we have entity keys without proper component boundaries
    issues = []
    current_component = None
    entity_count_in_component = 0
    
    for line_num, line in enumerate(lines, 1):
        # Component start
        if re.match(r'^\s*-\s+kind:\s+\w+Component', line):
            if current_component and entity_count_in_component > 1:
                issues.append((current_component, entity_count_in_component))
            current_component = line_num
            entity_count_in_component = 0
        
        # Entity key
        elif re.match(r'^\s*entity:\s*', line):
            entity_count_in_component += 1
        
        # Potential component without proper structure
        elif re.match(r'^\s{4}displayName:\s+\w+.*_QA\s*$', line):
            if current_component is None:
                # This might be a component that's missing the proper header
                current_component = line_num
                entity_count_in_component = 0
    
    # Check last component
    if current_component and entity_count_in_component > 1:
        issues.append((current_component, entity_count_in_component))
    
    if issues:
        print(f"Found {len(issues)} components with multiple entity keys:")
        for component_line, entity_count in issues:
            print(f"  Component starting at line {component_line}: {entity_count} entity keys")
    
    print(f"\nSUMMARY:")
    print(f"- Total entity keys: {len(entity_lines)}")
    print(f"- Malformed components: {len(malformed_components)}")
    print(f"- Components with multiple entities: {len(issues)}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python find_entity_duplicates.py <yaml_file>")
        print("Example: python find_entity_duplicates.py Output.yml")
        sys.exit(1)
    
    file_path = sys.argv[1]
    find_entity_duplicates(file_path)

if __name__ == "__main__":
    main()
