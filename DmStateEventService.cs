﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public class DmStateEventService
    {
        public static List<EventModel> ReadEventState(IEnumerable<XElement> eventElements)
        {
            var eventList = new List<EventModel>();

            foreach (var eventElement in eventElements)
            {
                var eventModel = new EventModel
                {
                    Name = eventElement.Attribute("name")?.Value,
                    Next = eventElement.Attribute("next")?.Value
                };

                foreach (var sessionMappingElement in eventElement.Elements("session-mapping"))
                {
                    string key = sessionMappingElement.Attribute("key")?.Value;
                    string value = sessionMappingElement.Attribute("value")?.Value;
                    if(key!=null && value != null) {
                        var sessionMappingModel = new SessionMappingModel
                        {
                            key = key,
                            value = value
                        };
                        eventModel.SessionMappings.Add(sessionMappingModel);
                    }
                    
                }

               /* foreach (var actionElement in eventElement.Elements("action"))
                {
                    var transitionModel = new TransitionModel
                    {
                        next = actionElement.Attribute("next")?.Value
                    };

                    foreach (var sessionMappingElement in actionElement.Elements("session-mapping"))
                    {
                        var sessionMappingModel = new SessionMappingModel
                        {
                            key = sessionMappingElement.Attribute("key")?.Value,
                            value = sessionMappingElement.Attribute("value")?.Value
                        };
                        transitionModel.sessionMappings.Add(sessionMappingModel);
                    }

                    eventModel.TransitionList.Add(transitionModel);
                }*/

                eventList.Add(eventModel);
            }

            return eventList;
        }
    }

}
