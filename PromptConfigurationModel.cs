﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class PromptConfigurationModel
    {
        public PlayStateModel InitialPrompt { get; set; }
        public RepeatPromptsModel RepeatPrompts { get; set; }
        public List<PlayStateModel> NomatchPrompts { get; set; }
        public List<PlayStateModel> NoinputPrompts { get; set; }

        public List<NomatchPrefixeModel> NomatchPrefixes { get; set; }
        public List<NoinputPrefixeModel> NoinputPrefixes { get; set; }

        public PromptConfigurationModel()
        {
            NomatchPrompts = new List<PlayStateModel>();
            NoinputPrompts = new List<PlayStateModel>();
        }

    }
}
