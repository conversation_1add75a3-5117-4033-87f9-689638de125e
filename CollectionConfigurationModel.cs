﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class CollectionConfigurationModel
    {
        public string Confirmationmode { get; set; }
        public string Highconfidencelevel { get; set; }
        public string Inputmodes { get; set; }

        public ThresholdConfigurationModel ThresholdConfiguration { get; set; }

        public VxmlPropertiesModel VxmlProperties { get; set; }
        public PromptConfigurationModel PromptConfiguration { get; set; }

        public CollectionConfigurationModel()
        {
           
        }

    }
}
