﻿using System;

namespace NDFToCopilotStudioConverter
{
    public class SubdialogStateModel : StateModel
    {
        public List<SessionMappingModel> SessionMappings { get; set; }
        public string gotodialog { get; set; }
        public List<List<ConditionModel>> ConditionTransitionsList { get; set; }

        public SubdialogStateModel()
        {
            SessionMappings = new List<SessionMappingModel>();
        }
        
        public override void DisplayDetails()
        {
            Console.WriteLine($"Subdialog State ID: {Id}");
            Console.WriteLine($"Type: {Type}");
            Console.WriteLine($"gotodialog: {gotodialog}");
            foreach (var mapping in SessionMappings)
            {
                Console.WriteLine($"- {mapping.key}: {mapping.value}");
            }

            /*// Display the single transition if it exists
            if (Transition != null)
            {
                Console.WriteLine("Transition: ");
                Console.WriteLine($"- Label: {Transition.label}, Next: {Transition.next}");
                if (Transition.sessionMappings.Count > 0)
                {
                    Console.WriteLine("  Session Mappings: ");
                    foreach (var mapping in Transition.sessionMappings)
                    {
                        Console.WriteLine($"  - {mapping.key}: {mapping.value}");
                    }
                }
            }*/
            // Display each list of conditions (each list represents a separate if block)
            Console.WriteLine("Conditions and Transitions:");
            for (int i = 0; i < ConditionTransitionsList.Count; i++)
            {
                Console.WriteLine($"-- If Block {i + 1} --");
                foreach (var conditionModel in ConditionTransitionsList[i])
                {
                    DisplayConditionDetails(conditionModel, 1); // Start with level 1 indentation
                }
            }
            Console.WriteLine();
        }
        private void DisplayConditionDetails(ConditionModel conditionModel, int level)
        {
            var indent = new string(' ', level * 2); // Indentation for nested levels

            Console.WriteLine($"{indent}Condition: {conditionModel.Condition}"); // Raw condition string

            // Ensure Transitions is not null and process each transition
            var transition = conditionModel.Transitions;
            if (transition != null)
            {
                Console.WriteLine($"{indent}- Label: {transition.label}, Next: {transition.next}");

                if (transition.sessionMappings != null && transition.sessionMappings.Count > 0)
                {
                    Console.WriteLine($"{indent}  Session Mappings:");
                    foreach (var mapping in transition.sessionMappings)
                    {
                        Console.WriteLine($"{indent}  - {mapping.key}: {mapping.value}: {mapping.type}");
                    }
                }

                if (transition.InnerConditionTransitions != null && transition.InnerConditionTransitions.Count > 0)
                {
                    Console.WriteLine($"{indent}  Inner Conditions:");
                    foreach (var innerConditionModel in transition.InnerConditionTransitions)
                    {
                        DisplayConditionDetails(innerConditionModel, level + 1); // Recurse for inner conditions
                    }
                }
            }
        }
    }
}
