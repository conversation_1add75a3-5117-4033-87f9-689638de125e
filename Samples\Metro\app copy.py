import json

def flatten_external_grammar(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    flattened_data = []
    for item in data:
        flattened_item = {
            "Entity": item.get("Entity"),
            "Grammars": [],
            "SWI_meaning": [],
            "EntityType": item.get("EntityType")
        }

        # Split Grammers on \r\n if needed
        for g in item.get("Grammars", []):
            flattened_item["Grammars"].extend(g.splitlines())

        # Flatten SWI_meaning vocab
        for meaning in item.get("SWI_meaning", []):
            name = meaning.get("name")
            vocab_list = []
            for vocab_block in meaning.get("vocab", []):
                vocab_list.extend(vocab_block.get("values", []))
            flattened_item["SWI_meaning"].append({
                "Name": name,
                "Vocab": vocab_list
            })

        flattened_data.append(flattened_item)

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(flattened_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Flattened JSON written to {output_file}")

# Example usage
flatten_external_grammar(
    "ExternalGrammarEntityMapping_131946.json",
    "ExternalGrammarEntityMapping_simplified.json"
)
