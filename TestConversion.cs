using System;
using System.IO;
using NDFToCopilotStudioConverter;

class TestConversion
{
    static void Main(string[] args)
    {
        try
        {
            Console.WriteLine("Starting conversion test...");
            
            // Read the XML file
            string xmlFilePath = "test.xml";
            if (!File.Exists(xmlFilePath))
            {
                Console.WriteLine($"XML file not found: {xmlFilePath}");
                return;
            }
            
            string xmlContent = File.ReadAllText(xmlFilePath);
            Console.WriteLine($"Read XML file: {xmlFilePath}");
            
            // Process the XML
            string yamlContent = XmlProcessingClass.processXml(xmlContent);
            Console.WriteLine("XML processing completed");
            
            // Apply suffix replacements
            yamlContent = YamlSuffixReplacer.ReplaceYamlSuffix(yamlContent);
            Console.WriteLine("YAML suffix replacement completed");
            
            // Apply additional replacements
            if (yamlContent != null)
            {
                yamlContent = yamlContent.Replace(".toLowerCase()", "");
                yamlContent = yamlContent.Replace(".toLowerCase", "");
                yamlContent = yamlContent.Replace(".toUpperCase()", "");
                yamlContent = yamlContent.Replace(".toUpperCase", "");
                yamlContent = yamlContent.Replace("Global.undefined", "undefined");
                yamlContent = yamlContent.Replace("Global.null", "null");
                yamlContent = yamlContent.Replace("(GlobalVars.!IsBlank(", "Not(IsBlank(GlobalVars.");
                yamlContent = yamlContent.Replace("\\\"", "");
                yamlContent = System.Text.RegularExpressions.Regex.Replace(yamlContent, @"\\\w+", "");
                yamlContent = yamlContent.Replace("\\","");
                yamlContent = yamlContent.Replace("/", "");
            }
            
            // Write the output file
            string outputFilePath = "test_output.yml";
            File.WriteAllText(outputFilePath, yamlContent);
            Console.WriteLine($"Output written to: {outputFilePath}");
            
            Console.WriteLine("Conversion completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during conversion: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
