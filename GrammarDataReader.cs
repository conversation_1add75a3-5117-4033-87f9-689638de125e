﻿using System;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json;

namespace NDFToCopilotStudioConverter
{
    public class GrammarDataReader
    {
        public static List<GrammarDataModel> LoadGrammarData(string filePath)
        {
            try
            {
                string json = File.ReadAllText(filePath);
                List<GrammarDataModel> data = JsonConvert.DeserializeObject<List<GrammarDataModel>>(json);
                return data;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading JSON: {ex.Message}");
                return new List<GrammarDataModel>();
            }
        }
    }

    /// <summary>
    /// Bot Framework Grammar Data Reader - loads grammar data for Bot Framework conversion
    /// </summary>
    public class BotFrameworkGrammarDataReader
    {
        public static List<BotFrameworkGrammarDataModel> LoadBotFrameworkGrammarData(string filePath)
        {
            try
            {
                string json = File.ReadAllText(filePath);
                List<BotFrameworkGrammarDataModel> data = JsonConvert.DeserializeObject<List<BotFrameworkGrammarDataModel>>(json);
                return data;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading Bot Framework grammar JSON: {ex.Message}");
                return new List<BotFrameworkGrammarDataModel>();
            }
        }

        /// <summary>
        /// Converts existing Copilot Studio grammar data to Bot Framework format
        /// </summary>
        /// <param name="copilotStudioData">Copilot Studio grammar data</param>
        /// <returns>Bot Framework grammar data</returns>
        public static List<BotFrameworkGrammarDataModel> ConvertFromCopilotStudio(List<GrammarDataModel> copilotStudioData)
        {
            var botFrameworkData = new List<BotFrameworkGrammarDataModel>();

            foreach (var item in copilotStudioData)
            {
                var botFrameworkItem = new BotFrameworkGrammarDataModel
                {
                    Entity = item.Entity,
                    Grammars = new List<string>(item.Grammars ?? new List<string>()),
                    EntityType = item.EntityType,
                    Reason = item.Reason,
                    SWI_meaning = new List<BotFrameworkGrammarDataModel.BotFrameworkSWIMeaningModel>()
                };

                if (item.SWI_meaning != null)
                {
                    foreach (var meaning in item.SWI_meaning)
                    {
                        botFrameworkItem.SWI_meaning.Add(new BotFrameworkGrammarDataModel.BotFrameworkSWIMeaningModel
                        {
                            name = meaning.name,
                            VocabEnUs = new List<string>(meaning.VocabEnUs ?? new List<string>()),
                            dtmf = meaning.dtmf
                        });
                    }
                }

                botFrameworkData.Add(botFrameworkItem);
            }

            return botFrameworkData;
        }
    }
}
