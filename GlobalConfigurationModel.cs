﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class GlobalConfigurationModel
    {
        public string Confirmationmode { get; set; }
        public List<FailurePromptModel> Failureprompts { get; set; }
        public List<SuccessPromptModel> SuccessPrompts { get; set; }
        public List<SuccesscorrectedpromptModel> Successcorrectedprompt { get; set; }

       

        public GlobalConfigurationModel()
        {
            Failureprompts = new List<FailurePromptModel>();
            SuccessPrompts = new List<SuccessPromptModel>();
            Successcorrectedprompt = new List<SuccesscorrectedpromptModel>();
        }

    }
}
