﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class RepeatPromptsModel: PlayStateModel
    {
        //public Dictionary<string, List<PromptModel>> ConditionPrompts { get; set; }

        public RepeatPromptsModel()
        {
            //ConditionPrompts = new Dictionary<string, List<PromptModel>>();
        }

    }
}
