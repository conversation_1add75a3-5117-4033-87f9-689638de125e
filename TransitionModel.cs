﻿using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class TransitionModel
    {
        public string label { get; set; }
        public string next { get; set; }
        public string gotoDialogElement { get; set; }
        public List<SessionMappingModel> sessionMappings { get; set; }
        public List<PromptModel> PromptList { get; set; }
        public List<ConditionModel> InnerConditionTransitions { get; set; }
        //public List<ConditionPromptModel> InnerConditionPrompts { get; set; }
        public Dictionary<string, List<PromptModel>> ConditionPrompts { get; set; }
        
        public TransitionModel()
        {
            sessionMappings = new List<SessionMappingModel>();
            PromptList = new List<PromptModel>();
            InnerConditionTransitions = new List<ConditionModel>();
            ConditionPrompts = new Dictionary<string, List<PromptModel>>();
        }
    }
}
