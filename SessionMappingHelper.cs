using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using System.Text.RegularExpressions;

namespace NDFToCopilotStudioConverter
{
    public static class SessionMappingHelper
    {
        public static List<SessionMappingModel> ProcessSessionMappings(IEnumerable<XElement> sessionMappingElements)
        {
            var sessionMappings = new List<SessionMappingModel>();

            foreach (var sessionMapping in sessionMappingElements)
            {
                string key = sessionMapping.Attribute("key")?.Value ?? sessionMapping.Attribute("name").Value;
                string value = sessionMapping.Attribute("value")?.Value;
                string type = sessionMapping.Attribute("type")?.Value ?? "String";
                string expr = sessionMapping.Attribute("expr")?.Value;
                string path = sessionMapping.Attribute("path")?.Value;

                /*if (value == null)
                {
                    value = path == null ? expr : "Global." + path;
                    type = "expression";
                }*/
                if (value == null && expr != null)
                {
                    if (expr.StartsWith("'") && expr.EndsWith("'"))
                    {
                        value = expr;
                        type = "string";
                    }
                    else
                    {
                        // Prepend "Global." to each variable in the expression, but avoid replacing after dots
                        // value = Regex.Replace(expr, @"(?<=^|\+|\s)\b([a-zA-Z_][a-zA-Z0-9_]*)\b", "Global.$1");
                        if (expr.Contains(".returnvalue"))
                        {
                            expr = expr.Split(".returnvalue")[0] + "_Input";
                            value = "=Global." + expr;
                            type = "expression";
                        }
                        else
                        {
                            if (expr != null && (expr.Contains("+") || expr.Contains("-")))
                            {
                                //TODO: Adding the code to check Gloabl before bracket
                                if (expr.StartsWith("("))
                                {
                                    if (expr.IndexOf("Global", StringComparison.OrdinalIgnoreCase) >= 0)
                                    {
                                        // Case 1: Starts with '(' and contains 'Global' inside, do NOT append Global.
                                        value = "=" + expr;
                                    }
                                    else
                                    {
                                        // Case 2: Starts with '(' and does NOT contain 'Global' inside
                                        // Use regex to find all variable names (strings) inside the parentheses and prepend 'Global.' to them,
                                        // but do NOT prepend to numbers or operators.
                                        // Also, remove any 'Global.' directly before '(' if present.

                                        // Remove '=Global.(' at the start and replace with '=(' to preserve the equals sign
                                        Console.WriteLine($"Original expr: {expr}");
                                        string exprClean = Regex.Replace(expr, @"^=Global\.\(", "=(");
                                        Console.WriteLine($"After removing '=Global.(': {exprClean}");

                                        // Improved regex: match variable names at start, after (, whitespace, comma, or operator
                                        exprClean = Regex.Replace(exprClean, @"(?<=^|[\(\s,+\-*/^%=<>!&|])([a-zA-Z_][a-zA-Z0-9_]*)", match =>
                                        {
                                            string token = match.Value;
                                            if (!Regex.IsMatch(token, @"^(true|false|null|undefined|\d+)$", RegexOptions.IgnoreCase) &&
                                                !token.StartsWith("Global."))
                                            {
                                                return "Global." + token;
                                            }
                                            return token;
                                        });
                                        Console.WriteLine($"After prefixing variables: {exprClean}");

                                        value = exprClean;
                                    }
                                }
                                else
                                {
                                    // Case 3: All other cases, use current logic
                                    value = "=Global." + expr;
                                }

                                // type = "expression";
                                // value = "=Global." + expr;
                                // value = Regex.Replace(expr, @"(?<=^|\s|[+\-*/^%=<>!&|()])([a-zA-Z_][a-zA-Z0-9_]*)(?=\s|[+\-*/^%=<>!&|()])", "Global.$1");
                            }
                            else
                            {
                                // Added the below if condition to Check if the expression is a single digit or a simple variable
                                if (Regex.IsMatch(expr, @"^[0-9]$"))
                                {
                                    value = ExtractValue(expr);
                                    type = "expression";
                                }
                                else
                                {
                                    //TODO: Adding the code to check Gloabl before bracket
                                    // /*value = expr;
                                    // type = "string";*/
                                    // value = "=Global." + ExtractValue(expr);
                                    // type = "expression";
                                    if (expr.StartsWith("("))
                                    {
                                        if (expr.IndexOf("Global", StringComparison.OrdinalIgnoreCase) >= 0)
                                        {
                                            // Case 1: Starts with '(' and contains 'Global' inside, do NOT append Global.
                                            value = "=" + ExtractValue(expr);
                                        }
                                        else
                                        {
                                            // Case 2: Starts with '(' and does NOT contain 'Global' inside
                                            // Use regex to find all variable names (strings) inside the parentheses and prepend 'Global.' to them,
                                            // but do NOT prepend to numbers or operators.
                                            // Also, remove any 'Global.' directly before '(' if present.

                                            // Remove '=Global.(' at the start and replace with '=(' to preserve the equals sign
                                            Console.WriteLine($"Original expr: {expr}");
                                            string exprClean = Regex.Replace(expr, @"^=Global\.\(", "=(");
                                            Console.WriteLine($"After removing '=Global.(': {exprClean}");

                                            // Improved regex: match variable names at start, after (, whitespace, comma, or operator
                                            exprClean = Regex.Replace(exprClean, @"(?<=^|[\(\s,+\-*/^%=<>!&|])([a-zA-Z_][a-zA-Z0-9_]*)", match =>
                                            {
                                                string token = match.Value;
                                                if (!Regex.IsMatch(token, @"^(true|false|null|undefined|\d+)$", RegexOptions.IgnoreCase) &&
                                                    !token.StartsWith("Global."))
                                                {
                                                    return "Global." + token;
                                                }
                                                return token;
                                            });
                                            Console.WriteLine($"After prefixing variables: {exprClean}");

                                            value = exprClean;
                                        }
                                    }
                                    else
                                    {
                                        // Case 3: All other cases, use current logic
                                        value = "=Global." + ExtractValue(expr);
                                    }


                                }

                            }
                        }
                    }
                }


                if (key != null && value != null)
                {
                    sessionMappings.Add(new SessionMappingModel
                    {
                        key = StateUtility.SimplifyCondition(key),
                        value = StateUtility.SimplifyCondition(value),
                        type = type
                    });
                }
            }

            return sessionMappings;
        }

        static string ExtractValue(string input)
        {
            if (!string.IsNullOrEmpty(input))
            {
                // Regular expression to match ${...} or {...}
                Regex regex = new Regex(@"\$\{(.*?)\}|\{(.*?)\}");

                // Extract matched values
                MatchCollection matches = regex.Matches(input);

                if (matches.Count > 0)
                {
                    // Join all matches into a single string separated by spaces (optional)
                    return string.Join(" ", matches.Select(m => m.Groups[1].Value + m.Groups[2].Value));
                }

                return input; // If no match is found, return the original string
            }
            return input;
        }

    }
}
