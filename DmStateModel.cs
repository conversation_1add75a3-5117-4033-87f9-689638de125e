﻿using System;
using System.Collections.Generic;
using XmlProcessing;

namespace NDFToCopilotStudioConverter
{
    public class DmStateModel : StateModel
    {
        public String dmType { get; set; }
        public SuccessModel success { get; set; }
        public List<EventModel> EventList { get; set; }
        public CollectionConfigurationModel CollectionConfiguration { get; set; }
        public GlobalConfigurationModel GlobalConfiguration { get; set; }

        public List<SessionMappingModel> SessionMappings { get; set; }

        public DmStateModel()
        {
            EventList = new List<EventModel>();
            SessionMappings = new List<SessionMappingModel>(); ;
        }

        public override void DisplayDetails()
        {
            Console.WriteLine($"DM State ID: {Id}");
            Console.WriteLine($"Type: {Type}");

            if (success != null)
            {
                Console.WriteLine("Success State:");
                foreach (var action in success.ActionList)
                {
                    Console.WriteLine($"  Action: {action.Name} : {action.Next}");
                    foreach (var sessionMapping in action.SessionMappingList)
                    {
                        Console.WriteLine($"    Session Mapping: {sessionMapping.key} = {sessionMapping.value}");
                    }
                   
                    Console.WriteLine("Conditions and Transitions:");
                    for (int i = 0; i < action.ConditionTransitionsList.Count; i++)
                    {
                        Console.WriteLine($"-- If Block {i + 1} --");
                        foreach (var conditionModel in action.ConditionTransitionsList[i])
                        {
                            DisplayConditionDetails(conditionModel, 1); // Start with level 1 indentation
                        }
                    }
                    
                }
            }
            if (EventList != null)
            {
                foreach (var eventModel in EventList)
                {
                    Console.WriteLine($"Event Name: {eventModel.Name}");
                    Console.WriteLine($"Next: {eventModel.Next}");
                    Console.WriteLine("Session Mappings:");
                    /*foreach (var sessionMapping in eventModel.SessionMappingList)
                    {
                        Console.WriteLine($"    Key: {sessionMapping.key}, Value: {sessionMapping.value}");
                    }
                    Console.WriteLine("Transitions:");
                    foreach (var transition in eventModel.TransitionList)
                    {
                        Console.WriteLine($"    Next: {transition.next}");
                        foreach (var sessionMapping in transition.sessionMappings)
                        {
                            Console.WriteLine($"        Key: {sessionMapping.key}, Value: {sessionMapping.value}");
                        }
                    }*/
                }
            }

            if (CollectionConfiguration != null)
            {
                Console.WriteLine("Collection Configuration:");
                Console.WriteLine($"  Confirmationmode: {CollectionConfiguration.Confirmationmode}");
                Console.WriteLine($"  Highconfidencelevel: {CollectionConfiguration.Highconfidencelevel}");
                Console.WriteLine($"  Inputmodes: {CollectionConfiguration.Inputmodes}");

                if (CollectionConfiguration.ThresholdConfiguration != null)
                {
                    Console.WriteLine("  Threshold Configuration:");
                    Console.WriteLine($"    MaxInvalidAnswers: {CollectionConfiguration.ThresholdConfiguration.Maxinvalidanswers}");
                    Console.WriteLine($"    MaxTurns: {CollectionConfiguration.ThresholdConfiguration.Maxturns}");
                    Console.WriteLine($"    MaxNoInputs: {CollectionConfiguration.ThresholdConfiguration.Maxnoinputs}");
                    Console.WriteLine($"    MaxNoMatches: {CollectionConfiguration.ThresholdConfiguration.Maxnomatches}");
                    Console.WriteLine($"    MaxRepeats: {CollectionConfiguration.ThresholdConfiguration.Maxrepeats}");
                    Console.WriteLine($"    MaxHelps: {CollectionConfiguration.ThresholdConfiguration.Maxhelps}");
                }

                if (CollectionConfiguration.VxmlProperties != null)
                {
                    Console.WriteLine("  vxmlProperties:");
                    Console.WriteLine($"    confidencelevel: {CollectionConfiguration.VxmlProperties.confidencelevel}");
                    Console.WriteLine($"    timeout: {CollectionConfiguration.VxmlProperties.timeout}");
                    Console.WriteLine($"    incompletetimeout: {CollectionConfiguration.VxmlProperties.incompletetimeout}");
                    Console.WriteLine($"    maxspeechtimeout: {CollectionConfiguration.VxmlProperties.maxspeechtimeout}");
                    Console.WriteLine($"    termtimeout: {CollectionConfiguration.VxmlProperties.termtimeout}");
                    Console.WriteLine($"    interdigittimeout: {CollectionConfiguration.VxmlProperties.interdigittimeout}");
                    Console.WriteLine($"    inputmodes: {CollectionConfiguration.VxmlProperties.inputmodes}");
                }

                if (CollectionConfiguration.PromptConfiguration != null)
                {
                    Console.WriteLine("  Prompt Configuration:");
                    if (CollectionConfiguration.PromptConfiguration.InitialPrompt != null)
                    {
                        Console.WriteLine("    Initial Prompt:");
                        /*foreach (var entry in CollectionConfiguration.PromptConfiguration.InitialPrompt.ConditionPrompts)
                        {
                            Console.WriteLine($"      Condition: {entry.Key}");
                            foreach (var prompt in entry.Value)
                            {
                                Console.WriteLine($"        Prompt ID: {prompt.PromptId}");
                                Console.WriteLine($"        Text: {prompt.Text}");
                            }
                        }
                        Console.WriteLine("Conditions and Prompt:");*/
                        for (int i = 0; i < CollectionConfiguration.PromptConfiguration.InitialPrompt.ConditionPromptList.Count; i++)
                        {
                            Console.WriteLine($"-- If Block {i + 1} --");
                            foreach (var conditionModel in CollectionConfiguration.PromptConfiguration.InitialPrompt.ConditionPromptList[i])
                            {
                                DisplayConditionPromptDetails(conditionModel, 1); // Start with level 1 indentation
                            }
                        }
                    }
                    if (CollectionConfiguration.PromptConfiguration.RepeatPrompts != null)
                    {
                        Console.WriteLine("    Repeat Prompts:");
                        foreach (var entry in CollectionConfiguration.PromptConfiguration.RepeatPrompts.ConditionPrompts)
                        {
                            Console.WriteLine($"      Condition: {entry.Key}");
                            foreach (var prompt in entry.Value)
                            {
                                Console.WriteLine($"        Prompt ID: {prompt.PromptId}");
                                Console.WriteLine($"        Text: {prompt.Text}");
                            }
                        }
                    }
                    if (CollectionConfiguration.PromptConfiguration.NomatchPrompts != null)
                    {
                        Console.WriteLine("    No Match Prompts:");
                        foreach (var entry in CollectionConfiguration.PromptConfiguration.NomatchPrompts)
                        {
                            Console.WriteLine("    No Match Prompt:");
                            /*foreach (var noMatchPrompt in entry.ConditionPrompts)
                            {

                                Console.WriteLine($"      Condition: {noMatchPrompt.Key}");

                                foreach (var prompt in noMatchPrompt.Value)
                                {
                                    Console.WriteLine($"        Prompt ID: {prompt.PromptId}");
                                    Console.WriteLine($"        Text: {prompt.Text}");
                                }
                            }*/
                            for (int i = 0; i < entry.ConditionPromptList.Count; i++)
                            {
                                Console.WriteLine($"-- If Block {i + 1} --");
                                foreach (var conditionModel in entry.ConditionPromptList[i])
                                {
                                    DisplayConditionPromptDetails(conditionModel, 1); // Start with level 1 indentation
                                }
                            }
                        }
                    }
                    if (CollectionConfiguration.PromptConfiguration.NoinputPrompts != null)
                    {
                        Console.WriteLine("    No input Prompts:");
                        foreach (var entry in CollectionConfiguration.PromptConfiguration.NoinputPrompts)
                        {
                            Console.WriteLine("    No input Prompt:");
                            /*foreach (var NoInputPrompts in entry.ConditionPrompts)
                            {

                                Console.WriteLine($"      Condition: {NoInputPrompts.Key}");

                                foreach (var prompt in NoInputPrompts.Value)
                                {
                                    Console.WriteLine($"        Prompt ID: {prompt.PromptId}");
                                    Console.WriteLine($"        Text: {prompt.Text}");
                                }
                            }*/
                            for (int i = 0; i < entry.ConditionPromptList.Count; i++)
                            {
                                Console.WriteLine($"-- If Block {i + 1} --");
                                foreach (var conditionModel in entry.ConditionPromptList[i])
                                {
                                    DisplayConditionPromptDetails(conditionModel, 1); // Start with level 1 indentation
                                }
                            }
                        }
                    }

                    if (GlobalConfiguration != null)
                    {
                        Console.WriteLine("Global Configuration:");
                        Console.WriteLine($"  Confirmationmode: {GlobalConfiguration.Confirmationmode}");

                        foreach (var failurePrompt in GlobalConfiguration.Failureprompts)
                        {
                            Console.WriteLine("  Failure Prompt:");
                            Console.WriteLine($"    Count: {failurePrompt.Count}");
                            Console.WriteLine($"    Filename: {failurePrompt.Filename}");
                            Console.WriteLine($"    Text: {failurePrompt.Text}");
                            Console.WriteLine($"    Id: {failurePrompt.Id}");
                        }

                        foreach (var successPrompt in GlobalConfiguration.SuccessPrompts)
                        {
                            Console.WriteLine("  Success Prompt:");
                            Console.WriteLine($"    Count: {successPrompt.Count}");
                            Console.WriteLine($"    Filename: {successPrompt.Filename}");
                            Console.WriteLine($"    Text: {successPrompt.Text}");
                            Console.WriteLine($"    Id: {successPrompt.Id}");
                        }

                        foreach (var successCorrectedPrompt in GlobalConfiguration.Successcorrectedprompt)
                        {
                            Console.WriteLine("  Success Corrected Prompt:");
                            Console.WriteLine($"    Count: {successCorrectedPrompt.Count}");
                            Console.WriteLine($"    Filename: {successCorrectedPrompt.Filename}");
                            Console.WriteLine($"    Text: {successCorrectedPrompt.Text}");
                            Console.WriteLine($"    Id: {successCorrectedPrompt.Id}");
                        }
                    }

                }
            }
            Console.WriteLine();
        }

        private void DisplayConditionDetails(ConditionModel conditionModel, int level)
        {
            var indent = new string(' ', level * 2); // Indentation for nested levels

            Console.WriteLine($"{indent}Condition: {conditionModel.Condition}"); // Raw condition string

            // Ensure Transitions is not null and process each transition
            var transition = conditionModel.Transitions;
            if (transition != null)
            {
                Console.WriteLine($"{indent}- Label: {transition.label}, Next: {transition.next}");

                if (transition.sessionMappings != null && transition.sessionMappings.Count > 0)
                {
                    Console.WriteLine($"{indent}  Session Mappings:");
                    foreach (var mapping in transition.sessionMappings)
                    {
                        Console.WriteLine($"{indent}  - {mapping.key}: {mapping.value}: {mapping.type}");
                    }
                }
                Console.WriteLine("Prompt text: ");
                if (transition.PromptList != null)
                {
                    foreach (var prompt in transition.PromptList)
                    {
                        Console.WriteLine(prompt.Text);
                    }
                }

                if (transition.InnerConditionTransitions != null && transition.InnerConditionTransitions.Count > 0)
                {
                    Console.WriteLine($"{indent}  Inner Conditions:");
                    foreach (var innerConditionModel in transition.InnerConditionTransitions)
                    {
                        DisplayConditionDetails(innerConditionModel, level + 1); // Recurse for inner conditions
                    }
                }
            }
        }
        private void DisplayConditionPromptDetails(ConditionModel conditionModel, int level)
        {
            var indent = new string(' ', level * 2); // Indentation for nested levels

            Console.WriteLine($"{indent}Condition: {conditionModel.Condition}"); // Raw condition string

            // Ensure Transitions is not null and process each transition
            var transition = conditionModel.Transitions;
            if (transition != null)
            {
                Console.WriteLine("Prompt text: ");
                if (transition.PromptList != null)
                {
                    foreach (var prompt in transition.PromptList)
                    {
                        Console.WriteLine(prompt.Text);
                        Console.WriteLine();
                    }
                }
                Console.WriteLine();
                if (transition.InnerConditionTransitions != null && transition.InnerConditionTransitions.Count > 0)
                {
                    Console.WriteLine($"{indent}  Inner Conditions:");
                    foreach (var innerConditionModel in transition.InnerConditionTransitions)
                    {
                        DisplayConditionPromptDetails(innerConditionModel, level + 1); // Recurse for inner conditions
                    }
                }
            }
        }
    }
}
