@echo off
REM Command to create a bot using PAC CLI

REM Generate a random number between 0 and 99999
set /a randomNumber=%RANDOM% %% 100000

REM Prompt user for environment choice
echo Select environment:
echo 1. Nuance
echo 2. CIVR
set /p envChoice="Enter choice (1 or 2): "

REM Set environment ID and solution name based on choice
if "%envChoice%"=="1" (
    set envId=29208c38-8fc5-4a03-89e2-9b6e8e4b388b
    set solutionName=DevTesting_NG
) else if "%envChoice%"=="2" (
    set envId=9ef80e1b-291a-e64d-b811-4324c173bc1c
    set solutionName=NDFMCSTool
) else (
    echo Invalid choice
    pause
    exit /b 1
)

REM Prompt user for template file name
set /p templateFile="Enter template file name (e.g., final.yml, delta.yml): "

REM Prompt user for display name
set /p displayName="Enter display name for the bot: "

REM Validate that the file exists
if not exist "Samples/bot_yaml/%templateFile%" (
    echo Error: File Samples/bot_yaml/%templateFile% does not exist.
    pause
    exit /b 1
)

REM Execute PAC CLI command with user-provided template file and display name
pac copilot create --environment %envId% --schemaName crd%randomNumber%_voicePp --templateFileName Samples/bot_yaml/%templateFile% --displayName %displayName% --solution %solutionName%

pause