﻿using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace NDFToCopilotStudioConverter
{
    public class DecisionStateModel : StateModel
    {
        public List<List<ConditionModel>> ConditionTransitionsList { get; set; }
        public List<SessionMappingModel> SessionMappings { get; set; }
        //Adding gotoDialogElement for adding goto if it is at last
        public string gotoDialogElement { get; set; }
        //Adding nextGoto for adding goto if it is at last
        public string nextGoto { get; set; }
        //Adding sesssion mapping for nextState 
        public List<SessionMappingModel> sessionMappings { get; set; }

        public bool label { get; set; } = false;
        public DecisionStateModel()
        {
            ConditionTransitionsList = new List<List<ConditionModel>>();
            SessionMappings = new List<SessionMappingModel>();
        }

        public override void DisplayDetails()
        {
            Console.WriteLine($"Decision State ID: {Id}");
            Console.WriteLine($"Type: {Type}");

            // Display each list of conditions (each list represents a separate if block)
            Console.WriteLine("Conditions and Transitions:");
            for (int i = 0; i < ConditionTransitionsList.Count; i++)
            {
                Console.WriteLine($"-- If Block {i + 1} --");
                foreach (var conditionModel in ConditionTransitionsList[i])
                {
                    DisplayConditionDetails(conditionModel, 1); // Start with level 1 indentation
                }
            }

            // Optionally, display session mappings at the root level
            if (SessionMappings != null && SessionMappings.Count > 0)
            {
                Console.WriteLine("Root Session Mappings:");
                foreach (var mapping in SessionMappings)
                {
                    Console.WriteLine($"- {mapping.key}: {mapping.value}: {mapping.type}");
                }
            }

            Console.WriteLine();
        }

        private void DisplayConditionDetails(ConditionModel conditionModel, int level)
        {
            var indent = new string(' ', level * 2); // Indentation for nested levels

            Console.WriteLine($"{indent}Condition: {conditionModel.Condition}"); // Raw condition string

            // Ensure Transitions is not null and process each transition
            var transition = conditionModel.Transitions;
            if (transition != null)
            {
                Console.WriteLine($"{indent}- Label: {transition.label}, Next: {transition.next}");

                if (transition.sessionMappings != null && transition.sessionMappings.Count > 0)
                {
                    Console.WriteLine($"{indent}  Session Mappings:");
                    foreach (var mapping in transition.sessionMappings)
                    {
                        Console.WriteLine($"{indent}  - {mapping.key}: {mapping.value}: {mapping.type}");
                    }
                }

                if (transition.InnerConditionTransitions != null && transition.InnerConditionTransitions.Count > 0)
                {
                    Console.WriteLine($"{indent}  Inner Conditions:");
                    foreach (var innerConditionModel in transition.InnerConditionTransitions)
                    {
                        DisplayConditionDetails(innerConditionModel, level + 1); // Recurse for inner conditions
                    }
                }
            }
        }
    }
}
