﻿using System.Text.RegularExpressions;

namespace NDFToCopilotStudioConverter
{
    public static class YamlSuffixReplacer
    {
        public static string ReplaceYamlSuffix(string yamlContent)
        {
            // Mapping of suffix replacements
            Dictionary<string, string> replacements = new Dictionary<string, string>
        {
            { "_PP", "_MS" },
            { "_DS", "_CD" },
            { "_DM", "_QA" },
            { "_SD", "_RD" },
            { "_XR", "_RD" },
            { "_DB", "_HT" },
            { "_DA", "_HT" },
            { "_SD_return", "_RD_return" },
            { "_XR_return", "_RD_return" }
        };

            // Perform replacements
            foreach (var pair in replacements)
            {
                string pattern = $@"{Regex.Escape(pair.Key)}\b"; // Match only at the end of a word
                yamlContent = Regex.Replace(yamlContent, pattern, pair.Value);
            }

            // Special case: Replace "_DM_" with "_QA_"
            yamlContent = yamlContent.Replace("_DM_", "_QA_");

            // Remove unwanted 'kind: CustomEntityComponent' lines from entity definitions
            yamlContent = RemoveCustomEntityComponentKind(yamlContent);

            return ReplaceYamlInBetween(yamlContent);
        }

        public static string ReplaceYamlInBetween(string yamlContent)
        {
            // Mapping of in-between replacements
            Dictionary<string, string> inBetweenReplacements = new Dictionary<string, string>
        {
            { "_PP_", "_MS_" },
            { "_DS_", "_CD_" },
            { "_DM_", "_QA_" },
            { "_SD_", "_RD_" },
            { "_DB_", "_DA_" }
        };

            // Perform replacements
            foreach (var pair in inBetweenReplacements)
            {
                yamlContent = yamlContent.Replace(pair.Key, pair.Value);
            }

            return yamlContent;
        }

        /// <summary>
        /// Removes 'kind: CustomEntityComponent' lines from entity definitions in YAML content.
        /// This ensures entities have the correct structure without the unwanted kind field.
        /// </summary>
        /// <param name="yamlContent">The YAML content to process</param>
        /// <returns>YAML content with CustomEntityComponent kind lines removed</returns>
        private static string RemoveCustomEntityComponentKind(string yamlContent)
        {
            // Pattern to match 'kind: CustomEntityComponent' lines (with optional leading whitespace)
            string pattern = @"^\s*-?\s*kind:\s*CustomEntityComponent\s*$";

            // Remove all matching lines
            yamlContent = Regex.Replace(yamlContent, pattern, "", RegexOptions.Multiline);

            // Clean up any resulting empty lines or extra whitespace
            yamlContent = Regex.Replace(yamlContent, @"\n\s*\n\s*\n", "\n\n", RegexOptions.Multiline);

            return yamlContent;
        }
    }


}
