{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"MixToCopilotStudioConverter/1.0.0": {"dependencies": {"AdaptiveCards": "3.1.0", "AdaptiveCards.Templating": "2.0.5", "Azure.Identity": "1.13.2", "ExternalGrammarConverter": "1.0.0-***********-194847", "Microsoft.Bot.Builder": "4.23.0", "Microsoft.Bot.Builder.Dialogs": "4.23.0", "Microsoft.Bot.Builder.Dialogs.Adaptive": "4.23.0", "Microsoft.Bot.ObjectModel": "2025.4.3-3", "Microsoft.Bot.ObjectModel.Dataverse": "2025.4.3-3", "Microsoft.Bot.ObjectModel.NodeGenerators": "2025.4.3-3", "NuanceMix": "1.0.0", "System.IO.Packaging": "9.0.4", "System.Text.Json": "9.0.4", "YamlDotNet": "16.3.0"}, "runtime": {"MixToCopilotStudioConverter.dll": {}}}, "AdaptiveCards/3.1.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/AdaptiveCards.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.2309.26002"}}}, "AdaptiveCards.Templating/2.0.5": {"dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Microsoft.Bot.AdaptiveExpressions.Core": "4.22.9", "PolySharp": "1.14.1", "System.Text.Json": "9.0.4"}, "runtime": {"lib/net8.0/AdaptiveCards.Templating.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AdaptiveExpressions/4.23.0": {"dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Microsoft.CSharp": "4.7.0", "Microsoft.Recognizers.Text.DataTypes.TimexExpression": "1.3.2", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/AdaptiveExpressions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Antlr4.Runtime.Standard/4.13.1": {"runtime": {"lib/netstandard2.0/Antlr4.Runtime.Standard.dll": {"assemblyVersion": "4.13.1.0", "fileVersion": "4.13.1.0"}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "9.0.4", "System.Text.Json": "9.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "Azure.Identity/1.13.2": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"assemblyVersion": "1.13.2.0", "fileVersion": "1.1300.225.6404"}}}, "ExternalGrammarConverter/1.0.0-***********-194847": {"dependencies": {"Microsoft.Bot.ObjectModel": "2025.4.3-3", "Microsoft.Bot.ObjectModel.NodeGenerators": "2025.4.3-3"}, "runtime": {"lib/net8.0/ExternalGrammarConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Google.Protobuf/3.25.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.25.1.0", "fileVersion": "3.25.1.0"}}}, "Grpc.Core/2.46.6": {"dependencies": {"Grpc.Core.Api": "2.46.6", "System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/Grpc.Core.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.46.6.0"}}, "runtimeTargets": {"runtimes/linux-arm64/native/libgrpc_csharp_ext.arm64.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libgrpc_csharp_ext.x64.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libgrpc_csharp_ext.x64.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/grpc_csharp_ext.x64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/grpc_csharp_ext.x86.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Grpc.Core.Api/2.46.6": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.46.6.0"}}}, "Json.More.Net/2.0.1.2": {"runtime": {"lib/net8.0/Json.More.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.1.2"}}}, "JsonPath.Net/1.1.0": {"dependencies": {"Json.More.Net": "2.0.1.2"}, "runtime": {"lib/net8.0/JsonPath.Net.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Bcl.HashCode/1.1.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Bcl.TimeProvider/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.123.58001"}}}, "Microsoft.Bot.AdaptiveExpressions.Core/4.22.9": {"dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "JsonPath.Net": "1.1.0", "Microsoft.CSharp": "4.7.0", "Microsoft.Recognizers.Text.DataTypes.TimexExpression": "1.3.2", "System.Text.Json": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.AdaptiveExpressions.Core.dll": {"assemblyVersion": "4.22.9.0", "fileVersion": "4.22.9.0"}}}, "Microsoft.Bot.Builder/4.23.0": {"dependencies": {"Microsoft.Bot.Connector": "4.23.0", "Microsoft.Bot.Connector.Streaming": "4.23.0", "Microsoft.Bot.Streaming": "4.23.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Builder.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Builder.Dialogs/4.23.0": {"dependencies": {"Microsoft.Bot.Builder": "4.23.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Recognizers.Text.Choice": "1.3.2", "Microsoft.Recognizers.Text.DateTime": "1.3.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Builder.Dialogs.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Builder.Dialogs.Adaptive/4.23.0": {"dependencies": {"Microsoft.Bot.Builder.Dialogs.Declarative": "4.23.0", "Microsoft.Bot.Builder.LanguageGeneration": "4.23.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Recognizers.Text": "1.3.2", "Microsoft.Recognizers.Text.Choice": "1.3.2", "Microsoft.Recognizers.Text.DateTime": "1.3.2", "Microsoft.Recognizers.Text.Number": "1.3.2", "Microsoft.Recognizers.Text.Sequence": "1.3.2", "System.Text.Json": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Builder.Dialogs.Adaptive.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Builder.Dialogs.Declarative/4.23.0": {"dependencies": {"AdaptiveExpressions": "4.23.0", "Microsoft.Bot.Builder.Dialogs": "4.23.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.10.1", "System.Formats.Asn1": "8.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Builder.Dialogs.Declarative.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Builder.LanguageGeneration/4.23.0": {"dependencies": {"AdaptiveExpressions": "4.23.0", "Antlr4.Runtime.Standard": "4.13.1", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Builder.LanguageGeneration.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Connector/4.23.0": {"dependencies": {"Microsoft.Bot.Schema": "4.23.0", "Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Web.Certificateless": "3.3.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.1.2", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Connector.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Connector.Streaming/4.23.0": {"dependencies": {"Microsoft.Bot.Schema": "4.23.0", "Microsoft.Bot.Streaming": "4.23.0", "Microsoft.Extensions.Logging": "8.0.0", "Newtonsoft.Json": "13.0.3", "System.IO.Pipelines": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Connector.Streaming.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.ObjectModel/2025.4.3-3": {"dependencies": {"Microsoft.Bcl.HashCode": "1.1.0", "System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.ObjectModel.dll": {"assemblyVersion": "2025.4.3.3", "fileVersion": "2025.4.3.3"}}, "resources": {"lib/netstandard2.0/cs-CZ/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "cs-CZ"}, "lib/netstandard2.0/da-DK/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "da-DK"}, "lib/netstandard2.0/de-DE/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "de-DE"}, "lib/netstandard2.0/el-GR/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "el-GR"}, "lib/netstandard2.0/es-ES/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "es-ES"}, "lib/netstandard2.0/fi-FI/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "fi-FI"}, "lib/netstandard2.0/fr-FR/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "fr-FR"}, "lib/netstandard2.0/hi-IN/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "hi-IN"}, "lib/netstandard2.0/id-ID/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "id-ID"}, "lib/netstandard2.0/it-IT/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "it-IT"}, "lib/netstandard2.0/ja-JP/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "ja-<PERSON>"}, "lib/netstandard2.0/ko-KR/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "ko-KR"}, "lib/netstandard2.0/nb-NO/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "nb-NO"}, "lib/netstandard2.0/nl-NL/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "nl-NL"}, "lib/netstandard2.0/pl-PL/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "pl-PL"}, "lib/netstandard2.0/pt-BR/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru-RU/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "ru-RU"}, "lib/netstandard2.0/sv-SE/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "sv-SE"}, "lib/netstandard2.0/th-TH/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "th-TH"}, "lib/netstandard2.0/tr-TR/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "tr-TR"}, "lib/netstandard2.0/zh-CN/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "zh-CN"}, "lib/netstandard2.0/zh-TW/Microsoft.Bot.ObjectModel.resources.dll": {"locale": "zh-TW"}}}, "Microsoft.Bot.ObjectModel.Dataverse/2025.4.3-3": {"dependencies": {"Microsoft.Bot.ObjectModel": "2025.4.3-3", "Microsoft.Bot.ObjectModel.Json": "2025.4.3-3", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.ObjectModel.Dataverse.dll": {"assemblyVersion": "2025.4.3.3", "fileVersion": "2025.4.3.3"}}}, "Microsoft.Bot.ObjectModel.Json/2025.4.3-3": {"dependencies": {"Microsoft.Bot.ObjectModel": "2025.4.3-3", "System.Memory": "4.5.5", "System.Text.Json": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.ObjectModel.Json.dll": {"assemblyVersion": "2025.4.3.3", "fileVersion": "2025.4.3.3"}}}, "Microsoft.Bot.ObjectModel.NodeGenerators/2025.4.3-3": {"dependencies": {"Microsoft.Bot.ObjectModel": "2025.4.3-3", "Microsoft.Bot.ObjectModel.Json": "2025.4.3-3", "Microsoft.Bot.ObjectModel.PowerFx": "2025.4.3-3", "Microsoft.Bot.Schema": "4.23.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.ObjectModel.NodeGenerators.dll": {"assemblyVersion": "2025.4.3.3", "fileVersion": "2025.4.3.3"}}}, "Microsoft.Bot.ObjectModel.PowerFx/2025.4.3-3": {"dependencies": {"Microsoft.Bot.ObjectModel": "2025.4.3-3", "Microsoft.Bot.ObjectModel.Json": "2025.4.3-3", "Microsoft.PowerFx.Interpreter": "1.3.0-build.20250212-1002", "Microsoft.PowerFx.Json": "1.3.0-build.20250212-1002", "Microsoft.PowerFx.LanguageServerProtocol": "1.3.0-build.20250212-1002"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.ObjectModel.PowerFx.dll": {"assemblyVersion": "2025.4.3.3", "fileVersion": "2025.4.3.3"}}}, "Microsoft.Bot.Schema/4.23.0": {"dependencies": {"AdaptiveCards": "3.1.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Schema.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Streaming/4.23.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.Bot.Streaming.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Caching.Abstractions/2.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/2.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "9.0.4"}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Identity.Client/4.67.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.1.2", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.67.2.0", "fileVersion": "4.67.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"dependencies": {"Microsoft.Identity.Client": "4.67.2", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.67.2.0", "fileVersion": "4.67.2.0"}}}, "Microsoft.Identity.Web.Certificateless/3.3.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Identity.Client": "4.67.2", "Microsoft.IdentityModel.JsonWebTokens": "8.1.2", "System.Text.Json": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Web.Certificateless.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "Microsoft.IdentityModel.Abstractions/8.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.1.2": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Logging/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Protocols/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.1.2", "System.IdentityModel.Tokens.Jwt": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.IdentityModel.Tokens/8.1.2": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Logging": "8.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "Microsoft.PowerFx.Core/1.3.0-build.20250212-1002": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.PowerFx.Transport.Attributes": "1.3.0-build.20250212-1002", "System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.PowerFx.Core.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}, "resources": {"lib/netstandard2.0/bg-BG/Microsoft.PowerFx.Core.resources.dll": {"locale": "bg-BG"}, "lib/netstandard2.0/ca-ES/Microsoft.PowerFx.Core.resources.dll": {"locale": "ca-ES"}, "lib/netstandard2.0/cs-CZ/Microsoft.PowerFx.Core.resources.dll": {"locale": "cs-CZ"}, "lib/netstandard2.0/da-DK/Microsoft.PowerFx.Core.resources.dll": {"locale": "da-DK"}, "lib/netstandard2.0/de-DE/Microsoft.PowerFx.Core.resources.dll": {"locale": "de-DE"}, "lib/netstandard2.0/el-GR/Microsoft.PowerFx.Core.resources.dll": {"locale": "el-GR"}, "lib/netstandard2.0/en-US/Microsoft.PowerFx.Core.resources.dll": {"locale": "en-US"}, "lib/netstandard2.0/es-ES/Microsoft.PowerFx.Core.resources.dll": {"locale": "es-ES"}, "lib/netstandard2.0/et-EE/Microsoft.PowerFx.Core.resources.dll": {"locale": "et-EE"}, "lib/netstandard2.0/eu-ES/Microsoft.PowerFx.Core.resources.dll": {"locale": "eu-ES"}, "lib/netstandard2.0/fi-FI/Microsoft.PowerFx.Core.resources.dll": {"locale": "fi-FI"}, "lib/netstandard2.0/fr-FR/Microsoft.PowerFx.Core.resources.dll": {"locale": "fr-FR"}, "lib/netstandard2.0/gl-ES/Microsoft.PowerFx.Core.resources.dll": {"locale": "gl-ES"}, "lib/netstandard2.0/hi-IN/Microsoft.PowerFx.Core.resources.dll": {"locale": "hi-IN"}, "lib/netstandard2.0/hr-HR/Microsoft.PowerFx.Core.resources.dll": {"locale": "hr-HR"}, "lib/netstandard2.0/hu-HU/Microsoft.PowerFx.Core.resources.dll": {"locale": "hu-HU"}, "lib/netstandard2.0/id-ID/Microsoft.PowerFx.Core.resources.dll": {"locale": "id-ID"}, "lib/netstandard2.0/it-IT/Microsoft.PowerFx.Core.resources.dll": {"locale": "it-IT"}, "lib/netstandard2.0/ja-JP/Microsoft.PowerFx.Core.resources.dll": {"locale": "ja-<PERSON>"}, "lib/netstandard2.0/kk-KZ/Microsoft.PowerFx.Core.resources.dll": {"locale": "kk-KZ"}, "lib/netstandard2.0/ko-KR/Microsoft.PowerFx.Core.resources.dll": {"locale": "ko-KR"}, "lib/netstandard2.0/lt-LT/Microsoft.PowerFx.Core.resources.dll": {"locale": "lt-LT"}, "lib/netstandard2.0/lv-LV/Microsoft.PowerFx.Core.resources.dll": {"locale": "lv-LV"}, "lib/netstandard2.0/ms-MY/Microsoft.PowerFx.Core.resources.dll": {"locale": "ms-MY"}, "lib/netstandard2.0/nb-NO/Microsoft.PowerFx.Core.resources.dll": {"locale": "nb-NO"}, "lib/netstandard2.0/nl-NL/Microsoft.PowerFx.Core.resources.dll": {"locale": "nl-NL"}, "lib/netstandard2.0/pl-PL/Microsoft.PowerFx.Core.resources.dll": {"locale": "pl-PL"}, "lib/netstandard2.0/pt-BR/Microsoft.PowerFx.Core.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/pt-PT/Microsoft.PowerFx.Core.resources.dll": {"locale": "pt-PT"}, "lib/netstandard2.0/ro-RO/Microsoft.PowerFx.Core.resources.dll": {"locale": "ro-RO"}, "lib/netstandard2.0/ru-RU/Microsoft.PowerFx.Core.resources.dll": {"locale": "ru-RU"}, "lib/netstandard2.0/sk-SK/Microsoft.PowerFx.Core.resources.dll": {"locale": "sk-SK"}, "lib/netstandard2.0/sl-SI/Microsoft.PowerFx.Core.resources.dll": {"locale": "sl-SI"}, "lib/netstandard2.0/sr-Cyrl-RS/Microsoft.PowerFx.Core.resources.dll": {"locale": "sr-Cyrl-RS"}, "lib/netstandard2.0/sr-Latn-RS/Microsoft.PowerFx.Core.resources.dll": {"locale": "sr-Latn-RS"}, "lib/netstandard2.0/sv-SE/Microsoft.PowerFx.Core.resources.dll": {"locale": "sv-SE"}, "lib/netstandard2.0/th-TH/Microsoft.PowerFx.Core.resources.dll": {"locale": "th-TH"}, "lib/netstandard2.0/tr-TR/Microsoft.PowerFx.Core.resources.dll": {"locale": "tr-TR"}, "lib/netstandard2.0/uk-UA/Microsoft.PowerFx.Core.resources.dll": {"locale": "uk-UA"}, "lib/netstandard2.0/vi-VN/Microsoft.PowerFx.Core.resources.dll": {"locale": "vi-VN"}, "lib/netstandard2.0/zh-CN/Microsoft.PowerFx.Core.resources.dll": {"locale": "zh-CN"}, "lib/netstandard2.0/zh-TW/Microsoft.PowerFx.Core.resources.dll": {"locale": "zh-TW"}}}, "Microsoft.PowerFx.Interpreter/1.3.0-build.20250212-1002": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.PowerFx.Core": "1.3.0-build.20250212-1002", "System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.PowerFx.Interpreter.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}, "resources": {"lib/netstandard2.0/bg-BG/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "bg-BG"}, "lib/netstandard2.0/ca-ES/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "ca-ES"}, "lib/netstandard2.0/cs-CZ/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "cs-CZ"}, "lib/netstandard2.0/da-DK/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "da-DK"}, "lib/netstandard2.0/de-DE/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "de-DE"}, "lib/netstandard2.0/el-GR/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "el-GR"}, "lib/netstandard2.0/en-US/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "en-US"}, "lib/netstandard2.0/es-ES/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "es-ES"}, "lib/netstandard2.0/et-EE/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "et-EE"}, "lib/netstandard2.0/eu-ES/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "eu-ES"}, "lib/netstandard2.0/fi-FI/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "fi-FI"}, "lib/netstandard2.0/fr-FR/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "fr-FR"}, "lib/netstandard2.0/gl-ES/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "gl-ES"}, "lib/netstandard2.0/hi-IN/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "hi-IN"}, "lib/netstandard2.0/hr-HR/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "hr-HR"}, "lib/netstandard2.0/hu-HU/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "hu-HU"}, "lib/netstandard2.0/id-ID/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "id-ID"}, "lib/netstandard2.0/it-IT/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "it-IT"}, "lib/netstandard2.0/ja-JP/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "ja-<PERSON>"}, "lib/netstandard2.0/kk-KZ/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "kk-KZ"}, "lib/netstandard2.0/ko-KR/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "ko-KR"}, "lib/netstandard2.0/lt-LT/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "lt-LT"}, "lib/netstandard2.0/lv-LV/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "lv-LV"}, "lib/netstandard2.0/ms-MY/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "ms-MY"}, "lib/netstandard2.0/nb-NO/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "nb-NO"}, "lib/netstandard2.0/nl-NL/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "nl-NL"}, "lib/netstandard2.0/pl-PL/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "pl-PL"}, "lib/netstandard2.0/pt-BR/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/pt-PT/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "pt-PT"}, "lib/netstandard2.0/ro-RO/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "ro-RO"}, "lib/netstandard2.0/ru-RU/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "ru-RU"}, "lib/netstandard2.0/sk-SK/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "sk-SK"}, "lib/netstandard2.0/sl-SI/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "sl-SI"}, "lib/netstandard2.0/sr-Cyrl-RS/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "sr-Cyrl-RS"}, "lib/netstandard2.0/sr-Latn-RS/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "sr-Latn-RS"}, "lib/netstandard2.0/sv-SE/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "sv-SE"}, "lib/netstandard2.0/th-TH/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "th-TH"}, "lib/netstandard2.0/tr-TR/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "tr-TR"}, "lib/netstandard2.0/uk-UA/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "uk-UA"}, "lib/netstandard2.0/vi-VN/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "vi-VN"}, "lib/netstandard2.0/zh-CN/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "zh-CN"}, "lib/netstandard2.0/zh-TW/Microsoft.PowerFx.Interpreter.resources.dll": {"locale": "zh-TW"}}}, "Microsoft.PowerFx.Json/1.3.0-build.20250212-1002": {"dependencies": {"Microsoft.PowerFx.Core": "1.3.0-build.20250212-1002", "System.Collections.Immutable": "6.0.0", "System.Text.Json": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.PowerFx.Json.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "Microsoft.PowerFx.LanguageServerProtocol/1.3.0-build.20250212-1002": {"dependencies": {"Microsoft.PowerFx.Core": "1.3.0-build.20250212-1002", "System.Collections.Immutable": "6.0.0", "System.Memory": "4.5.5", "System.Text.Json": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.PowerFx.LanguageServerProtocol.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "Microsoft.PowerFx.Transport.Attributes/1.3.0-build.20250212-1002": {"runtime": {"lib/netstandard2.0/Microsoft.PowerFx.Transport.Attributes.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "Microsoft.Recognizers.Text/1.3.2": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "2.0.0", "System.Collections.Immutable": "6.0.0", "System.ValueTuple": "4.4.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Recognizers.Definitions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/Microsoft.Recognizers.Text.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Recognizers.Text.Choice/1.3.2": {"dependencies": {"Microsoft.Recognizers.Text": "1.3.2", "System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Recognizers.Text.Choice.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Recognizers.Text.DataTypes.TimexExpression/1.3.2": {"runtime": {"lib/netstandard2.0/Microsoft.Recognizers.Text.DataTypes.TimexExpression.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Recognizers.Text.DateTime/1.3.2": {"dependencies": {"Microsoft.Recognizers.Text": "1.3.2", "Microsoft.Recognizers.Text.Number": "1.3.2", "Microsoft.Recognizers.Text.NumberWithUnit": "1.3.2", "System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Recognizers.Text.DateTime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Recognizers.Text.Number/1.3.2": {"dependencies": {"Microsoft.Recognizers.Text": "1.3.2", "System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Recognizers.Text.Number.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Recognizers.Text.NumberWithUnit/1.3.2": {"dependencies": {"Microsoft.Recognizers.Text": "1.3.2", "Microsoft.Recognizers.Text.Number": "1.3.2", "System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Recognizers.Text.NumberWithUnit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Recognizers.Text.Sequence/1.3.2": {"dependencies": {"Microsoft.Recognizers.Text": "1.3.2", "System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Recognizers.Text.Sequence.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Rest.ClientRuntime/2.3.24": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Rest.ClientRuntime.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.3.23.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "NuGet.Common/6.10.1": {"dependencies": {"NuGet.Frameworks": "6.10.1"}, "runtime": {"lib/netstandard2.0/NuGet.Common.dll": {"assemblyVersion": "6.10.1.5", "fileVersion": "6.10.1.5"}}}, "NuGet.Configuration/6.10.1": {"dependencies": {"NuGet.Common": "6.10.1", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "runtime": {"lib/netstandard2.0/NuGet.Configuration.dll": {"assemblyVersion": "6.10.1.5", "fileVersion": "6.10.1.5"}}}, "NuGet.Frameworks/6.10.1": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "6.10.1.5", "fileVersion": "6.10.1.5"}}}, "NuGet.Packaging/6.10.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "NuGet.Configuration": "6.10.1", "NuGet.Versioning": "6.10.1", "System.Security.Cryptography.Pkcs": "6.0.4"}, "runtime": {"lib/net5.0/NuGet.Packaging.dll": {"assemblyVersion": "6.10.1.5", "fileVersion": "6.10.1.5"}}}, "NuGet.Versioning/6.10.1": {"runtime": {"lib/netstandard2.0/NuGet.Versioning.dll": {"assemblyVersion": "6.10.1.5", "fileVersion": "6.10.1.5"}}}, "PolySharp/1.14.1": {}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "9.0.4"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.24.46703"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Formats.Asn1/8.0.1": {}, "System.IdentityModel.Tokens.Jwt/8.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.1.2", "Microsoft.IdentityModel.Tokens": "8.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.2.51008"}}}, "System.IO.Packaging/9.0.4": {"runtime": {"lib/net8.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.IO.Pipelines/9.0.4": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Memory/4.5.5": {}, "System.Memory.Data/6.0.0": {"dependencies": {"System.Text.Json": "9.0.4"}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.Cryptography.Pkcs/6.0.4": {"dependencies": {"System.Formats.Asn1": "8.0.1"}}, "System.Security.Cryptography.ProtectedData/4.5.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Text.Encodings.Web/9.0.4": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Text.Json/9.0.4": {"dependencies": {"System.IO.Pipelines": "9.0.4", "System.Text.Encodings.Web": "9.0.4"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.ValueTuple/4.4.0": {}, "YamlDotNet/16.3.0": {"runtime": {"lib/net8.0/YamlDotNet.dll": {"assemblyVersion": "1*******", "fileVersion": "********"}}}, "NuanceMix/1.0.0": {"dependencies": {"Azure.Identity": "1.13.2", "Google.Protobuf": "3.25.1", "Grpc.Core": "2.46.6", "System.IO.Packaging": "9.0.4", "System.Text.Json": "9.0.4"}, "runtime": {"NuanceMix.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"MixToCopilotStudioConverter/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AdaptiveCards/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-b+sPwH0oyAflpgxCyNPMzH92xrQjWl6GuuEBv86/VhO6iHhiWv+PtwzqMS70nOXZQRzpl9YVHXAvn+dKot5IBQ==", "path": "adaptivecards/3.1.0", "hashPath": "adaptivecards.3.1.0.nupkg.sha512"}, "AdaptiveCards.Templating/2.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ywMfDrvi5v1+H38nXRhfNZB9K963gzdOEQewaSO5MLy+z8cqubZjcCTGl7LctWWt8rnAPr3mnZLb773eIyHMFQ==", "path": "adaptivecards.templating/2.0.5", "hashPath": "adaptivecards.templating.2.0.5.nupkg.sha512"}, "AdaptiveExpressions/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-NbJE4tiyn1oHsSLy+JzaCApPnG98vIEn6jYrxG7sdf84xYnW+n/9p6pl/L9xue80BuJTXTsUP35iCL2IXmtzVQ==", "path": "adaptiveexpressions/4.23.0", "hashPath": "adaptiveexpressions.4.23.0.nupkg.sha512"}, "Antlr4.Runtime.Standard/4.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-Da5+i4kFHUseJRZGcBG5fmZGpA/Ns180ibrQMxgZzjpQOnENVvSL5gi5HZ8Ncz8/AR2WsKbOg2lMBzjz0HUQcA==", "path": "antlr4.runtime.standard/4.13.1", "hashPath": "antlr4.runtime.standard.4.13.1.nupkg.sha512"}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Identity/1.13.2": {"type": "package", "serviceable": true, "sha512": "sha512-CngQVQELdzFmsGSWyGIPIUOCrII7nApMVWxVmJCKQQrWxRXcNquCsZ+njRJRnhFUfD+KMAhpjyRCaceE4EOL6A==", "path": "azure.identity/1.13.2", "hashPath": "azure.identity.1.13.2.nupkg.sha512"}, "ExternalGrammarConverter/1.0.0-***********-194847": {"type": "package", "serviceable": true, "sha512": "sha512-+ULn+vMkc1ouepsJQC4JJ9Yjik1FRUanVpC1kGxwU4SOBL7EsVyZ3w66TlXMR//tQNO7Taer45Ah+Ibt+UabZg==", "path": "externalgrammarconverter/1.0.0-ci-20250428-194847", "hashPath": "externalgrammarconverter.1.0.0-ci-20250428-194847.nupkg.sha512"}, "Google.Protobuf/3.25.1": {"type": "package", "serviceable": true, "sha512": "sha512-Sw9bq4hOD+AaS3RrnmP5IT25cyZ/T1qpM0e8+G+23Nojhv7+ScJFPEAQo1m4EFQWhXoI4FRZDrK+wjHCPw9yxg==", "path": "google.protobuf/3.25.1", "hashPath": "google.protobuf.3.25.1.nupkg.sha512"}, "Grpc.Core/2.46.6": {"type": "package", "serviceable": true, "sha512": "sha512-ZoRg3KmOJ2urTF4+u3H0b1Yv10xzz2Y/flFWS2tnRmj8dbKLeiJaSRqu4LOBD3ova90evqLkVZ85kUkC4JT4lw==", "path": "grpc.core/2.46.6", "hashPath": "grpc.core.2.46.6.nupkg.sha512"}, "Grpc.Core.Api/2.46.6": {"type": "package", "serviceable": true, "sha512": "sha512-Z7HJGqJYyKb53qfp1jf0wRDYs3sxOnkAFxXAW6q52LLmX/zxzjtFLI9eaWO5UC0weiWjn4iT1FzR+tj9qYZAMg==", "path": "grpc.core.api/2.46.6", "hashPath": "grpc.core.api.2.46.6.nupkg.sha512"}, "Json.More.Net/2.0.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-uF3QeiaXEfH92emz0/BWUiNtMSfxIIvgynuB0Bf1vF4s8eWTcZitBx9l+g/FDaJk5XxqBv9buQXizXKQcXFG1w==", "path": "json.more.net/2.0.1.2", "hashPath": "json.more.net.2.0.1.2.nupkg.sha512"}, "JsonPath.Net/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Njbt3xuyiJ41zkut0nrKbHL7Hpxb39siV/KchPnXKVNGnhnYqIUmiWh653EfRK4lG8H+ds08bNrw5/3jl9ZC3A==", "path": "jsonpath.net/1.1.0", "hashPath": "jsonpath.net.1.1.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-J2G1k+u5unBV+aYcwxo94ip16Rkp65pgWFb0R6zwJipzWNMgvqlWeuI7/+R+e8bob66LnSG+llLJ+z8wI94cHg==", "path": "microsoft.bcl.hashcode/1.1.0", "hashPath": "microsoft.bcl.hashcode.1.1.0.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA==", "path": "microsoft.bcl.timeprovider/8.0.1", "hashPath": "microsoft.bcl.timeprovider.8.0.1.nupkg.sha512"}, "Microsoft.Bot.AdaptiveExpressions.Core/4.22.9": {"type": "package", "serviceable": true, "sha512": "sha512-hA6ewCCxRuLhS4fYYW8YA0Xez3Th3UJeAxKMiKvKVAOWYpnL1PedNdRInIAEreK2Er6od2Upjzvdr/NOzAmisA==", "path": "microsoft.bot.adaptiveexpressions.core/4.22.9", "hashPath": "microsoft.bot.adaptiveexpressions.core.4.22.9.nupkg.sha512"}, "Microsoft.Bot.Builder/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-bLTrp/tfSNWDAIJ90TqyZ8JtpjCvNO7kgvhW0R5eSu72tAifwEIi2uxhFS+c8alsa2DM4EW3JSemoDgn0r6Hog==", "path": "microsoft.bot.builder/4.23.0", "hashPath": "microsoft.bot.builder.4.23.0.nupkg.sha512"}, "Microsoft.Bot.Builder.Dialogs/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-oE/wx093M8zqB68z22+x5EvPdH4gprsZ9n0T9fUxG1iiEarRDNnJbPkWGmAhOPAI0HdBauVceYbsGargbKJ97w==", "path": "microsoft.bot.builder.dialogs/4.23.0", "hashPath": "microsoft.bot.builder.dialogs.4.23.0.nupkg.sha512"}, "Microsoft.Bot.Builder.Dialogs.Adaptive/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-FFe3x3VIARMHX4vjizgHtG19wQ1z7LAxNE+h+2VUH54XwDLhWzmvOy7i2MkNP/FhxysnnZdtaY98+vMfr7FVgw==", "path": "microsoft.bot.builder.dialogs.adaptive/4.23.0", "hashPath": "microsoft.bot.builder.dialogs.adaptive.4.23.0.nupkg.sha512"}, "Microsoft.Bot.Builder.Dialogs.Declarative/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-sRP+soveV+pMdlTDIxo1GyJvpLjUqW/fXLV9MntluHTyFMN2Ei4r30QMqTTya1cETmJQd2go/uEarExGE32ABg==", "path": "microsoft.bot.builder.dialogs.declarative/4.23.0", "hashPath": "microsoft.bot.builder.dialogs.declarative.4.23.0.nupkg.sha512"}, "Microsoft.Bot.Builder.LanguageGeneration/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-SHPMsyTwUuuyHRu5tO4RT/bbb6nVKawhpbeycgaQCsMYzm2g0123kAh+HxsyH60r1o0LbrcLDm8ypnWzh/2N1Q==", "path": "microsoft.bot.builder.languagegeneration/4.23.0", "hashPath": "microsoft.bot.builder.languagegeneration.4.23.0.nupkg.sha512"}, "Microsoft.Bot.Connector/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-/vgAQ8LonwAnyu6CzYYElU4k65k+9V2ncwztpZtBM+IuwkhDe3iAO2ycObqcyhMMWYUV81IOb0JZYcabjiZ4NQ==", "path": "microsoft.bot.connector/4.23.0", "hashPath": "microsoft.bot.connector.4.23.0.nupkg.sha512"}, "Microsoft.Bot.Connector.Streaming/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-Yz6PcySgtje88IGEJXj6agzya48pBL34/A5Zs3/xqmHvEQlI2ypMNc3OBOo2TRGxykklCSwc60PN2k95d4pbFw==", "path": "microsoft.bot.connector.streaming/4.23.0", "hashPath": "microsoft.bot.connector.streaming.4.23.0.nupkg.sha512"}, "Microsoft.Bot.ObjectModel/2025.4.3-3": {"type": "package", "serviceable": true, "sha512": "sha512-8bhvTBehGMX2Dwh5hAD4Zy0uHlG2i6XkdIGX/Q3VmKunthQjaOHnIf6LnW2zY78AhErCWV8WRUwuSAuS8uwT9A==", "path": "microsoft.bot.objectmodel/2025.4.3-3", "hashPath": "microsoft.bot.objectmodel.2025.4.3-3.nupkg.sha512"}, "Microsoft.Bot.ObjectModel.Dataverse/2025.4.3-3": {"type": "package", "serviceable": true, "sha512": "sha512-BQ+ZstDraoD/1yqhjJDGpWW+PAs6ukF9u7G/G7U1St8Xd2r17dyjJMBsH5mBcmBdHXfUJbx5hgMi8O2OzfS0VQ==", "path": "microsoft.bot.objectmodel.dataverse/2025.4.3-3", "hashPath": "microsoft.bot.objectmodel.dataverse.2025.4.3-3.nupkg.sha512"}, "Microsoft.Bot.ObjectModel.Json/2025.4.3-3": {"type": "package", "serviceable": true, "sha512": "sha512-9oZWAAh+HW1+eZ7kLSRJtZVaL5A3VIVpJJGkr8kexJZGocr9QDTHzhnxcfo5KiWLiRAgdjJ3bWlJqW9JLUDx0g==", "path": "microsoft.bot.objectmodel.json/2025.4.3-3", "hashPath": "microsoft.bot.objectmodel.json.2025.4.3-3.nupkg.sha512"}, "Microsoft.Bot.ObjectModel.NodeGenerators/2025.4.3-3": {"type": "package", "serviceable": true, "sha512": "sha512-45iA/HnrAwWPgoV+yShKO7PyfwdYtBgf2YN49EKzVC/gWdgasQ8lIf34icxTy51Zt3Zr8gpap3EaDi+nVB9EuA==", "path": "microsoft.bot.objectmodel.nodegenerators/2025.4.3-3", "hashPath": "microsoft.bot.objectmodel.nodegenerators.2025.4.3-3.nupkg.sha512"}, "Microsoft.Bot.ObjectModel.PowerFx/2025.4.3-3": {"type": "package", "serviceable": true, "sha512": "sha512-TA0iP1bgdMc8bkiuH7b5V4aZhHptFJgq3i5Fe9CGgB2ksbMnZ6qtd9/2JiFlJk4YfsboI99ylDzRZMN1/Zmz2w==", "path": "microsoft.bot.objectmodel.powerfx/2025.4.3-3", "hashPath": "microsoft.bot.objectmodel.powerfx.2025.4.3-3.nupkg.sha512"}, "Microsoft.Bot.Schema/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-HZeEXg/PuniNRIUU8ioSx/LrOXcbTZCZ1hUIqDdc6akWwhjrC2sTv7TaBpD0FlY4hDyUvj3GLyurPI/YChGPzA==", "path": "microsoft.bot.schema/4.23.0", "hashPath": "microsoft.bot.schema.4.23.0.nupkg.sha512"}, "Microsoft.Bot.Streaming/4.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-gYudjsFAVjwZBRU5irJh7sdsD7gNRFfZEUwWTqkNp1eGaR1t+4sJY+YyqYL3PyCMSgLrKE46bt/JWuDi3CjRfA==", "path": "microsoft.bot.streaming/4.23.0", "hashPath": "microsoft.bot.streaming.4.23.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kGMEV53Od1ES0BDh7OOKbTW9Zu5dbbQ72yI936dvvbHlde3puuq/WRKAccFgcB2PuRjox1HFhA9+t53RYqfuEA==", "path": "microsoft.extensions.caching.abstractions/2.0.0", "hashPath": "microsoft.extensions.caching.abstractions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NqvVdYLbX7N2J2Wz9y3zjhE66JRdROiZZsGhA2u4a9IcIq/jzINC/cLM96BHA+TSOZFPxVdWneqB6/yt9u846A==", "path": "microsoft.extensions.caching.memory/2.0.0", "hashPath": "microsoft.extensions.caching.memory.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-37t0TfekfG6XM8kue/xNaA66Qjtti5Qe1xA41CK+bEd8VD76/oXJc+meFJHGzygIC485dCpKoamG/pDfb9Qd7Q==", "path": "microsoft.identity.client/4.67.2", "hashPath": "microsoft.identity.client.4.67.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-DKs+Lva6csEUZabw+JkkjtFgVmcXh4pJeQy5KH5XzPOaKNoZhAMYj1qpKd97qYTZKXIFH12bHPk0DA+6krw+Cw==", "path": "microsoft.identity.client.extensions.msal/4.67.2", "hashPath": "microsoft.identity.client.extensions.msal.4.67.2.nupkg.sha512"}, "Microsoft.Identity.Web.Certificateless/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yb<PERSON><PERSON>CLeJFuTCDVTtt3OlD5n+CYQgUAzmn0YZw+Z4NR5XwB4iGQ/zMqQ+ruJfgoKGWe6BTl0vCfsv1O4XPqCvg==", "path": "microsoft.identity.web.certificateless/3.3.0", "hashPath": "microsoft.identity.web.certificateless.3.3.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-QSSDer3kvyTdNq6BefgX4EYi1lsia2zJUh5CfIMZFQUh6BhrXK1WE4i2C9ltUmmuUjoeVVX6AaSo9NZfpTGNdw==", "path": "microsoft.identitymodel.abstractions/8.1.2", "hashPath": "microsoft.identitymodel.abstractions.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-AWQINMvtamdYBqtG8q8muyYTfA9i5xRBEsMKQdzOn5xRzhVVDSzsNGYof1docfF3pX4hNRUpHlzs61RP0reZMw==", "path": "microsoft.identitymodel.jsonwebtokens/8.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-pEn//qKJcEXDsLHLzACFrT3a2kkpIGOXLEYkcuxjqWoeDnbeotu0LY9fF8+Ds9WWpVE9ZGlxXamT0VR8rxaQeA==", "path": "microsoft.identitymodel.logging/8.1.2", "hashPath": "microsoft.identitymodel.logging.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Yu3UJWIFX2/5m2MZskECqByr62L8A0uTtTblWIxy0wJNUg0OJGhIK6oRdpcZ8xbSJYD/SOE8psjo5IXRqC3Bsw==", "path": "microsoft.identitymodel.protocols/8.1.2", "hashPath": "microsoft.identitymodel.protocols.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-eEtnzZiYymJYaguYeIXyviUocltBQzeYI0bEtot1Nrnl+qklCZARgk+SAaeYfdmc9CYo7aqP5UJ78rTTSTpQGQ==", "path": "microsoft.identitymodel.protocols.openidconnect/8.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZSzGsAA3BY20XHnsp8OjrHFtpd+pQtiu4UJDjPtXwCtEzcE5CjWP/8iZEJXy5AxVEFB0z6EwLSN+T1Fsdpjifw==", "path": "microsoft.identitymodel.tokens/8.1.2", "hashPath": "microsoft.identitymodel.tokens.8.1.2.nupkg.sha512"}, "Microsoft.PowerFx.Core/1.3.0-build.20250212-1002": {"type": "package", "serviceable": true, "sha512": "sha512-iJO5BNxGAHb5Fjc41NDtCpORk18y/Xm+S2VEI84VZGAFXTifMkfnBW4wYp7vxQ9xoS7O67gLKOjqrsEtb0RPnA==", "path": "microsoft.powerfx.core/1.3.0-build.20250212-1002", "hashPath": "microsoft.powerfx.core.1.3.0-build.20250212-1002.nupkg.sha512"}, "Microsoft.PowerFx.Interpreter/1.3.0-build.20250212-1002": {"type": "package", "serviceable": true, "sha512": "sha512-LvTCvg4VPDhdcNdULXCWmvFIA61Jb+V0d8seopUbiggZaF7j1h/XgkdLolrXdu3dOpma6AoVTz2c0bCgT78Yqg==", "path": "microsoft.powerfx.interpreter/1.3.0-build.20250212-1002", "hashPath": "microsoft.powerfx.interpreter.1.3.0-build.20250212-1002.nupkg.sha512"}, "Microsoft.PowerFx.Json/1.3.0-build.20250212-1002": {"type": "package", "serviceable": true, "sha512": "sha512-m8UYwdgzAEsj0DnLk8cbIMWYqydC96y47DeuoEeJjbY2QVnyAuZzR/5g+Yz9YEmgwfHKhAuQ1OlXlOctO6TQwA==", "path": "microsoft.powerfx.json/1.3.0-build.20250212-1002", "hashPath": "microsoft.powerfx.json.1.3.0-build.20250212-1002.nupkg.sha512"}, "Microsoft.PowerFx.LanguageServerProtocol/1.3.0-build.20250212-1002": {"type": "package", "serviceable": true, "sha512": "sha512-pcmsgJvxzXKsTvvmDejnTij/iFM4xNNyW7UkvGwRVsfI3GrYaj1nZogLZIQ0RB6cVb1s3PlNOj3wn0s27jj/vg==", "path": "microsoft.powerfx.languageserverprotocol/1.3.0-build.20250212-1002", "hashPath": "microsoft.powerfx.languageserverprotocol.1.3.0-build.20250212-1002.nupkg.sha512"}, "Microsoft.PowerFx.Transport.Attributes/1.3.0-build.20250212-1002": {"type": "package", "serviceable": true, "sha512": "sha512-Rtg7ydnDo65fJHpd7LCMfCdpnkNmYp/BRXrqyvnwJh1lMKj6Qxnx14gqOKepI0EXMqENXseAn+cFmbo92dw2Mw==", "path": "microsoft.powerfx.transport.attributes/1.3.0-build.20250212-1002", "hashPath": "microsoft.powerfx.transport.attributes.1.3.0-build.20250212-1002.nupkg.sha512"}, "Microsoft.Recognizers.Text/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-URNFAH3Q6rJILL2PixaOcUfoLOFRaiEw7K6AsVsbMzThBZeNU8GMpJb2mFABCyx5I43DrmpB0vl/7EmRP/16RQ==", "path": "microsoft.recognizers.text/1.3.2", "hashPath": "microsoft.recognizers.text.1.3.2.nupkg.sha512"}, "Microsoft.Recognizers.Text.Choice/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-4cPOSKNCN0BIeaAfSV7DnR/XxsTscm9lWgEzhYIbrXd94UuHzZuUR+0U5MaYTB9W6t7yoHJLSChAaGq4pAAMYw==", "path": "microsoft.recognizers.text.choice/1.3.2", "hashPath": "microsoft.recognizers.text.choice.1.3.2.nupkg.sha512"}, "Microsoft.Recognizers.Text.DataTypes.TimexExpression/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-bTIQbNtjrLwvXuBRc2FT3N4/TIT19xA0vmVw8imKsRCX9zuv2yxNOOqIWe7TH3uULftCrZWs55AtD3hB4Pvqrw==", "path": "microsoft.recognizers.text.datatypes.timexexpression/1.3.2", "hashPath": "microsoft.recognizers.text.datatypes.timexexpression.1.3.2.nupkg.sha512"}, "Microsoft.Recognizers.Text.DateTime/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-KGDTLJflS2qJVFHDaWivRHH8lp/Udpjd3v0We7MDYFNcnNsJKjlW3zXwx3DYmStRtGgFD+o9/oNLDFKhBUFdFg==", "path": "microsoft.recognizers.text.datetime/1.3.2", "hashPath": "microsoft.recognizers.text.datetime.1.3.2.nupkg.sha512"}, "Microsoft.Recognizers.Text.Number/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-eY<PERSON>cfeQeF3gbb9ReEFT9OHznSI8WmU7dwVuTXbRreySZEfdDM967Vg0sGlcnploe9XDcqPPd66851htVR2dqg==", "path": "microsoft.recognizers.text.number/1.3.2", "hashPath": "microsoft.recognizers.text.number.1.3.2.nupkg.sha512"}, "Microsoft.Recognizers.Text.NumberWithUnit/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-s7f+sqnJFmNV1BD32ESN02Exs2WJgA79aCDFIVg4plw3PBTxIFO+79BDf0J2WeH0JeSXpQekWuedIXFXQc5x+A==", "path": "microsoft.recognizers.text.numberwithunit/1.3.2", "hashPath": "microsoft.recognizers.text.numberwithunit.1.3.2.nupkg.sha512"}, "Microsoft.Recognizers.Text.Sequence/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-IS/yMFfLOX4AR5RM+mwI4yTlk4uijjs4XcwRpAPjulW6kUQJXvWpsLBrXP6LEPOpdImHJ5J0ZE0VnnYdiOXTqg==", "path": "microsoft.recognizers.text.sequence/1.3.2", "hashPath": "microsoft.recognizers.text.sequence.1.3.2.nupkg.sha512"}, "Microsoft.Rest.ClientRuntime/2.3.24": {"type": "package", "serviceable": true, "sha512": "sha512-hZH7XgM3eV2jFrnq7Yf0nBD4WVXQzDrer2gEY7HMNiwio2hwDsTHO6LWuueNQAfRpNp4W7mKxcXpwXUiuVIlYw==", "path": "microsoft.rest.clientruntime/2.3.24", "hashPath": "microsoft.rest.clientruntime.2.3.24.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NuGet.Common/6.10.1": {"type": "package", "serviceable": true, "sha512": "sha512-djIP7OOdQYamFFtLFxbGTotFhlCkRs42Nc2lYS7E1uGLq91zwLdfOUCsWLfFBMT5EA8iqQnyRYrMD/sihgTkiQ==", "path": "nuget.common/6.10.1", "hashPath": "nuget.common.6.10.1.nupkg.sha512"}, "NuGet.Configuration/6.10.1": {"type": "package", "serviceable": true, "sha512": "sha512-SlPy3mxtMZRb281WUDA5Q+8SPR5objjFfXXOGjk5vs60/f7KeIdORJOj2UgxJal9YFFU58sJv3tVkaF51bGoyA==", "path": "nuget.configuration/6.10.1", "hashPath": "nuget.configuration.6.10.1.nupkg.sha512"}, "NuGet.Frameworks/6.10.1": {"type": "package", "serviceable": true, "sha512": "sha512-DtppveEBKkGwLoY5fk2DLNxtVbx0iw8r7s/RjYdm2AkK7RwnfJGe+j7DriYSEuxHrvSOU7n3ELKmlnn9jbZYfQ==", "path": "nuget.frameworks/6.10.1", "hashPath": "nuget.frameworks.6.10.1.nupkg.sha512"}, "NuGet.Packaging/6.10.1": {"type": "package", "serviceable": true, "sha512": "sha512-0YiFuHfPty9XOZXEZTj8KPjhBZhr7q91vmANttay+3IsO3ri40sMyGDoTRhFYH/A8dJzwmnD7ZNDJLFTiChwNA==", "path": "nuget.packaging/6.10.1", "hashPath": "nuget.packaging.6.10.1.nupkg.sha512"}, "NuGet.Versioning/6.10.1": {"type": "package", "serviceable": true, "sha512": "sha512-tovHZ3OlMVmsTdhv2z5nwnnhoA1ryhfJMyVQ9/+iv6d3h78fp230XaGy3K/iVcLwB50DdfNfIsitW97KSOWDFg==", "path": "nuget.versioning/6.10.1", "hashPath": "nuget.versioning.6.10.1.nupkg.sha512"}, "PolySharp/1.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-mOOmFYwad3MIOL14VCjj02LljyF1GNw1wP0YVlxtcPvqdxjGGMNdNJJxHptlry3MOd8b40Flm8RPOM8JOlN2sQ==", "path": "polysharp/1.14.1", "hashPath": "polysharp.1.14.1.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Formats.Asn1/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "path": "system.formats.asn1/8.0.1", "hashPath": "system.formats.asn1.8.0.1.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-UoidlNYjML1ZbV5s8bLP84VpxDzv8uhHzyt5YkZwqLmFTmtOQheNuTKpR/5UWmO5Ka4JT3kVmhUNq5Li733wTg==", "path": "system.identitymodel.tokens.jwt/8.1.2", "hashPath": "system.identitymodel.tokens.jwt.8.1.2.nupkg.sha512"}, "System.IO.Packaging/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-p4E6xqW9T8zTgS1NoB3oYkeuNp75bKMK11YuArrkg52wygxcQjbcPB8Ryh9E5ohLu+ZRCqmsg7flE9F9fUeeyA==", "path": "system.io.packaging/9.0.4", "hashPath": "system.io.packaging.9.0.4.nupkg.sha512"}, "System.IO.Pipelines/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-luF2Xba+lTe2GOoNQdZLe8q7K6s7nSpWZl9jIwWNMszN4/Yv0lmxk9HISgMmwdyZ83i3UhAGXaSY9o6IJBUuuA==", "path": "system.io.pipelines/9.0.4", "hashPath": "system.io.pipelines.9.0.4.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "path": "system.memory.data/6.0.0", "hashPath": "system.memory.data.6.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-LGbXi1oUJ9QgCNGXRO9ndzBL/GZgANcsURpMhNR8uO+rca47SZmciS3RSQUvlQRwK3QHZSHNOXzoMUASKA+Anw==", "path": "system.security.cryptography.pkcs/6.0.4", "hashPath": "system.security.cryptography.pkcs.6.0.4.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-V+5cCPpk1S2ngekUs9nDrQLHGiWFZMg8BthADQr+Fwi59a8DdHFu26S2oi9Bfgv+d67bqmkPqctJXMEXiimXUg==", "path": "system.text.encodings.web/9.0.4", "hashPath": "system.text.encodings.web.9.0.4.nupkg.sha512"}, "System.Text.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-pYtmpcO6R3Ef1XilZEHgXP2xBPVORbYEzRP7dl0IAAbN8Dm+kfwio8aCKle97rAWXOExr292MuxWYurIuwN62g==", "path": "system.text.json/9.0.4", "hashPath": "system.text.json.9.0.4.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.ValueTuple/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-BahUww/+mdP4ARCAh2RQhQTg13wYLVrBb9SYVgW8ZlrwjraGCXHGjo0oIiUfZ34LUZkMMR+RAzR7dEY4S1HeQQ==", "path": "system.valuetuple/4.4.0", "hashPath": "system.valuetuple.4.4.0.nupkg.sha512"}, "YamlDotNet/16.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SgMOdxbz8X65z8hraIs6hOEdnkH6hESTAIUa7viEngHOYaH+6q5XJmwr1+yb9vJpNQ19hCQY69xbFsLtXpobQA==", "path": "yamldotnet/16.3.0", "hashPath": "yamldotnet.16.3.0.nupkg.sha512"}, "NuanceMix/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}