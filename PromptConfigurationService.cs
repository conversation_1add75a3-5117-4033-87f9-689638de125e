﻿using NDFToCopilotStudioConverter;
using System;
using System.Collections.Generic;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public static class PromptConfigurationService
    {
        public static PromptConfigurationModel ReadCollectionConfiguration(XElement stateElement)
        {
            var model = new PromptConfigurationModel();

            var initialPromptElement = stateElement.Element("initialprompt");
            if (initialPromptElement != null)
            {
                //model.InitialPrompt = InitialPromptService.ReadCollectionConfiguration(initialPromptElement);
                model.InitialPrompt = PlayStateService.ProcessState(initialPromptElement);
            }

            var repeatPromptElement = stateElement.Element("repeatprompts");
            if (repeatPromptElement != null)
            {
                model.RepeatPrompts = RepeatePromptService.ReadCollectionConfiguration(repeatPromptElement);
            }

            var noMatchPrompts = new List<PlayStateModel>();
            foreach (var promptElement in stateElement.Elements("nomatchprompts"))
            {
                var noMatchPromptsModel = PlayStateService.ProcessState(promptElement);
                noMatchPrompts.Add(noMatchPromptsModel);
            }
            model.NomatchPrompts = noMatchPrompts;

            var noInputPrompts = new List<PlayStateModel>();
            foreach (var promptElement in stateElement.Elements("noinputprompts"))
            {
                var noInputPromptsModel = PlayStateService.ProcessState(promptElement);
                noInputPrompts.Add(noInputPromptsModel);
            }
            model.NoinputPrompts = noInputPrompts;

            // Add logic for NomatchPrefixes and NoinputPrefixes if needed
            // Example:
            // var nomatchPrefixes = new List<NomatchPrefixeModel>();
            // foreach (var prefixElement in stateElement.Elements("nomatchprefixes"))
            // {
            //     var nomatchPrefixModel = NomatchPrefixService.ReadCollectionConfiguration(prefixElement);
            //     nomatchPrefixes.Add(nomatchPrefixModel);
            // }
            // model.NomatchPrefixes = nomatchPrefixes;

            // var noinputPrefixes = new List<NoinputPrefixeModel>();
            // foreach (var prefixElement in stateElement.Elements("noinputprefixes"))
            // {
            //     var noinputPrefixModel = NoinputPrefixService.ReadCollectionConfiguration(prefixElement);
            //     noinputPrefixes.Add(noinputPrefixModel);
            // }
            // model.NoinputPrefixes = noinputPrefixes;

            return model;
        }
    }
}
