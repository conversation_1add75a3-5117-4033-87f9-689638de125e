﻿using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class GrammarDataModel
    {
        public string Entity { get; set; }
        public List<string> Grammars { get; set; }
        public List<SWIMeaningModel> SWI_meaning { get; set; }
        public string EntityType { get; set; }
        public string Reason { get; set; }

        public class SWIMeaningModel
        {
            public string name { get; set; }

            [Newtonsoft.Json.JsonProperty("vocab {en-us}")]
            public List<string> VocabEnUs { get; set; }

            public string dtmf { get; set; }
        }
    }

    /// <summary>
    /// Bot Framework equivalent of GrammarDataModel for grammar-to-entity mapping
    /// </summary>
    public class BotFrameworkGrammarDataModel
    {
        public string Entity { get; set; }
        public List<string> Grammars { get; set; }
        public List<BotFrameworkSWIMeaningModel> SWI_meaning { get; set; }
        public string EntityType { get; set; }
        public string Reason { get; set; }

        public class BotFrameworkSWIMeaningModel
        {
            public string name { get; set; }

            [Newtonsoft.Json.JsonProperty("vocab {en-us}")]
            public List<string> VocabEnUs { get; set; }

            public string dtmf { get; set; }
        }
    }
}
