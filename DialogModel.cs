﻿using System;
using System.Collections.Generic;

//rename namespace from MixToCopilotStudioConverter to NDFToCopilotStudioConverter
namespace NDFToCopilotStudioConverter
{
    public class DialogModel
    {
        public string dialogName { get; set; }
        public List<StateModel> States { get; set; }
        public List<SessionMappingModel> SessionMappings { get; set; }

        public DialogModel()
        {
            States = new List<StateModel>();
            SessionMappings = new List<SessionMappingModel>();
        }

        public void DisplayDetails()
        {
            Console.WriteLine($"Dialog ID: {dialogName}");
            foreach (var state in States)
            {
                state.DisplayDetails();
            }
            Console.WriteLine();
        }
    }
}
