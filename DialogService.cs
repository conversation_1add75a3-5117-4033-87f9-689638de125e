﻿using System;
using System.Collections.Generic;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public static class DialogService
    {
        public static DialogModel ProcessDialog(XElement dialogElement)
        {
            DialogModel dialogModel = new DialogModel
            {
                dialogName = dialogElement.Attribute("id")?.Value
            };

            bool isStartNodeFound = false;

            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(dialogElement.Elements("session-mapping"));
            dialogModel.SessionMappings.AddRange(sessionMappings);
            var varMappings = SessionMappingHelper.ProcessSessionMappings(dialogElement.Elements("var"));
            dialogModel.SessionMappings.AddRange(varMappings);

            foreach (var stateElement in dialogElement.Elements())
            {
                string stateType = stateElement.Name.LocalName;
                StateModel stateModel;

                switch (stateType)
                {
                    case "decision-state":
                        stateModel = DecisionStateService.ProcessState(stateElement);
                        break;

                    case "play-state":
                        stateModel = PlayStateService.ProcessState(stateElement);
                        break;

                    case "custom-state":
                        stateModel = CustomStateService.ProcessState(stateElement);
                        break;

                    case "subdialog-state":
                        stateModel = SubdialogStateService.ProcessState(stateElement);
                        break;

                    case "dm-state":
                        stateModel = DmStateService.ProcessState(stateElement);
                        break;

                    case "data-access-state":
                        stateModel = DataAccessStateService.ProcessState(stateElement);
                        break;

                    default:
                        continue; // Skip any other types of states
                }

                if (isStartNodeFound==false)
                {
                    stateModel.IsStartNode = true;
                    isStartNodeFound = true;
                }
                else
                {
                    stateModel.IsStartNode = false;
                }

                dialogModel.States.Add(stateModel);
            }

            return dialogModel;
        }
    }
}
