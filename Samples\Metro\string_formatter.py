#!/usr/bin/env python3
"""
Script to process JSON files and transform strings with \r\n separators into comma-separated quoted strings.
"""

import json
import argparse
import sys
from pathlib import Path

def format_string(input_string):
    """
    Transform input string from:
    "AP1005_ACPStart_DM.grxml\r\nAP1005_ACPStart_DM_dtmf.grxml"

    to:
    "AP1005_ACPStart_DM.grxml",
    "AP1005_ACPStart_DM_dtmf.grxml"
    """
    if not isinstance(input_string, str):
        return input_string

    # Split the string on \r\n
    parts = input_string.split('\r\n')

    # Filter out empty strings and strip whitespace
    parts = [part.strip() for part in parts if part.strip()]

    # If only one part, return it without comma formatting
    if len(parts) <= 1:
        return input_string

    # Format each part with quotes and join with comma
    formatted_parts = [f'"{part}"' for part in parts]

    return ',\n'.join(formatted_parts)

def process_json_value(value):
    """
    Recursively process JSON values, applying string formatting to strings.
    """
    if isinstance(value, str):
        return format_string(value)
    elif isinstance(value, dict):
        return {key: process_json_value(val) for key, val in value.items()}
    elif isinstance(value, list):
        return [process_json_value(item) for item in value]
    else:
        return value

def process_json_file(input_file, output_file):
    """
    Process a JSON file, transforming strings with \r\n separators.
    """
    try:
        # Read input JSON file
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Process the data
        processed_data = process_json_value(data)

        # Write output JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, indent=2, ensure_ascii=False)

        print(f"Successfully processed {input_file} -> {output_file}")
        return True

    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        return False
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in '{input_file}': {e}")
        return False
    except Exception as e:
        print(f"Error processing file: {e}")
        return False

def main():
    """
    Main function to handle command-line arguments and process files.
    """
    parser = argparse.ArgumentParser(
        description='Process JSON files and transform strings with \\r\\n separators into comma-separated quoted strings.'
    )
    parser.add_argument('input_file', nargs='?', help='Input JSON file path')
    parser.add_argument('output_file', nargs='?', help='Output JSON file path')
    parser.add_argument('--test', action='store_true', help='Run test with sample string')
    parser.add_argument('--interactive', action='store_true', help='Run in interactive mode')

    args = parser.parse_args()

    # Check if we need input and output files
    if not args.test and not args.interactive:
        if not args.input_file or not args.output_file:
            parser.error("input_file and output_file are required unless using --test or --interactive")

    if args.test:
        # Run test with sample string
        test_string = "AP1005_ACPStart_DM.grxml\r\nAP1005_ACPStart_DM_dtmf.grxml"
        result = format_string(test_string)
        print("Test Input:")
        print(repr(test_string))
        print("\nTest Output:")
        print(result)
        return

    if args.interactive:
        # Interactive mode - allow user to input their own string
        print("Interactive mode:")
        print("Enter your string (or press Enter to exit):")

        while True:
            user_input = input("> ")
            if not user_input:
                break

            # Replace literal \r\n with actual carriage return and newline
            user_input = user_input.replace('\\r\\n', '\r\n')

            result = format_string(user_input)
            print("Formatted output:")
            print(result)
            print()
        return

    # Validate input file exists
    input_path = Path(args.input_file)
    if not input_path.exists():
        print(f"Error: Input file '{args.input_file}' does not exist.")
        sys.exit(1)

    # Process the JSON file
    success = process_json_file(args.input_file, args.output_file)
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
