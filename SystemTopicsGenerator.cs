﻿using System.Text;
using System.Text.RegularExpressions;

namespace NDFToCopilotStudioConverter
{
    public static class SystemTopicsGenerator
    {
        public static string GenerateYamlOnUnknownKeyPress()
        {

            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: OnUnknownDtmfKey");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    description: This system topic triggers when the user's utterance does not match any existing topics.");
            yamlBuilder.AppendLine("    schemaName: topic.OnUnknownDtmfKey");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnUnknownDtmfKey");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: SendActivity");
            yamlBuilder.AppendLine("            id: sendActivity_26sT88");
            yamlBuilder.AppendLine("            activity:");
            yamlBuilder.AppendLine("              text:");
            yamlBuilder.AppendLine("                - I'm sorry. I did not understand. Can you try again?");
            yamlBuilder.AppendLine("              speak:");
            yamlBuilder.AppendLine("                - I'm sorry. I did not understand. Can you try again?");

            return yamlBuilder.ToString();
        }
        public static string GenerateYamlOnSpeechUnrecognized()
        {

            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: OnUnrecognizedSpeech");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    description: This system topic triggers when the user's utterance does not match any existing topics.");
            yamlBuilder.AppendLine("    schemaName: topic.OnUnrecognizedSpeech");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnUnrecognizedSpeech");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: SendActivity");
            yamlBuilder.AppendLine("            id: sendActivity_26sT9O");
            yamlBuilder.AppendLine("            activity:");
            yamlBuilder.AppendLine("              text:");
            yamlBuilder.AppendLine("                - I'm sorry. I did not understand. Can you try again?");
            yamlBuilder.AppendLine("              speak:");
            yamlBuilder.AppendLine("                - I'm sorry. I did not understand. Can you try again?");

            return yamlBuilder.ToString();
        }
        public static string GenerateYamlOnSilenceDetection()
        {

            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: OnSilence");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    description: This system topic triggers when the user's utterance does not match any existing topics.");
            yamlBuilder.AppendLine("    schemaName: topic.OnSilence");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnSilence");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: SendActivity");
            yamlBuilder.AppendLine("            id: sendActivity_C6pKOo");
            yamlBuilder.AppendLine("            activity:");
            yamlBuilder.AppendLine("              text:");
            yamlBuilder.AppendLine("                - Goodbye!");
            yamlBuilder.AppendLine("              speak:");
            yamlBuilder.AppendLine("                - Goodbye!");
            yamlBuilder.AppendLine("          - kind: EndConversation");
            yamlBuilder.AppendLine("            id: Rgrh4i");


            return yamlBuilder.ToString();
        }
        public static string GenerateYamlOnSignin()
        {

            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: OnSignIn");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    description: This system topic triggers when the user's utterance does not match any existing topics.");
            yamlBuilder.AppendLine("    schemaName: topic.OnSignIn");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnSignIn");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: ConditionGroup");
            yamlBuilder.AppendLine("            id: conditionGroup_ypjGKL");
            yamlBuilder.AppendLine("            conditions:");
            yamlBuilder.AppendLine("              - id: conditionItem_7XYIIR");
            yamlBuilder.AppendLine("                condition: =System.SignInReason = SignInReason.SignInRequired");
            yamlBuilder.AppendLine("                actions:");
            yamlBuilder.AppendLine("                  - kind: SendActivity");
            yamlBuilder.AppendLine("                    id: sendMessage_1jHUNO");
            yamlBuilder.AppendLine("                    activity:");
            yamlBuilder.AppendLine("                      text:");
            yamlBuilder.AppendLine("                        - Hello! To be able to help you, I'll need you to sign in.");
            yamlBuilder.AppendLine("                      speak:");
            yamlBuilder.AppendLine("                        - Hello! To be able to help you, I'll need you to sign in.");
            yamlBuilder.AppendLine("          - kind: OAuthInput");
            yamlBuilder.AppendLine("            id: gOjhZA");
            yamlBuilder.AppendLine("            title: Login");
            yamlBuilder.AppendLine("            text: To continue, please login");

            return yamlBuilder.ToString();
        }

        public static string GenerateYamlOnError()
        {

            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: OnError");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    description: This system topic triggers when the user's utterance does not match any existing topics.");
            yamlBuilder.AppendLine("    schemaName: topic.OnError");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnError");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: SetVariable");
            yamlBuilder.AppendLine("            id: setVariable_timestamp");
            yamlBuilder.AppendLine("            variable: init:Topic.CurrentTime");
            yamlBuilder.AppendLine("            value: =Text(Now(), DateTimeFormat.UTC)");
            yamlBuilder.AppendLine("          - kind: ConditionGroup");
            yamlBuilder.AppendLine("            id: condition_1");
            yamlBuilder.AppendLine("            conditions:");
            yamlBuilder.AppendLine("              - id: bL4wmY");
            yamlBuilder.AppendLine("                condition: =System.Conversation.InTestMode = true");
            yamlBuilder.AppendLine("                actions:");
            yamlBuilder.AppendLine("                  - kind: SendActivity");
            yamlBuilder.AppendLine("                    id: sendMessage_XJBYMo");
            yamlBuilder.AppendLine("                    activity:");
            yamlBuilder.AppendLine("                      text:");
            yamlBuilder.AppendLine("                        - |-");
            yamlBuilder.AppendLine("                          Error Message: {System.Error.Message}");
            yamlBuilder.AppendLine("                          Error Code: {System.Error.Code}");
            yamlBuilder.AppendLine("                          Conversation Id: {System.Conversation.Id}");
            yamlBuilder.AppendLine("                          Time (UTC): {Topic.CurrentTime}");
            yamlBuilder.AppendLine("                      speak:");
            yamlBuilder.AppendLine("                        - |-");
            yamlBuilder.AppendLine("                          Error Message: {System.Error.Message}");
            yamlBuilder.AppendLine("                          Error Code: {System.Error.Code}");
            yamlBuilder.AppendLine("                          Conversation Id: {System.Conversation.Id}");
            yamlBuilder.AppendLine("                          Time (UTC): {Topic.CurrentTime}");
            yamlBuilder.AppendLine("            elseActions:");
            yamlBuilder.AppendLine("              - kind: SendActivity");
            yamlBuilder.AppendLine("                id: sendMessage_dZ0gaF");
            yamlBuilder.AppendLine("                activity:");
            yamlBuilder.AppendLine("                  text:");
            yamlBuilder.AppendLine("                    - |-");
            yamlBuilder.AppendLine("                      An error has occurred.");
            yamlBuilder.AppendLine("                      Error code: {System.Error.Code}");
            yamlBuilder.AppendLine("                      Conversation Id: {System.Conversation.Id}");
            yamlBuilder.AppendLine("                      Time (UTC): {Topic.CurrentTime}.");
            yamlBuilder.AppendLine("                  speak:");
            yamlBuilder.AppendLine("                    - An error has occurred, please try again.");
            yamlBuilder.AppendLine("          - kind: LogCustomTelemetryEvent");
            yamlBuilder.AppendLine("            id: 9KwEAn");
            yamlBuilder.AppendLine("            eventName: OnErrorLog");
            yamlBuilder.AppendLine("            properties: \"={ErrorMessage: System.Error.Message, ErrorCode: System.Error.Code, TimeUTC: Topic.CurrentTime, ConversationId: System.Conversation.Id}\"");
            yamlBuilder.AppendLine("          - kind: CancelAllDialogs");
            yamlBuilder.AppendLine("            id: NW7NyY");

            return yamlBuilder.ToString();

        }
        public static string GenerateYamlOnEscalate()
        {
            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: Escalate");
            yamlBuilder.AppendLine("    parentBotId: 4a1f5002-3044-ef11-8409-6045bd0303b3");
            yamlBuilder.AppendLine("    description: This system topic is triggered when the user indicates they would like to speak to a human agent. You can configure how the bot will handle human hand-off scenarios in the bot settings. If your bot does not handle escalations, this topic should be disabled.");
            yamlBuilder.AppendLine("    schemaName: topic.Escalate");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      startBehavior: CancelOtherTopics");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnEscalate");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        intent:");
            yamlBuilder.AppendLine("          displayName: Escalate");
            yamlBuilder.AppendLine("          includeInOnSelectIntent: false");
            yamlBuilder.AppendLine("          triggerQueries:");
            yamlBuilder.AppendLine("            - Talk to agent");
            yamlBuilder.AppendLine("            - Talk to a person");
            yamlBuilder.AppendLine("            - Talk to someone");
            yamlBuilder.AppendLine("            - Call back");
            yamlBuilder.AppendLine("            - Call customer service");
            yamlBuilder.AppendLine("            - Call me please");
            yamlBuilder.AppendLine("            - Call support");
            yamlBuilder.AppendLine("            - Call technical support");
            yamlBuilder.AppendLine("            - Can an agent call me");
            yamlBuilder.AppendLine("            - operator");
            yamlBuilder.AppendLine("            - representative");
            yamlBuilder.AppendLine("            - Can I get in touch with someone else");
            yamlBuilder.AppendLine("            - Can I get real agent support");
            yamlBuilder.AppendLine("            - Can I get transferred to a person to call");
            yamlBuilder.AppendLine("            - Can I have a call in number Or can I be called");
            yamlBuilder.AppendLine("            - Can I have a representative call me");
            yamlBuilder.AppendLine("            - Can I schedule a call");
            yamlBuilder.AppendLine("            - Can I speak to a representative");
            yamlBuilder.AppendLine("            - Can I talk to a human");
            yamlBuilder.AppendLine("            - Can I talk to a human assistant");
            yamlBuilder.AppendLine("            - Can someone call me");
            yamlBuilder.AppendLine("            - Chat with a human");
            yamlBuilder.AppendLine("            - Chat with a representative");
            yamlBuilder.AppendLine("            - Chat with agent");
            yamlBuilder.AppendLine("            - Chat with someone please");
            yamlBuilder.AppendLine("            - Connect me to a live agent");
            yamlBuilder.AppendLine("            - Connect me to a person");
            yamlBuilder.AppendLine("            - Could someone contact me by phone");
            yamlBuilder.AppendLine("            - Customer agent");
            yamlBuilder.AppendLine("            - Customer representative");
            yamlBuilder.AppendLine("            - Customer service");
            yamlBuilder.AppendLine("            - I need a manager to contact me");
            yamlBuilder.AppendLine("            - I need customer service");
            yamlBuilder.AppendLine("            - I need help from a person");
            yamlBuilder.AppendLine("            - I need to speak with a live agent");
            yamlBuilder.AppendLine("            - I need to talk to a specialist please");
            yamlBuilder.AppendLine("            - I want to talk to customer service");
            yamlBuilder.AppendLine("            - I want to proceed with live support");
            yamlBuilder.AppendLine("            - I want to speak with a consultant");
            yamlBuilder.AppendLine("            - I want to speak with a live tech");
            yamlBuilder.AppendLine("            - I would like to speak with an associate");
            yamlBuilder.AppendLine("            - I would like to talk to a technician");
            yamlBuilder.AppendLine("            - Talk with tech support member");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: SendActivity");
            yamlBuilder.AppendLine("            id: sendMessage_s39DCt");
            yamlBuilder.AppendLine("            conversationOutcome: Escalated");
            yamlBuilder.AppendLine("            activity: Escalating to a live agent is not currently configured for this bot, however this is where the bot could provide information about how to get in touch with someone another way. Is there anything else I can help you with?");

            return yamlBuilder.ToString();
        }


        public static string GenerateYamlOnReset()
        {
            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: Reset Conversation");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    schemaName: topic.ResetConversation");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      startBehavior: UseLatestPublishedContentAndCancelOtherTopics");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnSystemRedirect");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: ClearAllVariables");
            yamlBuilder.AppendLine("            id: clearAllVariables_73bTFR");
            yamlBuilder.AppendLine("            variables: ConversationScopedVariables");
            yamlBuilder.AppendLine("          - kind: BeginDialog");
            yamlBuilder.AppendLine("            id: 21ppQE");
            yamlBuilder.AppendLine("            dialog: topic.Conversation Start");

            return yamlBuilder.ToString();
        }
        public static string GenerateYamlOnStartOver()
        {
            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: Start Over");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    schemaName: topic.StartOver");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnRecognizedIntent");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        intent:");
            yamlBuilder.AppendLine("          displayName: Start Over");
            yamlBuilder.AppendLine("          includeInOnSelectIntent: false");
            yamlBuilder.AppendLine("          triggerQueries:");
            yamlBuilder.AppendLine("            - let's begin again");
            yamlBuilder.AppendLine("            - start over");
            yamlBuilder.AppendLine("            - start again");
            yamlBuilder.AppendLine("            - restart");
            yamlBuilder.AppendLine("            - main menu");
            yamlBuilder.AppendLine("            - i need something else");
            yamlBuilder.AppendLine("            - mainmenu");
            yamlBuilder.AppendLine("            - main-menu");
            yamlBuilder.AppendLine("            - main menu please");
            yamlBuilder.AppendLine("            - main menu options");
            yamlBuilder.AppendLine("            - main menu selection");
            yamlBuilder.AppendLine("            - main menu selection please");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: Question");
            yamlBuilder.AppendLine("            id: question_zguoVV");
            yamlBuilder.AppendLine("            alwaysPrompt: false");
            yamlBuilder.AppendLine("            variable: init:Topic.Confirm");
            yamlBuilder.AppendLine("            prompt: Are you sure you want to restart the conversation?");
            yamlBuilder.AppendLine("            entity: BooleanPrebuiltEntity");
            yamlBuilder.AppendLine("          - kind: ConditionGroup");
            yamlBuilder.AppendLine("            id: conditionGroup_lvx2zV");
            yamlBuilder.AppendLine("            conditions:");
            yamlBuilder.AppendLine("              - id: conditionItem_sVQtHa");
            yamlBuilder.AppendLine("                condition: |-\n                  =If(Topic.Confirm = true, true, false)");
            yamlBuilder.AppendLine("                actions:");
            yamlBuilder.AppendLine("                  - kind: BeginDialog");
            yamlBuilder.AppendLine("                    id: 0YKYsy");
            yamlBuilder.AppendLine("                    dialog: topic.ResetConversation");
            yamlBuilder.AppendLine("            elseActions:");
            yamlBuilder.AppendLine("              - kind: SendActivity");
            yamlBuilder.AppendLine("                id: sendMessage_lk2CyQ");
            yamlBuilder.AppendLine("                activity: Ok. Let's carry on.");

            return yamlBuilder.ToString();
        }


        public static string GenerateYamlOnFallback()
        {
            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: Fallback");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    description: This system topic triggers when the user's utterance does not match any existing topics.");
            yamlBuilder.AppendLine("    schemaName: topic.Fallback");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnUnknownIntent");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: ConditionGroup");
            yamlBuilder.AppendLine("            id: conditionGroup_LktzXw");
            yamlBuilder.AppendLine("            conditions:");
            yamlBuilder.AppendLine("              - id: conditionItem_tlGIVo");
            yamlBuilder.AppendLine("                condition: |-\n                  =If(System.FallbackCount < 3, true, false)");
            yamlBuilder.AppendLine("                actions:");
            yamlBuilder.AppendLine("                  - kind: SendActivity");
            yamlBuilder.AppendLine("                    id: sendMessage_QZreqo");
            yamlBuilder.AppendLine("                    activity: I'm sorry, I'm not sure how to help with that. Can you try rephrasing?");
            yamlBuilder.AppendLine("            elseActions:");
            yamlBuilder.AppendLine("              - kind: BeginDialog");
            yamlBuilder.AppendLine("                id: 5aXj5M");
            yamlBuilder.AppendLine("                dialog: topic.Escalate");


            return yamlBuilder.ToString();
        }


        public static String GenerateYamlOnEndConversation()
        {

            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: EndConversation");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    description: This system topic triggers when the user's utterance does not match any existing topics.");
            yamlBuilder.AppendLine("    schemaName: topic.EndConversation");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnSystemRedirect");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: Question");
            yamlBuilder.AppendLine("            id: 41d42054-d4cb-4e90-b922-2b16b37fe379");
            yamlBuilder.AppendLine("            conversationOutcome: ResolvedImplied");
            yamlBuilder.AppendLine("            alwaysPrompt: true");
            yamlBuilder.AppendLine("            variable: init:Topic.SurveyResponse");
            yamlBuilder.AppendLine("            prompt:");
            yamlBuilder.AppendLine("              text:");
            yamlBuilder.AppendLine("                - Did that answer your question?");
            yamlBuilder.AppendLine("              speak:");
            yamlBuilder.AppendLine("                - Did that answer your question?");
            yamlBuilder.AppendLine("            entity:");
            yamlBuilder.AppendLine("              kind: BooleanPrebuiltEntity");
            yamlBuilder.AppendLine("              dtmfMultipleChoiceOptions:");
            yamlBuilder.AppendLine("                generateMapping: true");
            yamlBuilder.AppendLine("          - kind: ConditionGroup");
            yamlBuilder.AppendLine("            id: condition-0");
            yamlBuilder.AppendLine("            conditions:");
            yamlBuilder.AppendLine("              - id: condition-0-item-0");
            yamlBuilder.AppendLine("                condition: =Topic.SurveyResponse = true");
            yamlBuilder.AppendLine("                actions:");
            yamlBuilder.AppendLine("                  - kind: CSATQuestion");
            yamlBuilder.AppendLine("                    id: csat_1");
            yamlBuilder.AppendLine("                    conversationOutcome: ResolvedConfirmed");
            yamlBuilder.AppendLine("                  - kind: SendActivity");
            yamlBuilder.AppendLine("                    id: sendMessage_8r29O0");
            yamlBuilder.AppendLine("                    activity:");
            yamlBuilder.AppendLine("                      text:");
            yamlBuilder.AppendLine("                        - Thanks for your feedback.");
            yamlBuilder.AppendLine("                      speak:");
            yamlBuilder.AppendLine("                        - Thanks for your feedback.");
            yamlBuilder.AppendLine("                  - kind: Question");
            yamlBuilder.AppendLine("                    id: question_1");
            yamlBuilder.AppendLine("                    alwaysPrompt: true");
            yamlBuilder.AppendLine("                    variable: init:Topic.Continue");
            yamlBuilder.AppendLine("                    prompt:");
            yamlBuilder.AppendLine("                      text:");
            yamlBuilder.AppendLine("                        - Can I help with anything else?");
            yamlBuilder.AppendLine("                      speak:");
            yamlBuilder.AppendLine("                        - Can I help with anything else?");
            yamlBuilder.AppendLine("                    entity:");
            yamlBuilder.AppendLine("                      kind: BooleanPrebuiltEntity");
            yamlBuilder.AppendLine("                      dtmfMultipleChoiceOptions:");
            yamlBuilder.AppendLine("                        generateMapping: true");
            yamlBuilder.AppendLine("                  - kind: ConditionGroup");
            yamlBuilder.AppendLine("                    id: condition-1");
            yamlBuilder.AppendLine("                    conditions:");
            yamlBuilder.AppendLine("                      - id: condition-1-item-0");
            yamlBuilder.AppendLine("                        condition: =Topic.Continue = true");
            yamlBuilder.AppendLine("                        actions:");
            yamlBuilder.AppendLine("                          - kind: SendActivity");
            yamlBuilder.AppendLine("                            id: sendMessage_4eOE6h");
            yamlBuilder.AppendLine("                            activity:");
            yamlBuilder.AppendLine("                              text:");
            yamlBuilder.AppendLine("                                - Go ahead. I'm listening.");
            yamlBuilder.AppendLine("                              speak:");
            yamlBuilder.AppendLine("                                - Go ahead. I'm listening.");
            yamlBuilder.AppendLine("                    elseActions:");
            yamlBuilder.AppendLine("                      - kind: SendActivity");
            yamlBuilder.AppendLine("                        id: yHBz55");
            yamlBuilder.AppendLine("                        activity:");
            yamlBuilder.AppendLine("                          text:");
            yamlBuilder.AppendLine("                            - Ok, goodbye.");
            yamlBuilder.AppendLine("                          speak:");
            yamlBuilder.AppendLine("                            - Ok, goodbye.");
            yamlBuilder.AppendLine("                      - kind: EndConversation");
            yamlBuilder.AppendLine("                        id: jh1GMT");
            yamlBuilder.AppendLine("            elseActions:");
            yamlBuilder.AppendLine("              - kind: Question");
            yamlBuilder.AppendLine("                id: PM68ot");
            yamlBuilder.AppendLine("                alwaysPrompt: true");
            yamlBuilder.AppendLine("                variable: init:Topic.TryAgain");
            yamlBuilder.AppendLine("                prompt:");
            yamlBuilder.AppendLine("                  text:");
            yamlBuilder.AppendLine("                    - Sorry I wasn't able to help better. Would you like to try again?");
            yamlBuilder.AppendLine("                  speak:");
            yamlBuilder.AppendLine("                    - Sorry I wasn't able to help better. Would you like to try again?");
            yamlBuilder.AppendLine("                entity:");
            yamlBuilder.AppendLine("                  kind: BooleanPrebuiltEntity");
            yamlBuilder.AppendLine("                  dtmfMultipleChoiceOptions:");
            yamlBuilder.AppendLine("                    generateMapping: true");
            yamlBuilder.AppendLine("              - kind: ConditionGroup");
            yamlBuilder.AppendLine("                id: KNxYBf");
            yamlBuilder.AppendLine("                conditions:");
            yamlBuilder.AppendLine("                  - id: DPveFP");
            yamlBuilder.AppendLine("                    condition: =Topic.TryAgain = false");
            yamlBuilder.AppendLine("                    actions:");
            yamlBuilder.AppendLine("                      - kind: BeginDialog");
            yamlBuilder.AppendLine("                        id: cngqi4");
            yamlBuilder.AppendLine("                        dialog: crd93_voice2.topic.Escalate");
            yamlBuilder.AppendLine("                elseActions:");
            yamlBuilder.AppendLine("                  - kind: SendActivity");
            yamlBuilder.AppendLine("                    id: GrVHEW");
            yamlBuilder.AppendLine("                    activity:");
            yamlBuilder.AppendLine("                      text:");
            yamlBuilder.AppendLine("                        - Go ahead. I'm listening.");
            yamlBuilder.AppendLine("                      speak:");
            yamlBuilder.AppendLine("                        - Go ahead. I'm listening.");

            return yamlBuilder.ToString();
        }

        public static String GenerateYamlOnMultipleTopicsMatch()
        {

            var yamlBuilder = new StringBuilder();

            yamlBuilder.AppendLine("  - kind: DialogComponent");
            yamlBuilder.AppendLine("    displayName: MultipleTopicsMatch");
            yamlBuilder.AppendLine("    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3");
            yamlBuilder.AppendLine("    description: This system topic triggers when the user's utterance does not match any existing topics.");
            yamlBuilder.AppendLine("    schemaName: topic.MultipleTopicsMatch");
            yamlBuilder.AppendLine("    dialog:");
            yamlBuilder.AppendLine("      beginDialog:");
            yamlBuilder.AppendLine("        kind: OnSelectIntent");
            yamlBuilder.AppendLine("        id: main");
            yamlBuilder.AppendLine("        triggerBehavior: Always");
            yamlBuilder.AppendLine("        actions:");
            yamlBuilder.AppendLine("          - kind: SetVariable");
            yamlBuilder.AppendLine("            id: setVariable_M6434i");
            yamlBuilder.AppendLine("            variable: init:Topic.IntentOptions");
            yamlBuilder.AppendLine("            value: =System.Recognizer.IntentOptions");
            yamlBuilder.AppendLine("          - kind: SetTextVariable");
            yamlBuilder.AppendLine("            id: setTextVariable_0");
            yamlBuilder.AppendLine("            variable: Topic.NoneOfTheseDisplayName");
            yamlBuilder.AppendLine("            value: None of these");
            yamlBuilder.AppendLine("          - kind: EditTable");
            yamlBuilder.AppendLine("            id: sendMessage_g5Ls09");
            yamlBuilder.AppendLine("            changeType: Add");
            yamlBuilder.AppendLine("            itemsVariable: Topic.IntentOptions");
            yamlBuilder.AppendLine("            value: \"={ DisplayName: Topic.NoneOfTheseDisplayName, TopicId: \\\"NoTopic\\\", TriggerId: \\\"NoTrigger\\\", Score: 1.0 }\"");
            yamlBuilder.AppendLine("          - kind: Question");
            yamlBuilder.AppendLine("            id: question_zf2HhP");
            yamlBuilder.AppendLine("            interruptionPolicy:");
            yamlBuilder.AppendLine("              allowInterruption: false");
            yamlBuilder.AppendLine("            alwaysPrompt: true");
            yamlBuilder.AppendLine("            variable: System.Recognizer.SelectedIntent");
            yamlBuilder.AppendLine("            prompt:");
            yamlBuilder.AppendLine("              text:");
            yamlBuilder.AppendLine("                - \"To clarify, did you mean:\"");
            yamlBuilder.AppendLine("              speak:");
            yamlBuilder.AppendLine("                - \"To clarify, did you mean:\"");
            yamlBuilder.AppendLine("            entity:");
            yamlBuilder.AppendLine("              kind: DynamicClosedListEntity");
            yamlBuilder.AppendLine("              items: =Topic.IntentOptions");
            yamlBuilder.AppendLine("              dtmfMultipleChoiceOptions:");
            yamlBuilder.AppendLine("                generateMapping: true");
            yamlBuilder.AppendLine("          - kind: ConditionGroup");
            yamlBuilder.AppendLine("            id: conditionGroup_60PuXb");
            yamlBuilder.AppendLine("            conditions:");
            yamlBuilder.AppendLine("              - id: conditionItem_rs7GgM");
            yamlBuilder.AppendLine("                condition: =System.Recognizer.SelectedIntent.TopicId = \\\"NoTopic\\\"");
            yamlBuilder.AppendLine("                actions:");
            yamlBuilder.AppendLine("                  - kind: ReplaceDialog");
            yamlBuilder.AppendLine("                    id: YZXRDb");
            yamlBuilder.AppendLine("                    dialog: crd93_voice2.topic.Fallback");

            return yamlBuilder.ToString();

        }
    }

}
