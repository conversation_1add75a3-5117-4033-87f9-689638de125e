﻿using NDFToCopilotStudioConverter;
using System;
using System.Collections.Generic;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public static class RepeatePromptService
    {
        public static RepeatPromptsModel ReadCollectionConfiguration(XElement stateElement)
        {
            var model = new RepeatPromptsModel();
            ProcessAudio(stateElement.Element("audio"), model);
            return model;
        }

        private static void ProcessAudio(XElement audioElement, RepeatPromptsModel model)
        {
            if (audioElement != null)
            {
                foreach (var ifElement in audioElement.Elements("if"))
                {
                    ProcessIfCondition(ifElement, model);
                }

                // Process default prompts if no <if> conditions are met
                ProcessDefaultPrompts(audioElement, model);
            }
        }

        private static void ProcessIfCondition(XElement ifElement, RepeatPromptsModel model)
        {
            string condition = BuildConditionString(ifElement);
            ProcessInnerElements(ifElement, condition, model);

            foreach (var elseifElement in ifElement.Elements("elseif"))
            {
                string elseifCondition = BuildConditionString(elseifElement);
                ProcessInnerElements(elseifElement, elseifCondition, model);
            }

            var elseElement = ifElement.Element("else");
            if (elseElement != null)
            {
                string elseCondition = "else";
                ProcessInnerElements(elseElement, elseCondition, model);
            }
        }

        private static string BuildConditionString(XElement conditionElement)
        {
            var conditions = new List<string>();

            foreach (var condition in conditionElement.Elements("condition"))
            {
                string leftParam = condition.Elements("param")
                    .FirstOrDefault(p => p.Attribute("scope")?.Value == "session")?.Attribute("value")?.Value;
                string rightParam = condition.Elements("param")
                    .FirstOrDefault(p => p.Attribute("name")?.Value == "compare")?.Attribute("value")?.Value;

                // Default operator value
                string operatorValue = "=";

                // Check each param element for operator attribute
                var operatorParam = condition.Elements("param").FirstOrDefault(p => p.Attribute("name")?.Value == "operator");
                if (operatorParam != null)
                {
                    string op = operatorParam.Attribute("value")?.Value;
                    if (op == "NOT")
                    {
                        operatorValue = "<>";
                    }
                    // Handle other potential operators if needed
                }

                if (leftParam != null && rightParam != null)
                {
                    leftParam = $"Global.{leftParam}";
                    conditions.Add($"{leftParam} {operatorValue} \"{rightParam}\"");
                }
            }

            return string.Join(" && ", conditions);
        }

        private static void ProcessInnerElements(XElement containerElement, string condition, RepeatPromptsModel model)
        {
            var promptList = new List<PromptModel>();

            foreach (var promptElement in containerElement.Elements("prompt"))
            {
                var promptType = promptElement.Attribute("type")?.Value;
                var promptExpr = promptElement.Attribute("expr")?.Value;
                if (promptExpr != null)
                {
                    promptExpr = StateUtility.SimplifyCondition(promptExpr);
                }

                if (!string.IsNullOrEmpty(promptExpr) && promptType == "custom")
                {
                    string text = $"{{Global.{promptExpr}}}";
                    var promptModel = new PromptModel
                    {
                        PromptId = promptElement.Attribute("id")?.Value,
                        Text = text
                    };
                    promptList.Add(promptModel);
                }
                else
                {
                    var promptSegments = promptElement.Element("prompt-segments");
                    if (promptSegments != null)
                    {
                        var audiofiles = promptSegments.Elements("audiofile");
                        foreach (var audiofile in audiofiles)
                        {
                            string text = audiofile.Attribute("text")?.Value;
                            if (!string.IsNullOrEmpty(text))
                            {
                                var promptModel = new PromptModel
                                {
                                    PromptId = promptElement.Attribute("id")?.Value,
                                    Text = text
                                };
                                promptList.Add(promptModel);
                            }
                        }
                    }
                }
            }

            if (promptList.Count > 0)
            {
                var key = string.IsNullOrEmpty(condition) ? "default" : condition;
                model.ConditionPrompts[key] = promptList;
            }
        }

        private static void ProcessDefaultPrompts(XElement audioElement, RepeatPromptsModel model)
        {
            var promptList = new List<PromptModel>();

            foreach (var promptElement in audioElement.Elements("prompt"))
            {
                var promptSegments = promptElement.Element("prompt-segments");
                if (promptSegments != null)
                {
                    var audiofiles = promptSegments.Elements("audiofile");
                    foreach (var audiofile in audiofiles)
                    {
                        string text = audiofile.Attribute("text")?.Value;
                        if (!string.IsNullOrEmpty(text))
                        {
                            var promptModel = new PromptModel
                            {
                                PromptId = promptElement.Attribute("id")?.Value,
                                Text = text
                            };
                            promptList.Add(promptModel);
                        }
                    }
                }
            }

            if (promptList.Count > 0)
            {
                model.ConditionPrompts["default"] = promptList;
            }
        }
    }
}
