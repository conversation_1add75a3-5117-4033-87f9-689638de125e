﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <RootNamespace>NDFToCopilotStudioConverter</RootNamespace>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	
	<ItemGroup>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2"/>
        <PackageReference Include="YamlDotNet" Version="15.3.0" />
        <PackageReference Include="Google.Protobuf" Version="3.27.2" />
        <PackageReference Include="Microsoft.Bot.ObjectModel" Version="2025.6.1-1" />
        <PackageReference Include="Microsoft.Bot.ObjectModel.NodeGenerators" Version="2025.6.1-1" />
        <PackageReference Include="NUnit" Version="4.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.App" />
        <PackageReference Include="System.Web.Http" Version="4.0.0"/>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.10"/>
        <PackageReference Include="ExternalGrammarConverter" Version="1.0.0-ci-20250530-212356" />
	</ItemGroup>
  <!-- <ItemGroup>
        <Reference Include="ExternalGrammarConverter">
          <HintPath>libs\net8.0\ExternalGrammarConverter.dll</HintPath>
          <Private>True</Private>
        </Reference>
  </ItemGroup> -->
	<!-- <ItemGroup>
    <Reference Include="ExternalGrammarConverter">
      <HintPath>libs\net8.0\ExternalGrammarConverter.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Bot.ObjectModel">
      <HintPath>libs\net8.0\Microsoft.Bot.ObjectModel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Bot.ObjectModel.NodeGenerators">
      <HintPath>libs\net8.0\Microsoft.Bot.ObjectModel.NodeGenerators.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>libs\net8.0\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Bot.ObjectModel.PowerFx">
      <HintPath>libs\net8.0\Microsoft.Bot.ObjectModel.PowerFx.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Bot.Schema">
      <HintPath>libs\net8.0\Microsoft.Bot.Schema.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.PowerFx.Core">
      <HintPath>libs\net8.0\Microsoft.PowerFx.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.PowerFx.Interpreter">
      <HintPath>libs\net8.0\Microsoft.PowerFx.Interpreter.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.PowerFx.Json">
      <HintPath>libs\net8.0\Microsoft.PowerFx.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.PowerFx.LanguageServerProtocol">
      <HintPath>libs\net8.0\Microsoft.PowerFx.LanguageServerProtocol.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.PowerFx.Transport.Attributes">
      <HintPath>libs\net8.0\Microsoft.PowerFx.Transport.Attributes.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup> -->

</Project>
