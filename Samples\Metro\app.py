import json
import re

def clean_vocab_entry(vocab_entry):
    """Clean vocabulary entry by removing all brackets, parentheses, and pipes while keeping all words"""
    # Remove all square brackets, keeping content
    cleaned = re.sub(r'\[([^\]]+)\]', r'\1', vocab_entry)

    # Remove all parentheses and pipes, keeping all alternative words
    # This handles complex patterns like ((need to)|(want to)|(would like to))
    # First, replace pipes with spaces to separate alternatives
    cleaned = re.sub(r'\|', ' ', cleaned)

    # Remove all remaining parentheses
    cleaned = re.sub(r'[()]', '', cleaned)

    # Clean up multiple spaces and return
    cleaned = re.sub(r'\s+', ' ', cleaned)
    return cleaned.strip()

def flatten_external_grammar(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    flattened_data = []
    for item in data:
        flattened_item = {
            "Entity": item.get("Entity"),
            "Grammars": [],
            "SWI_meaning": [],
            "EntityType": item.get("EntityType")
        }

        # Split Grammers on \r\n if needed
        for g in item.get("Grammars", []):
            flattened_item["Grammars"].extend(g.splitlines())

        # Flatten SWI_meaning vocab
        for meaning in item.get("SWI_meaning", []):
            name = meaning.get("name")
            vocab_list = []
            for vocab_block in meaning.get("vocab", []):
                # Clean each vocab entry by removing brackets, parentheses, and pipes
                raw_values = vocab_block.get("values", [])
                for value in raw_values:
                    cleaned_value = clean_vocab_entry(value)
                    vocab_list.append(cleaned_value)
            flattened_item["SWI_meaning"].append({
                "Name": name,
                "Vocab": vocab_list
            })

        flattened_data.append(flattened_item)

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(flattened_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Flattened JSON written to {output_file}")

# Example usage
flatten_external_grammar(
    "ExternalGrammarEntityMapping_131946.json",
    "ExternalGrammarEntityMapping_simplified.json"
)
