﻿using Microsoft.Bot.ObjectModel;
using Microsoft.Bot.ObjectModel.NodeGenerators;
using Microsoft.Bot.Schema;
using NUnit.Framework;
using NUnit.Framework.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using XmlProcessing;
using static Google.Protobuf.WellKnownTypes.Field.Types;

namespace NDFToCopilotStudioConverter
{
    public class CustomStateConvertor
    {
        private CustomStateModel _component;
        private Guid _botId;

        public CustomStateConvertor(CustomStateModel component, Guid botId)
        {
            _component = component;
            _botId = botId;
        }

        public DialogComponent ConvertToDialogComponent()
        {
            DialogComponent.Builder dialogComponentBuilder = new DialogComponent.Builder();
            dialogComponentBuilder.DisplayName = _component.Id;
            dialogComponentBuilder.ParentBotId = _botId;
            dialogComponentBuilder.SchemaName = new DialogSchemaName("topic." + _component.Id);
            BeginDialog.Builder beginDialog = new BeginDialog().ToBuilder();

            TriggerBase.Builder triggerBase;
           // Console.WriteLine(_component.dialogName);
            if (_component.Id.Equals("ConversationStart", StringComparison.OrdinalIgnoreCase))
            {
                triggerBase = new OnConversationStart().ToBuilder();
            }
            else
            {
                triggerBase = new OnSystemRedirect().ToBuilder();
            }

            triggerBase.Id = _component.Id;

            if(_component != null)
            {
                if (_component.IsStartNode)
                {
                    ConvertNode(triggerBase, _component);
                }
                else
                {
                    ConvertNode(triggerBase, _component);
                }
            }
            EndDialog.Builder endDialogBuilder = new EndDialog.Builder()
            {
                Id = "return"
            };
            triggerBase.Actions.Add(endDialogBuilder);

            AdaptiveDialog.Builder adaptiveDialog = new AdaptiveDialog.Builder();
            adaptiveDialog.BeginDialog = triggerBase;

            dialogComponentBuilder.Dialog = adaptiveDialog.Build();

            return dialogComponentBuilder.Build();
        }

        private void ConvertNode(TriggerBase.Builder triggerBase, StateModel node)
        {
            Console.WriteLine("Processing node: " + node.Id);

            if (node is CustomStateModel customStateNode)
            {
                ConvertCustomStateNode(triggerBase, customStateNode);
            }
           
        }

        private void ConvertSubdialogStateNodeForCustomNode(TriggerBase.Builder triggerBase, CustomStateModel subdialogStateModel)
        {
            
            if (subdialogStateModel != null)
            {
                string dialogName = "";
                if (subdialogStateModel.Id.Contains("_"))
                {
                    dialogName = subdialogStateModel.Id.Split('_')[1];
                }
              
                BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                {
                    Id = subdialogStateModel.Id,
                    DisplayName = subdialogStateModel.Id,
                    Dialog = DialogExpression.Literal("topic." + dialogName)

                };

                triggerBase.Actions.Add(redirectConversation);

                DialogComponent.Builder dialogComponentBuilder = new DialogComponent.Builder();
                dialogComponentBuilder.DisplayName = dialogName;
                dialogComponentBuilder.SchemaName = new DialogSchemaName("topic." + dialogName);
                BeginDialog.Builder beginDialog = new BeginDialog().ToBuilder();

                 triggerBase = new OnSystemRedirect();
            }
        }

        private void ConvertCustomStateNode(TriggerBase.Builder triggerBase, CustomStateModel customStateNode)
        {
            Boolean isRegularCustomState = false;
            if (customStateNode != null && customStateNode.Id.Contains("_SD"))
            {
                isRegularCustomState = true;
                ConvertSubdialogStateNodeForCustomNode(triggerBase, customStateNode);
            }
            else if (customStateNode != null && customStateNode.Id == "end")
            {
                isRegularCustomState = true;
                EndConversation.Builder endConversation = new EndConversation.Builder()
                {
                    Id = customStateNode.Id,
                    DisplayName = customStateNode.Id
                };
                triggerBase.Actions.Add(endConversation);
            }
            else if (customStateNode != null && customStateNode.ConditionTransitionsList != null && customStateNode.ConditionTransitionsList.Count >= 1)
            {
                if (customStateNode.ConditionTransitionsList[0].Count == 1)
                {
                    var transitions = customStateNode.ConditionTransitionsList[0];
                    var model = transitions[0];
                    if (model != null && model.Transitions.next != null)
                    {
                        if (model.Transitions.next.Contains("_SD"))
                        {
                            isRegularCustomState = true;
                            String dialogName = model.Transitions.next;

                            BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                            {
                                Id = dialogName,
                                Dialog = DialogExpression.Literal("topic." + dialogName),
                                

                            };

                            triggerBase.Actions.Add(redirectConversation);

                            DialogComponent.Builder dialogComponentBuilder = new DialogComponent.Builder();
                            dialogComponentBuilder.DisplayName = dialogName;
                            dialogComponentBuilder.SchemaName = new DialogSchemaName("topic." + dialogName);
                            BeginDialog.Builder beginDialog = new BeginDialog().ToBuilder();

                            triggerBase = new OnSystemRedirect();
                        }
                    }
                }
            }
           
            if(isRegularCustomState == false)
            {
                ConditionGroup.Builder conditionGroupBuilder = new ConditionGroup.Builder()
                {
                    Id = customStateNode.Id,
                    DisplayName = customStateNode.Id
                };

                // Create a condition item builder
                ConditionItem.Builder conditionItemBuilder = new ConditionItem.Builder()
                {
                    Id = "conditionItem_" + ObjectModelHelper.GenerateRandomID(),
                    Condition = StateUtility.TransformCondition("=true")
                };

                foreach (var sessionMapping in customStateNode.SessionMappings)
                {
                    if (string.IsNullOrEmpty(sessionMapping.value))
                    {
                        conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                    }
                    else
                    {
                        conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, customStateNode.Id));
                    }
                }

                bool isDefault = false;
                // Process condition transitions
                if (customStateNode.ConditionTransitionsList.Count > 0)
                {
                    foreach (var conditionTransitions in customStateNode.ConditionTransitionsList)
                    {
                        foreach (var conditionModel in conditionTransitions)
                        {
                            if (conditionModel.Condition == "default")
                            {
                                isDefault = true;
                                //processSessionMapping(triggerBase, conditionModel.Transitions.sessionMappings);
                                foreach (var sessionMapping in conditionModel.Transitions.sessionMappings)
                                {
                                    if (string.IsNullOrEmpty(sessionMapping.value))
                                    {
                                        conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                                    }
                                    else
                                    {
                                        conditionItemBuilder.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, customStateNode.Id));
                                    }
                                }

                                if (!string.IsNullOrEmpty(conditionModel.eventCalls.Next))
                                {
                                    //string gotoNextNode = conditionModel.eventCalls.Next;

                                    //BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                                    //{
                                    //    Id = customStateNode.Id + ObjectModelHelper.GenerateRandomID(),
                                    //    Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                                    //};

                                    BeginDialog.Builder beginDialog = new BeginDialog().ToBuilder();

                                     triggerBase = new OnSystemRedirect();

                                    if (conditionModel.eventCalls.Next == "escalate")
                                    {
                                        triggerBase = new OnEscalate().ToBuilder();
                                    }
                                    else if (conditionModel.eventCalls.Next == "error")
                                    {
                                        triggerBase = new OnError().ToBuilder();
                                    }
                                    else if (conditionModel.eventCalls.Next == "fallback")
                                    {
                                        triggerBase = new OnUnknownIntent().ToBuilder();
                                    }

                                    triggerBase.Id = customStateNode.Id + ObjectModelHelper.GenerateRandomID();
                                    AdaptiveDialog.Builder adaptiveDialog = new AdaptiveDialog.Builder();
                                    adaptiveDialog.BeginDialog = triggerBase;

                                    conditionItemBuilder.Actions.Add(beginDialog);
                                }
                                if(conditionModel.Transitions.next != null)
                                {
                                    string gotoNextNode = conditionModel.Transitions.next;
                                    BeginDialog.Builder redirectConversation = new BeginDialog.Builder()
                                    {
                                        Id = customStateNode.Id + ObjectModelHelper.GenerateRandomID(),
                                        Dialog = DialogExpression.Literal("topic." + gotoNextNode)

                                    };
                                    conditionItemBuilder.Actions.Add(redirectConversation);
                                    
                                }
                            }
                            
                        }
                        
                    }
                }

                // Add the condition item to the condition group
                if (isDefault == true)
                {
                    if (conditionItemBuilder.Condition != null)
                    {
                        conditionGroupBuilder.Conditions.Add(conditionItemBuilder.Build());
                    }
                }
                // Add the condition group to the trigger base actions
                if (conditionGroupBuilder.Conditions.Count > 0)
                {
                    triggerBase.Actions.Add(conditionGroupBuilder.Build());
                }
            }
        }

        private void processSessionMapping(TriggerBase.Builder beginDialog, List<SessionMappingModel> sessionMappings, string stateId)
        {
            if (sessionMappings != null)
            {
                foreach (var sessionMapping in sessionMappings)
                {
                    if (sessionMapping.value == null || sessionMapping.value == "")
                    {
                        beginDialog.Actions.Add(ObjectModelHelper.SetVariableToBlank("Global." + sessionMapping.key));
                    }
                    else
                    {
                        beginDialog.Actions.Add(ObjectModelHelper.SetVariable("Global." + sessionMapping.key, sessionMapping.value, sessionMapping.type, stateId));
                    }

                }
            }
        }

    }

}
