﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using XmlProcessing;

namespace NDFToCopilotStudioConverter
{
    public class PlayStateService
    {
        public static PlayStateModel ProcessState(XElement stateElement)
        {
            var model = new PlayStateModel
            {
                Id = stateElement.Attribute("id")?.Value,
                Type = stateElement.Name.LocalName,
                ConditionTransitionsList = new List<List<ConditionModel>>(), // List of condition lists
                ConditionPromptList = new List<List<ConditionModel>>(),
                SessionMappings = new List<SessionMappingModel>(),
                prompts = new List<PromptModel>()
            };

            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(stateElement.Elements("session-mapping"));
            model.SessionMappings.AddRange(sessionMappings);
            var varMappings = SessionMappingHelper.ProcessSessionMappings(stateElement.Elements("var"));
            model.SessionMappings.AddRange(varMappings);

            //Adding the code for ifCondition if it is outside audio or action tag
            var ifElementsOutside = stateElement.Elements("if");
            if (ifElementsOutside != null)
                {
                    foreach (var ifElement in ifElementsOutside)
                        {
                            var conditionList = new List<ConditionModel>(); // List to store conditions for this 'if' block
                            ProcessTopIfCondition(ifElement, conditionList);

                            model.ConditionPromptList.Add(conditionList); // Add this list to the model
                        }
                }

            var audioElement = stateElement.Element("audio");
            if (audioElement != null)
            {
                
                    var ifElements = audioElement.Elements("if");
                    if (ifElements != null)
                    {
                        foreach (var ifElement in ifElements)
                        {
                            var conditionList = new List<ConditionModel>(); // List to store conditions for this 'if' block
                            ProcessTopIfCondition(ifElement, conditionList);

                            model.ConditionPromptList.Add(conditionList); // Add this list to the model
                        }
                    }
                    // If no <if> elements are present, process the default action
                    if (!ifElements.Any())
                    {
                        var conditionList = new List<ConditionModel>();
                        var conditionalModel = new ConditionModel();
                        conditionalModel.Condition = "default";
                        conditionalModel.Transitions = ProcessAudioElement(audioElement);
                        conditionList.Add(conditionalModel);
                        model.ConditionPromptList.Add(conditionList);
                    }
                foreach (var promptElement in audioElement.Elements("prompt"))
                {
                    var promptModel = ExtractPromptModel(promptElement);
                    if (promptModel != null)
                    {
                        model.prompts.Add(promptModel);
                    }
                }

                //Adding code if goto is present in audio tag
                var gotoDialog = audioElement.Element("gotodialog");
                if (gotoDialog != null)
                {
                    //26638
                    var nextAttr = gotoDialog.Attribute("next")?.Value;
                    if (!string.IsNullOrEmpty(nextAttr)){
                        if (nextAttr.Contains(".dvxml#")){
                            var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                            if (parts.Length == 2)
                            {
                                model.gotoDialogElement = parts[0];      // before .dvxml#
                                SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                    key = "nextState",
                                    value = parts[1]
                                };
                                model.SessionMappings.Add(sessionMappingModel);
                                //model.gotoDialogReference = parts[1];    // after #
                            }
                        }
                        else{
                            model.gotoDialogElement = nextAttr;
                        }
                    }
                    //26638
                    //model.gotoDialogElement = gotoDialogElement.Attribute("next")?.Value;
                }


            }

            var actionElement = stateElement.Element("action");
            if (actionElement != null)
            {
                var ifElements = actionElement.Elements("if");
                if (ifElements != null)
                {
                    foreach (var ifElement in ifElements)
                    {
                        var conditionList = new List<ConditionModel>(); // List to store conditions for this 'if' block
                        ProcessTopIfCondition(ifElement, conditionList);

                        model.ConditionTransitionsList.Add(conditionList); // Add this list to the model
                    }
                }
                // If no <if> elements are present, process the default action
                if (!ifElements.Any())
                {
                    var conditionList = new List<ConditionModel>();
                    var conditionalModel = new ConditionModel();
                    conditionalModel.Condition = "default";
                    conditionalModel.Transitions = ProcessActionElement(actionElement);
                    conditionList.Add(conditionalModel);
                    model.ConditionTransitionsList.Add(conditionList);
                }
            
            }

            var gotoDialogElement = stateElement.Element("gotodialog");
            if (gotoDialogElement != null)
            {
                //26638
                var nextAttr = gotoDialogElement.Attribute("next")?.Value;
                if (!string.IsNullOrEmpty(nextAttr)){
                    if (nextAttr.Contains(".dvxml#")){
                        var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                        if (parts.Length == 2)
                        {
                            model.gotoDialogElement = parts[0];      // before .dvxml#
                            SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                key = "nextState",
                                value = parts[1]
                            };
                            model.SessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                        }
                    }
                    else{
                        model.gotoDialogElement = nextAttr;
                    }
                }
                //26638
                //model.gotoDialogElement = gotoDialogElement.Attribute("next")?.Value;
            }
            return model;
        }

        public static TransitionModel ProcessAudioElement(XElement audioElement)
        {
            var transitionModel = new TransitionModel()
            {
                PromptList = new List<PromptModel>()
            };

            if (audioElement != null)
            {
                // Get all prompt elements
                var prompts = audioElement.Elements("prompt");
                Console.WriteLine($"Number of prompt elements: {prompts.Count()}");

                foreach (var promptElement in audioElement.Elements("prompt"))
                {
                    var promptModel = ExtractPromptModel(promptElement);
                    if (promptModel != null)
                    {
                        transitionModel.PromptList.Add(promptModel);
                    }
                }

                foreach (var promptElement in audioElement.Elements("for"))
                {
                    var promptModel = ExtractForModel(promptElement);
                    if (promptModel != null)
                    {
                        transitionModel.PromptList.Add(promptModel);
                    }
                }
            }

            return transitionModel;
        }

        public static TransitionModel ProcessActionElement(XElement actionElement)
        {
           
            var gotoElement = actionElement.Element("gotodialog");
           
            var transitionModel = new TransitionModel
            {
                label = actionElement.Attribute("label")?.Value,  // Add label if available
                next = actionElement.Attribute("next")?.Value
            };
            if (gotoElement != null)
            {
                //26638
                var nextAttr = gotoElement.Attribute("next")?.Value;
                if (!string.IsNullOrEmpty(nextAttr)){
                    if (nextAttr.Contains(".dvxml#")){
                        var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                        if (parts.Length == 2)
                        {
                            transitionModel.next = parts[0]+ ".dvxml";      // before .dvxml#
                            SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                key = "nextState",
                                value = parts[1]
                            };
                            transitionModel.sessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                        }
                    }
                    else{
                        transitionModel.next = nextAttr+ ".dvxml";
                    }
                }
                //26638
                //transitionModel.next = gotoElement.Attribute("next")?.Value + ".dvxml";
            }

            // Check for session mappings (if any)
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("session-mapping"));
            transitionModel.sessionMappings.AddRange(sessionMappings);
            var varMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("var"));
            transitionModel.sessionMappings.AddRange(varMappings);

            return transitionModel;
        }
        private static void ProcessTopIfCondition(XElement ifElement, List<ConditionModel> conditionList)
        {
            //string condition = ifElement.Attribute("cond")?.Value;
            string type = ifElement.Attribute("type")?.Value;
            string condition;
            if (type != null && type == "java")
            {
                condition = BuildConditionString(ifElement);
            }
            else
            {
                condition = ifElement.Attribute("cond")?.Value;
            }
            var transition = ProcessConditionElement(ifElement);

            // Create a ConditionModel for the top-level 'if'
            var topCondition = new ConditionModel
            {
                Condition = condition,
                Transitions = transition,
                IsIfCondition = true
            };

            // Process nested conditions
            ProcessNestedConditions(ifElement, topCondition);

            // Add the top condition to the list
            conditionList.Add(topCondition);

            // Process sibling 'elseif' and 'else' conditions
            ProcessSiblingConditions(ifElement, conditionList);
        }

        private static void ProcessNestedConditions(XElement parentElement, ConditionModel parentCondition)
        {
            // Extract nested 'if' condition from the top 'if' element
            // var nestedIfElement = parentElement.Element("if");
            // if (nestedIfElement != null)
            foreach (var nestedIfElement in parentElement.Elements())
            {
                //Adding the if condition so that it should only process with if conditions
                if(nestedIfElement.Name.LocalName != "if")
                {
                    continue;
                }
                // Add nested 'if' condition to the parent's InnerConditionTransitions
                // string nestedCondition = nestedIfElement.Attribute("cond")?.Value;
                string type = nestedIfElement.Attribute("type")?.Value;
                string nestedCondition;
                if (type != null && type == "java")
                {
                    nestedCondition = BuildConditionString(nestedIfElement);
                }
                else
                {
                    nestedCondition = nestedIfElement.Attribute("cond")?.Value;
                }
                var nestedTransition = ProcessConditionElement(nestedIfElement);

                ConditionModel nestedConditionModel = new ConditionModel
                {
                    Condition = nestedCondition,
                    Transitions = nestedTransition,
                    IsIfCondition = true
                };

                parentCondition.Transitions.InnerConditionTransitions.Add(nestedConditionModel);

                //Adding the below code for nested if condition
                ProcessNestedConditions(nestedIfElement, parentCondition);

                // Process nested 'elseif' and 'else' within the nested 'if'
                ProcessSiblingConditions(nestedIfElement, parentCondition.Transitions);
            }
        }

        private static void ProcessSiblingConditions(XElement parentElement, List<ConditionModel> conditionList)
        {
            // Process 'elseif' elements
            var elseifElements = parentElement.Elements("elseif");
            foreach (var elseifElement in elseifElements)
            {
                //string condition = elseifElement.Attribute("cond")?.Value;
                string type = elseifElement.Attribute("type")?.Value;
                string condition = elseifElement.Attribute("cond")?.Value;
                if (type == null && condition == null)
                {
                    condition = BuildConditionString(elseifElement);
                }
                var transition = ProcessConditionElement(elseifElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };
                ProcessNestedConditions(elseifElement, condObj);
                // Add to conditionList
                conditionList.Add(condObj);
            }

            // Process 'else' element
            var elseElement = parentElement.Element("else");
            if (elseElement != null)
            {
                string condition = "else";
                var transition = ProcessConditionElement(elseElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };
                ProcessNestedConditions(elseElement, condObj);
                // Add to conditionList
                conditionList.Add(condObj);
            }
        }

        private static void ProcessSiblingConditions(XElement parentElement, TransitionModel parentTransition)
        {
            // Process 'elseif' elements
            var elseifElements = parentElement.Elements("elseif");
            foreach (var elseifElement in elseifElements)
            {
                string type = elseifElement.Attribute("type")?.Value;
                string condition = elseifElement.Attribute("cond")?.Value;
                if (type==null && condition == null)
                {
                    condition = BuildConditionString(elseifElement);
                }
                
                var transition = ProcessConditionElement(elseifElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };

                

                // Add to InnerConditionTransitions
                parentTransition.InnerConditionTransitions.Add(condObj);
            }

            // Process 'else' element
            var elseElement = parentElement.Element("else");
            if (elseElement != null)
            {
                string condition = "else";
                var transition = ProcessConditionElement(elseElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };

                //Adding the below code for transition in else
                ProcessNestedConditions(elseElement, condObj);

                // Add to InnerConditionTransitions
                parentTransition.InnerConditionTransitions.Add(condObj);
            }
        }

        private static TransitionModel ProcessConditionElement(XElement element)
        {
            var transition = new TransitionModel
            {
                sessionMappings = new List<SessionMappingModel>(),
                InnerConditionTransitions = new List<ConditionModel>(),// Ensure it's empty for flat conditions
                PromptList = new List<PromptModel>()
            };

            // Use SessionMappingHelper to process session-mapping elements directly under the condition element
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(element.Elements("session-mapping"));
            transition.sessionMappings.AddRange(sessionMappings);
            var varMappings = SessionMappingHelper.ProcessSessionMappings(element.Elements("var"));
            transition.sessionMappings.AddRange(varMappings);

            //process audio element
            /*var audioElement = element.Element("audio");
            if (audioElement != null)
            {
                foreach (var promptElement in audioElement.Elements("prompt"))
                {
                    var promptModel = ExtractPromptModel(promptElement);
                    if (promptModel != null)
                    {
                        transition.PromptList.Add(promptModel);
                    }
                }

            }*/

            foreach (var promptElement in element.Elements("prompt"))
            {
                var promptModel = ExtractPromptModel(promptElement);
                if (promptModel != null)
                {
                    transition.PromptList.Add(promptModel);
                }
            }
            foreach (var promptElement in element.Elements("for"))
            {
                var promptModel = ExtractForModel(promptElement);
                if (promptModel != null)
                {
                    transition.PromptList.Add(promptModel);
                }
            }

            // Process action element if present
            var actionElement = element.Element("action");
            var gotoElement = element.Element("gotodialog");
            if (actionElement != null)
            {
                transition.label = actionElement.Attribute("label")?.Value ?? "fixedtransition";
                transition.next = actionElement.Attribute("next")?.Value;


                // Process session-mapping elements inside the action
                var actionSessionMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("session-mapping"));
                transition.sessionMappings.AddRange(actionSessionMappings);
                var actionVarMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("var"));
                transition.sessionMappings.AddRange(actionVarMappings);
            }
            if (gotoElement != null)
            {
                //26638
                var nextAttr = gotoElement.Attribute("next")?.Value;
                if (!string.IsNullOrEmpty(nextAttr)){
                    if (nextAttr.Contains(".dvxml#")){
                        var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                        if (parts.Length == 2)
                        {
                            transition.next = parts[0]+ ".dvxml";      // before .dvxml#
                            SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                key = "nextState",
                                value = parts[1]
                            };
                            transition.sessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                        }
                    }
                    else{
                        transition.next = nextAttr+ ".dvxml";
                    }
                }
                //26638
                //transition.next = gotoElement.Attribute("next")?.Value + ".dvxml";
            }

            return transition;
        }
      
        private static PromptModel ExtractPromptModel(XElement promptElement)
        {
            // Extract prompt segments
            String promptType = promptElement.Attribute("type")?.Value;
          
            if ((promptType == "custom"))
            {
                String customText = "custom text from java class";
                return new PromptModel
                {
                    PromptId = "custom_prompt_id",
                    Text = customText,
                };
            }
            var promptSegments = promptElement.Element("prompt-segments");
            if (promptSegments != null)
            {
                // Extract audiofile elements
                var audiofiles = promptSegments.Elements("audiofile");
                foreach (var audiofile in audiofiles)
                {
                    string text = audiofile.Attribute("text")?.Value;
                    String promptCond = promptElement.Attribute("cond")?.Value;

                    if (!string.IsNullOrEmpty(text) && !string.IsNullOrEmpty(promptCond))
                    {
                        // Successfully created PromptModel from audiofile
                        return new PromptModel
                        {
                            PromptId = promptElement.Attribute("id")?.Value,
                            Text = text,
                            Cond = promptCond
                        };
                    }
                    else if (!string.IsNullOrEmpty(text))
                    {
                        // Successfully created PromptModel from audiofile
                        return new PromptModel
                        {
                            PromptId = promptElement.Attribute("id")?.Value,
                            Text = text
                        };
                    }
                }
            }
            else
            {
                // Use expr attribute from prompt element
                string exprValue = promptElement.Attribute("expr")?.Value;
                if (!string.IsNullOrEmpty(exprValue))
                {
                    string text = "Global." + StateUtility.SimplifyCondition(exprValue);
                    // Successfully created PromptModel from expr attribute
                    return new PromptModel
                    {
                        PromptId = "customPrompt", // Assigning a default ID for custom prompts
                        Text = text
                    };
                }
            }

            return null;
        }
        private static PromptModel ExtractForModel(XElement promptElement)
        {
            if (promptElement != null)
            {
                // Extract the listPath attribute value from the <for> element
                string listPath = promptElement.Attribute("listPath")?.Value;
                if (!string.IsNullOrEmpty(listPath))
                {
                    // Construct the text value by concatenating "Global." with listPath
                    string text = "Global." + StateUtility.SimplifyCondition(listPath);

                    // Return a new PromptModel with the constructed text
                    return new PromptModel
                    {
                        PromptId = "customPrompt", // Assigning a default ID for custom prompts
                        Text = text
                    };
                }
            }
            // Return null if promptElement is null or listPath is not found
            return null;
        }
        private static string BuildConditionString(XElement conditionElement)
        {
            var conditions = new List<string>();

            foreach (var condition in conditionElement.Elements("condition"))
            {
                string leftParam = condition.Elements("param")
                    .FirstOrDefault(p => p.Attribute("scope")?.Value == "session")?.Attribute("value")?.Value;
                string rightParam = condition.Elements("param")
                    .FirstOrDefault(p => p.Attribute("name")?.Value == "compare")?.Attribute("value")?.Value;

                // Default operator value
                string operatorValue = "==";

                // Check each param element for operator attribute
                foreach (var param in condition.Elements("param"))
                {
                    string op = param.Attribute("value")?.Value;
                    if (op != null)
                    {
                        Console.WriteLine("operand value is:" + op);
                        if (op == "NOT")
                        {
                            operatorValue = "!=";
                        }
                        // Handle other potential operators if needed
                    }
                }

                /*if (leftParam != null && rightParam != null)
                {
                    leftParam = $"Global.{leftParam}";
                    conditions.Add($"{leftParam} {operatorValue} \"{rightParam}\"");
                }*/
                if (leftParam != null && rightParam != null)
                {
                    leftParam = $"{leftParam}";

                    string formattedRightParam = $"\"{rightParam}\"";

                    /*// Check if rightParam is a string
                    if (double.TryParse(rightParam.ToString(), out _) == false &&
                        bool.TryParse(rightParam.ToString(), out _) == false)
                    {
                        // Treat as string literal
                        formattedRightParam = $"\"{rightParam}\"";
                    }
                    else
                    {
                        // Treat as number or boolean, no quotes
                        formattedRightParam = rightParam.ToString();
                    }
*/
                    conditions.Add($"{leftParam} {operatorValue} {formattedRightParam}");
                }

            }

            return string.Join(" && ", conditions);
        }

    }

}
