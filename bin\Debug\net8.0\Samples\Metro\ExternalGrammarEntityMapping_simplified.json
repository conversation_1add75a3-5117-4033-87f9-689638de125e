[{"Entity": "AE1005_AnythingElse_DM", "Grammars": ["AE1005_AnythingElse_DM.grxml", "AE1005_AnythingElse_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yea", "yes"]}, {"Name": "no", "Vocab": ["no", "nope"]}], "EntityType": "ClosedList"}, {"Entity": "AP1005_ACPStart_DM", "Grammars": ["AP1005_ACPStart_DM.grxml", "AP1005_ACPStart_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "send_text", "Vocab": ["send me a text"]}, {"Name": "main_menu", "Vocab": ["main menu"]}], "EntityType": "ClosedList"}, {"Entity": "AP1020_FirstStep_DM", "Grammars": ["AP1020_FirstStep_DM.grxml", "AP1020_FirstStep_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AP1030_SecondStep_DM", "Grammars": ["AP1030_SecondStep_DM.grxml", "AP1030_SecondStep_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AP1035_ApplyMetroSite_DM", "Grammars": ["AP1035_ApplyMetroSite_DM.grxml", "AP1035_ApplyMetroSite_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat_that", "Vocab": ["please repeat that please"]}, {"Name": "main_menu", "Vocab": ["main menu"]}], "EntityType": "ClosedList"}, {"Entity": "AP1040_ACPMenu_DM", "Grammars": ["AP1040_ACPMenu_DM.grxml", "AP1040_ACPMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat_that", "Vocab": ["please repeat that please"]}, {"Name": "no_text", "Vocab": ["I didn't did not receive a the it text"]}, {"Name": "declined", "Vocab": ["I it got was declined"]}, {"Name": "discount_dropped", "Vocab": ["my the discount got was dropped"]}], "EntityType": "ClosedList"}, {"Entity": "AP1045_DiscountDropped_DM", "Grammars": ["AP1045_DiscountDropped_DM.grxml", "AP1045_DiscountDropped_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AP1050_Declined_DM", "Grammars": ["AP1050_Declined_DM.grxml", "AP1050_Declined_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "text_me", "Vocab": ["send me a text"]}, {"Name": "main_menu", "Vocab": ["main menu"]}], "EntityType": "ClosedList"}, {"Entity": "AP1055_TextNotReceived_DM", "Grammars": ["AP1055_TextNotReceived_DM.grxml", "AP1055_TextNotReceived_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AR1005_OfferSMSLinkForAcctPinUpdate_DM", "Grammars": ["AR1005_OfferSMSLinkForAcctPinUpdate_DM.grxml", "AR1005_OfferSMSLinkForAcctPinUpdate_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["say that again", "repeat please that"]}, {"Name": "main-menu", "Vocab": ["return to main menu"]}], "EntityType": "ClosedList"}, {"Entity": "AU1010_AskSetUpAutoPay_DM", "Grammars": ["AU1010_AskSetUpAutoPay_DM.grxml", "AU1010_AskSetUpAutoPay_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AU1015_PlayAPInfoConfirmSetup_DM", "Grammars": ["AU1015_PlayAPInfoConfirmSetup_DM.grxml", "AU1015_PlayAPInfoConfirmSetup_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AU1020_AutoPayMenu_DM", "Grammars": ["AU1020_AutoPayMenu_DM.grxml", "AU1020_AutoPayMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "cancel-autopay", "Vocab": ["i'd like to cancel a my the automatic payment autopay please", "i need to want to would like to cancel a my the automatic payment autopay please"]}, {"Name": "manage-cards", "Vocab": ["i'd like to manage a my the payment card cards please", "i need to want to would like to manage a my the payment card cards please"]}], "EntityType": "ClosedList"}, {"Entity": "AU1100_GetSecurityCode_DM (Complex)", "Grammars": ["AU1100_GetSecurityCode_DM.grxml", "AU1100_GetSecurityCode_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "3347759", "Vocab": ["that code is three three four seven seven five nine"]}, {"Name": "681214", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "AU1117_AskResetInformation_DM", "Grammars": ["AU1117_AskResetInformation_DM.grxml", "AU1117_AskResetInformation_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AU1303_AskMakePayment_DM", "Grammars": ["AU1303_AskMakePayment_DM.grxml", "AU1303_AskMakePayment_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AU1304_ConfirmCancel_DM", "Grammars": ["AU1304_ConfirmCancel_DM.grxml", "AU1304_ConfirmCancel_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AU1305_AskUsePaymentCard_DM", "Grammars": ["AU1305_AskUsePaymentCard_DM.grxml", "AU1305_AskUsePaymentCard_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "AX1705_ExtensionTermsYN_DM (Complex)", "Grammars": ["AX1705_ExtensionTermsYN_DM.grxml", "AX1705_ExtensionTermsYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes"]}, {"Name": "no", "Vocab": ["no"]}, {"Name": "operator", "Vocab": ["a rep"]}], "EntityType": "ClosedList"}, {"Entity": "BB1007_AskOneTimePayment_DM", "Grammars": ["BB1007_AskOneTimePayment_DM.grxml", "BB1007_AskOneTimePayment_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "BB1225_SBINavigate_DM", "Grammars": ["BB1225_SBINavigate_DM.grxml", "BB1225_SBINavigate_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["say that again", "repeat please that"]}, {"Name": "next", "Vocab": ["next please"]}], "EntityType": "ClosedList"}, {"Entity": "BB1305_WrapMenu_DM", "Grammars": ["BB1305_WrapMenu_DM.grxml", "BB1305_WrapMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["say that again", "repeat please that"]}, {"Name": "make-payment", "Vocab": ["pay now", "make a payment now", "i need to wanna want to would like to pay now", "i need to wanna want to would like to make a payment now"]}, {"Name": "main_menu", "Vocab": ["return to main menu"]}, {"Name": "more-options", "Vocab": ["back to payment options"]}], "EntityType": "ClosedList"}, {"Entity": "BL1010_AccountBalance_DM", "Grammars": ["BL1010_AccountBalance_DM.grxml"], "SWI_meaning": [{"Name": "make_pmt", "Vocab": ["payment", "pay a an my the your monthly bill", "make a an my the your monthly payment"]}, {"Name": "my_features", "Vocab": ["a an my the your feature features"]}, {"Name": "tell_me_more", "Vocab": ["a an my the your plan", "tell me about a an my the your calling plan", "more information about on a an my the your calling plan"]}, {"Name": "done_here", "Vocab": ["done here", "i'm done here", "i don't want any of these choices"]}, {"Name": "repeat", "Vocab": ["repeat that", "hear that again"]}], "EntityType": "ClosedList"}, {"Entity": "BR1002_ConfirmTransferYN_DM", "Grammars": ["BR1002_ConfirmTransferYN_DM.grxml", "BR1002_ConfirmTransferYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["sure yeah yep maam please thanks thank you", "yes yes yes yes maam please thanks thank you", "that is correct right maam please thanks thank you", "that's correct right maam please thanks thank you"]}, {"Name": "no", "Vocab": ["no no no no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "BR1025_AcceptBCRTermsYN_DM", "Grammars": ["BR1025_AcceptBCRTermsYN_DM.grxml", "BR1025_AcceptBCRTermsYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["sure yeah yep maam please thanks thank you", "yes yes yes yes maam please thanks thank you", "that is correct right maam please thanks thank you", "that's correct right maam please thanks thank you"]}, {"Name": "no", "Vocab": ["no no no no maam thanks"]}], "EntityType": "ClosedList"}, {"Entity": "CF1010_CurrentFeatures_DM", "Grammars": ["CF1010_CurrentFeatures_DM.grxml", "CF1010_CurrentFeatures_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat that", "hear say that again"]}, {"Name": "add_feature", "Vocab": ["add features", "add a feature", "i need to wanna want to would like to add features", "i need to wanna want to would like to add a feature"]}, {"Name": "plan_details", "Vocab": ["plan details", "what's my rate plan"]}], "EntityType": "ClosedList"}, {"Entity": "DA1110_OfferGetDataPlanYN_DM", "Grammars": ["DA1110_OfferGetDataPlanYN_DM.grxml", "DA1110_OfferGetDataPlanYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "yes please", "yes i would", "yes i agree", "yes that's correct right"]}, {"Name": "no", "Vocab": ["nope", "no i disagree", "no thanks thank you", "no i wouldn't would not", "no that's incorrect wrong"]}], "EntityType": "ClosedList"}, {"Entity": "DA1335_AskContinueYN_DM", "Grammars": ["DA1335_AskContinueYN_DM.grxml", "DA1335_AskContinueYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yeah", "okay", "continue", "yes maam please yes", "please yeah yes go ahead"]}, {"Name": "no", "Vocab": ["nope", "wrong", "cancel", "not right now", "no cancel no", "no i don't want"]}], "EntityType": "ClosedList"}, {"Entity": "DC0030_AskIntent_DM (Complex)", "Grammars": ["DC0030_AskIntent_DM.grxml", "DC0030_AskIntent_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "dtmf_entry", "Vocab": []}, {"Name": "operator", "Vocab": ["agent"]}], "EntityType": "ClosedList"}, {"Entity": "DH1020_DeviceHandlingMenu_DM", "Grammars": ["DH1020_DeviceHandlingMenu_DM.grxml", "DH1020_DeviceHandlingMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "make-payment", "Vocab": ["payment", "my bill", "make a payment", "pay a my the bill", "i need to wanna want to would like to make a payment"]}, {"Name": "add-line", "Vocab": ["family plan", "add a device line phone", "i need to wanna want to would like to add a device line phone"]}, {"Name": "switch-phone", "Vocab": ["switch phone phones", "switch to another phone", "switch to a different phone", "another different switch phone", "i need to wanna want to would like to switch phone phones", "i need to wanna want to would like to switch to another phone", "i need to wanna want to would like to switch to a different phone"]}, {"Name": "change-phone_number", "Vocab": ["change a my phone number", "i need to wanna want to would like to change a my phone number"]}, {"Name": "report-phone_lost", "Vocab": ["lost or damaged phone", "broken damaged lost phone", "my device is damaged lost", "my device is damaged or lost", "my device is lost or damaged"]}, {"Name": "something-else_devicehandling", "Vocab": ["other options", "it is it's none of these those", "it is it's none something else"]}, {"Name": "main_menu", "Vocab": ["go back", "main menu"]}], "EntityType": "ClosedList"}, {"Entity": "DH1234_AskHavePhoneYN_DM", "Grammars": ["DH1234_AskHavePhoneYN_DM.grxml", "DH1234_AskHavePhoneYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yeah", "okay", "yes i have", "yes maam please yes"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no no", "no i don't do not have"]}], "EntityType": "ClosedList"}, {"Entity": "DH1235_DeviceCarrierYN_DM", "Grammars": ["DH1235_DeviceCarrierYN_DM.grxml", "DH1235_DeviceCarrierYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yeah", "yes please", "yeah yes i would"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "DH1238_BYODDevice_DM", "Grammars": ["DH1238_BYODDevice_DM.grxml", "DH1238_BYODDevice_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "inquire-store_location", "Vocab": ["store locator", "find locate a store", "i need to wanna want to would like to find locate a store"]}, {"Name": "say-something_else", "Vocab": ["i need it is it's something else"]}], "EntityType": "ClosedList"}, {"Entity": "DH1250_DeviceHandlingWrapMenu_DM", "Grammars": ["DH1250_DeviceHandlingWrapMenu_DM.grxml", "DH1250_DeviceHandlingWrapMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "troubleshoot", "Vocab": ["i problem", "i had have a problem", "troubleshoot troubleshooting a problem"]}, {"Name": "options", "Vocab": ["go back", "main menu", "more other options"]}], "EntityType": "ClosedList"}, {"Entity": "DH1295_AddLineWrap_DM", "Grammars": ["DH1295_AddLineWrap_DM.grxml", "DH1295_AddLineWrap_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "main_menu", "Vocab": ["main menu please"]}], "EntityType": "ClosedList"}, {"Entity": "DH1310_ESNSuccessWrap_DM", "Grammars": ["DH1310_ESNSuccessWrap_DM.grxml", "DH1310_ESNSuccessWrap_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat it that please"]}, {"Name": "main_menu", "Vocab": ["main menu please"]}], "EntityType": "ClosedList"}, {"Entity": "DH1320_AskPayNowYN_DM", "Grammars": ["DH1320_AskPayNowYN_DM.grxml", "DH1320_AskPayNowYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yeah", "okay", "yes pay right now", "yes maam please yes"]}, {"Name": "no", "Vocab": ["nope", "wrong", "not right now", "no i don't no"]}], "EntityType": "ClosedList"}, {"Entity": "DH1410_AskReadyActivateYN_DM", "Grammars": ["DH1410_AskReadyActivateYN_DM.grxml", "DH1410_AskReadyActivateYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yeah", "yeah yes", "yes yes yes", "yes i am sure thank you yeah", "yup i am ready to go ahead now", "yes i am i am ready to go ahead now", "yes i did do guess so have will would", "yes yeah i am ready to go ahead now", "yeah yes i am ready to go ahead now", "yes yes yes i am ready to go ahead now", "yes sure thank you i am ready to go ahead now", "yes i did do guess so have will would i am ready to go ahead now"]}, {"Name": "no", "Vocab": ["nah no", "no nope", "no no no", "no i would not", "no i am i'm it's not", "no it isn't thanks thank you", "no i didn't don't won't wouldn't"]}, {"Name": "repeat", "Vocab": ["repeat that", "hear say that again"]}, {"Name": "inquire-store_location", "Vocab": ["find locate a store"]}], "EntityType": "ClosedList"}, {"Entity": "DH1415_AcceptTermsYN_DM", "Grammars": ["DH1415_AcceptTermsYN_DM.grxml", "DH1415_AcceptTermsYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes yeah", "yeah yes", "yes yes yes", "yes i am sure thank you", "yes i did do guess so have will would", "yup i accept agree to the terms and conditions of service", "yes yeah i accept agree to the terms and conditions of service", "yeah yes i accept agree to the terms and conditions of service", "yes yes yes i accept agree to the terms and conditions of service", "yes i am sure thank you i accept agree to the terms and conditions of service", "yes i did do guess so have will would i accept agree to the terms and conditions of service"]}, {"Name": "no", "Vocab": ["nope", "nah no", "no no no", "no i would not", "no i am i'm it's not", "no i didn't don't won't wouldn't", "no it isn't nope thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "DT1020_PayNowYN_DM", "Grammars": ["DT1020_PayNowYN_DM.grxml", "DT1020_PayNowYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "okay", "yeah", "sure", "yes i am", "i'm ready", "yes maam please yes"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no no", "no i am i'm not"]}], "EntityType": "ClosedList"}, {"Entity": "DU1125_CappedRepeatYN_DM", "Grammars": ["DU1125_CappedRepeatYN_DM.grxml", "DU1125_CappedRepeatYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "okay", "sure", "yeah", "yes i would", "repeat that", "say that again", "yes maam please yes"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no no", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "DU1220_UnlimitedRepeatYN_DM", "Grammars": ["DU1220_UnlimitedRepeatYN_DM.grxml", "DU1220_UnlimitedRepeatYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yeah", "okay", "i would", "yes i would", "repeat that", "say that again", "yes maam please yes"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no no", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "DU1305_DataUsageWrapMenu_DM", "Grammars": ["DU1305_DataUsageWrapMenu_DM.grxml", "DU1305_DataUsageWrapMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "add-data", "Vocab": ["more data", "top up please", "add more data", "i would like to more data", "i would like to add more data", "i need to wanna want to add data", "i need to wanna want to add more data", "i need to wanna want to would like to top up please"]}, {"Name": "datatips", "Vocab": ["data tips please", "i need wanna want would like data tips please"]}], "EntityType": "ClosedList"}, {"Entity": "DU1315_InternetUsageWrapMenu_DM", "Grammars": ["DU1315_InternetUsageWrapMenu_DM.grxml", "DU1315_InternetUsageWrapMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "hear-plan_details", "Vocab": ["plan details information"]}, {"Name": "main_menu", "Vocab": ["main menu"]}], "EntityType": "ClosedList"}, {"Entity": "DU1405_DataMaximizerToggleYN_DM", "Grammars": ["DU1405_DataMaximizerToggleYN_DM.grxml", "DU1405_DataMaximizerToggleYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yeah", "okay", "i would", "yes i would", "yes maam please yes"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no no", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "DU1510_DataTipsRepeatYN_DM", "Grammars": ["DU1510_DataTipsRepeatYN_DM.grxml", "DU1510_DataTipsRepeatYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "okay", "yeah", "sure", "i would", "yes i would", "repeat that", "say that again", "yes maam please yes"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no no", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "EP1106_OfferOnePlanYN_DM", "Grammars": ["EP1106_OfferOnePlanYN_DM.grxml", "EP1106_OfferOnePlanYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yeah", "yeah yes", "yes yes yes", "yes i did do have will would", "yeah yes switch to this plan", "yeah yup switch to this plan", "yes i am sure thank you yeah", "yes yes yes switch to this plan", "yes i did do have will would switch to this plan", "yes i am sure thank you yeah switch to this plan"]}, {"Name": "no", "Vocab": ["nope thanks", "no nope thanks", "no nope thank you", "nah no thanks thank you", "no no no thanks thank you"]}, {"Name": "repeat", "Vocab": ["repeat that please", "please repeat that"]}], "EntityType": "ClosedList"}, {"Entity": "EP1205_ConfirmNewPlan_DM", "Grammars": ["EP1205_ConfirmNewPlan_DM.grxml", "EP1205_ConfirmNewPlan_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "correct", "yes thank you", "that's right", "you you've got it", "yes that's correct", "yes it is thanks", "yes it is correct right", "yes that's right thanks", "yes that is correct right"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no it isn't", "that's not correct", "that is not correct", "no it is it's not", "that is incorrect wrong", "no that's not correct right", "no that's incorrect wrong"]}], "EntityType": "ClosedList"}, {"Entity": "EP1305_HearPlanDetailsYN_DM", "Grammars": ["EP1305_HearPlanDetailsYN_DM.grxml", "EP1305_HearPlanDetailsYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "sure", "yeah i would", "yes i would please", "yes hear the those plan details again"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "EP1310_PlayDetailsAgainYN_DM", "Grammars": ["EP1310_PlayDetailsAgainYN_DM.grxml", "EP1310_PlayDetailsAgainYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yeah", "yes please", "yeah yes i would", "yes hear the those plan details again"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "ES1005_KeepingSameSIMCard_DM", "Grammars": ["ES1005_KeepingSameSIMCard_DM.grxml", "ES1005_KeepingSameSIMCard_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "same", "Vocab": ["i'd like the same one please", "keep keeping the same one please", "i would like the same one please", "i need want the same one please", "i'd like to keep the same one please", "i wanna keep the same one please", "i need to want to would like to keep the same one please"]}, {"Name": "new", "Vocab": ["a new one", "i have a new one"]}], "EntityType": "ClosedList"}, {"Entity": "ES1007_SimSwappedBlocked_DM", "Grammars": ["ES1007_SimSwappedBlocked_DM.grxml", "ES1007_SimSwappedBlocked_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ES1105_ConfirmANIAccountYN_DM", "Grammars": ["ES1105_ConfirmANIAccountYN_DM.grxml", "ES1105_ConfirmANIAccountYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["sure yeah yep maam please thanks thank you", "yes yes yes yes maam please thanks thank you", "that is correct right maam please thanks thank you", "that's correct right maam please thanks thank you"]}, {"Name": "no", "Vocab": ["no no no no maam thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ES1205_IMEITransitionSkipSBI_DM", "Grammars": ["ES1205_IMEITransitionSkipSBI_DM.grxml", "ES1205_IMEITransitionSkipSBI_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["continue please"]}], "EntityType": "ClosedList"}, {"Entity": "ES1215_FindIMEIWaitSBI_DM", "Grammars": ["ES1215_FindIMEIWaitSBI_DM.grxml", "ES1215_FindIMEIWaitSBI_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["continue please"]}, {"Name": "not_working", "Vocab": ["it isn't working", "it is it's not working"]}], "EntityType": "ClosedList"}, {"Entity": "ES1220_CollectIMEI_DM (Complex)", "Grammars": ["ES1220_CollectIMEI_DM.grxml", "ES1220_CollectIMEI_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "321473786069409", "Vocab": ["three two one four seven three seven eight six oh six nine four oh nine"]}, {"Name": "359577688379402", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "ES1305_ICCIDTransitionSkipSBI_DM", "Grammars": ["ES1305_ICCIDTransitionSkipSBI_DM.grxml", "ES1305_ICCIDTransitionSkipSBI_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["continue please"]}], "EntityType": "ClosedList"}, {"Entity": "ES1315_FindICCIDWaitSBI_DM", "Grammars": ["ES1315_FindICCIDWaitSBI_DM.grxml", "ES1315_FindICCIDWaitSBI_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["continue please"]}, {"Name": "cant_find", "Vocab": ["i don't have it one", "i can't find don't have it"]}], "EntityType": "ClosedList"}, {"Entity": "ES1320_CollectICCID_DM (Complex)", "Grammars": ["ES1320_CollectICCID_DM.grxml", "ES1320_CollectICCID_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "7938578558068080740", "Vocab": ["the i c c i d seven nine three eight five seven eight five five eight zero six eight oh eight oh seven four oh"]}, {"Name": "7568956977504457994", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "ES1725_FindStore_DM (Complex)", "Grammars": ["ES1725_FindStore_DM.grxml", "ES1725_FindStore_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "inquire-store_location", "Vocab": ["yes"]}, {"Name": "repeat", "Vocab": ["repeat"]}, {"Name": "operator", "Vocab": ["a rep"]}], "EntityType": "ClosedList"}, {"Entity": "ES1925_AskContinueCancelFeature_DM", "Grammars": ["ES1925_AskContinueCancelFeature_DM.grxml", "ES1925_AskContinueCancelFeature_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["i want to continue please"]}, {"Name": "cancel", "Vocab": ["i want to cancel please"]}], "EntityType": "ClosedList"}, {"Entity": "ES2005_ApproveNewPayment_DM", "Grammars": ["ES2005_ApproveNewPayment_DM.grxml", "ES2005_ApproveNewPayment_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["yeah yes", "yes sure yeah", "yes yes yes", "i want to continue with plan change please"]}, {"Name": "cancel", "Vocab": ["no no no", "i want to cancel please"]}], "EntityType": "ClosedList"}, {"Entity": "ES2130_AskContinueWithESIMYN_DM", "Grammars": ["ES2130_AskContinueWithESIMYN_DM.grxml", "ES2130_AskContinueWithESIMYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "sure", "correct", "yes thank you", "that's right", "you you've got it", "yes it is thanks", "yes that's correct", "yes it is correct right", "yes that's right thanks", "yes that is correct right"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no it isn't", "that's not correct", "that is not correct", "no it is it's not", "that is incorrect wrong", "no that's incorrect wrong", "no that's not correct right"]}], "EntityType": "ClosedList"}, {"Entity": "ES2135_AskContinueWithESIM_DM", "Grammars": ["ES2135_AskContinueWithESIM_DM.grxml", "ES2135_AskContinueWithESIM_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["with new eSIM", "continue with new eSIM"]}, {"Name": "cancel", "Vocab": ["cancel"]}, {"Name": "more_info", "Vocab": ["tell me more please", "more info information please"]}], "EntityType": "ClosedList"}, {"Entity": "ES2140_AskContinueWithPSIMYN_DM", "Grammars": ["ES2140_AskContinueWithPSIMYN_DM.grxml", "ES2140_AskContinueWithPSIMYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "sure", "correct", "yes thank you", "that's right", "you you've got it", "yes that's correct", "yes it is thanks", "yes it is correct right", "yes that's right thanks", "yes that is correct right"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no it isn't", "that is not correct", "no that's not right", "no it is it's not", "no that's not correct", "that is incorrect wrong", "no that's incorrect wrong"]}], "EntityType": "ClosedList"}, {"Entity": "ES2145_ChooseESIMOrNewPSIM_DM", "Grammars": ["ES2145_ChooseESIMOrNewPSIM_DM.grxml", "ES2145_ChooseESIMOrNewPSIM_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "ESIM", "Vocab": ["new eSIM"]}, {"Name": "PSIM", "Vocab": ["new physical SIM"]}, {"Name": "more_info", "Vocab": ["tell me more please", "more info information please"]}], "EntityType": "ClosedList"}, {"Entity": "ES2150_ChooseESIMOrNewPSIMOrOldPSIM_DM", "Grammars": ["ES2150_ChooseESIMOrNewPSIMOrOldPSIM_DM.grxml", "ES2150_ChooseESIMOrNewPSIMOrOldPSIM_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "ESIM", "Vocab": ["new eSIM"]}, {"Name": "new_PSIM", "Vocab": ["new physical SIM"]}, {"Name": "current_SIM", "Vocab": ["old SIM", "current physical SIM", "my current existing old one"]}, {"Name": "current_PSIM", "Vocab": []}, {"Name": "more_info", "Vocab": ["tell me more please", "more info information please"]}], "EntityType": "ClosedList"}, {"Entity": "ES2205_AskWiFiConnected_DM", "Grammars": ["ES2205_AskWiFiConnected_DM.grxml", "ES2205_AskWiFiConnected_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yeah", "correct", "yes thank you", "that's right", "you you've got it", "yes it is thanks", "yes that's correct", "yes that's right thanks", "yes it is correct right", "yes that is correct right"]}, {"Name": "no", "Vocab": ["nope", "wrong", "no it isn't", "that is not correct", "no it is it's not", "no that's not right", "no that's not correct", "that is incorrect wrong", "no that's incorrect wrong"]}], "EntityType": "ClosedList"}, {"Entity": "FQ1005_NarrowDownFAQTopic_DM", "Grammars": ["FQ1005_NarrowDownFAQTopic_DM.grxml", "FQ1005_NarrowDownFAQTopic_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "go_back", "Vocab": ["go back", "the main menu", "go back to the main menu"]}, {"Name": "billing_and_payments", "Vocab": ["payment", "billing", "payments", "billing and or payment payments"]}, {"Name": "voicemail", "Vocab": ["voicemail help", "help with my voicemail"]}, {"Name": "coverage", "Vocab": ["nationwide coverage"]}, {"Name": "international_calling", "Vocab": ["international", "information on international calling"]}, {"Name": "my_device", "Vocab": ["my phone tablet", "my phone or tablet"]}, {"Name": "none_of_those", "Vocab": ["none", "none of these those", "it is it's also none of those"]}, {"Name": "inquire-store_location", "Vocab": ["store locator", "where is a location store", "find locate where is a store", "where is a metro p_c_s location store"]}], "EntityType": "ClosedList"}, {"Entity": "FQ1010_BillingPayments_DM", "Grammars": ["FQ1010_BillingPayments_DM.grxml", "FQ1010_BillingPayments_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "go_back", "Vocab": ["go back", "the main menu", "go back to the main menu", "choose a different category"]}, {"Name": "billing_info", "Vocab": ["bill", "due date", "statements", "my statement", "understanding the", "understand the due date", "billing info information", "understand understanding my bill"]}, {"Name": "ways_to_pay", "Vocab": ["how way ways to pay", "payment method methods"]}, {"Name": "cost_of_changing_plans", "Vocab": ["charge to change plan plans", "cost of changing plan plans"]}, {"Name": "cost_of_adding_features", "Vocab": ["feature", "features", "charge to add feature features", "cost of adding feature features"]}, {"Name": "none_of_those", "Vocab": ["none", "none of the above", "none of them these those"]}], "EntityType": "ClosedList"}, {"Entity": "FQ1030_RepeatOrAskAnotherQuestion_DM", "Grammars": ["FQ1030_RepeatOrAskAnotherQuestion_DM.grxml", "FQ1030_RepeatOrAskAnotherQuestion_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat that", "say that again"]}, {"Name": "help_me_out", "Vocab": ["something else", "choose another topic", "help me with something else"]}, {"Name": "main_menu", "Vocab": ["main menu", "go back return to the main menu"]}], "EntityType": "ClosedList"}, {"Entity": "GS1010_GettingStarted_DM", "Grammars": ["GS1010_GettingStarted_DM.grxml", "GS1010_GettingStarted_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "main_menu", "Vocab": ["go back", "main menu"]}, {"Name": "troubleshoot", "Vocab": ["troubleshoot", "troubleshooting", "need tech support"]}, {"Name": "about_metro", "Vocab": ["more info information", "tell me more about metro"]}, {"Name": "buy_phone", "Vocab": ["buy purchase a new phone", "i need to wanna want to would like to buy purchase a new phone"]}, {"Name": "open_acct", "Vocab": ["open an account", "open an metro p_c_s account", "my metro p_c_s activation", "activate my metro p_c_s device phone"]}, {"Name": "find_store", "Vocab": ["store locator", "find locate a store", "i need to wanna want to would like to find locate a store"]}], "EntityType": "ClosedList"}, {"Entity": "GS1025_SalesInfo_DM (Complex)", "Grammars": ["GS1025_SalesInfo_DM.grxml", "GS1025_SalesInfo_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat"]}, {"Name": "more_details", "Vocab": ["more info"]}, {"Name": "open_acct", "Vocab": ["activate phone"]}, {"Name": "find_store", "Vocab": ["find store"]}, {"Name": "operator", "Vocab": ["operator"]}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommands (Complex)", "Grammars": ["GlobalCommands.grxml", "GlobalCommands_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat"]}, {"Name": "operator", "Vocab": ["an agent"]}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommandsBR1025", "Grammars": ["GlobalCommandsBR1025.grxml", "GlobalCommandsBR1025_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat that", "say that again"]}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommandsNoOperator", "Grammars": ["GlobalCommandsNoOperator.grxml", "GlobalCommandsNoOperator_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat that", "say that again"]}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommandsNoRepeat (Complex)", "Grammars": ["GlobalCommandsNoRepeat.grxml", "GlobalCommandsNoRepeat_dtmf.grxml"], "SWI_meaning": [{"Name": "operator", "Vocab": ["an agent"]}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommandsNoRepeat_PL1030 (Complex)", "Grammars": ["GlobalCommandsNoRepeat_PL1030.grxml"], "SWI_meaning": [{"Name": "operator", "Vocab": ["an agent"]}, {"Name": "MainMenu", "Vocab": ["menu"]}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommands_OperatorOnly (Complex)", "Grammars": ["GlobalCommands_OperatorOnly.grxml", "GlobalCommands_OperatorOnly_dtmf.grxml"], "SWI_meaning": [{"Name": "operator", "Vocab": ["a rep"]}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommands_UP1020", "Grammars": ["GlobalCommands_UP1020.grxml", "GlobalCommands_UP1020_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat that", "say that again"]}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommands_mw1310 (Complex)", "Grammars": ["GlobalCommands_mw1310.grxml", "GlobalCommands_mw1310_dtmf.grxml"], "SWI_meaning": [{"Name": "help", "Vocab": ["help"]}, {"Name": "operator", "Vocab": ["agent"]}], "EntityType": "ClosedList"}, {"Entity": "IH1210_Disconnected_DM", "Grammars": ["IH1210_Disconnected_DM.grxml", "IH1210_Disconnected_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "activation", "Vocab": ["yes", "activate", "activate my phone"]}], "EntityType": "ClosedList"}, {"Entity": "IH1415_PlayAndAskIOM_DM", "Grammars": ["IH1415_PlayAndAskIOM_DM.grxml", "IH1415_PlayAndAskIOM_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yea", "yes"]}, {"Name": "no", "Vocab": ["no"]}], "EntityType": "ClosedList"}, {"Entity": "IH1505_EmployeeInfoMenu_DM", "Grammars": ["IH1505_EmployeeInfoMenu_DM.grxml", "IH1505_EmployeeInfoMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yea", "yup", "yes I would please"]}, {"Name": "no", "Vocab": ["no thank you"]}], "EntityType": "ClosedList"}, {"Entity": "IU1005_GetMDN_DM (Complex)", "Grammars": ["IU1005_GetMDN_DM.grxml", "IU1005_GetMDN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "7003009339", "Vocab": ["seven hundred three hundred nine three three nine"]}, {"Name": "5073955619", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "IU1006_AskIfPhoneAvailable_DM", "Grammars": ["IU1006_AskIfPhoneAvailable_DM.grxml", "IU1006_AskIfPhoneAvailable_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "IU1048_AskExistingCustomer_DM", "Grammars": ["IU1048_AskExistingCustomer_DM.grxml", "IU1048_AskExistingCustomer_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "IU1050_NewCustomerMenu_DM", "Grammars": ["IU1050_NewCustomerMenu_DM.grxml", "IU1050_NewCustomerMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "activate-phone_nc", "Vocab": ["i'd like to activate a my the device phone", "i need to want to would like to activate a my the device phone"]}, {"Name": "purchase-phone_nc", "Vocab": ["new customer device equipment phone", "i'd like to purchase a my the new device equipment phone", "i need to want to would like to purchase a my the new device equipment phone"]}, {"Name": "something-else_nc", "Vocab": ["its none of those something else please", "i'd like none of those something else please", "i would like none of those something else please", "i need want none of those something else please", "i'm calling about none of those something else please"]}], "EntityType": "ClosedList"}, {"Entity": "IU1051_AskMetroDevice_DM", "Grammars": ["IU1051_AskMetroDevice_DM.grxml", "IU1051_AskMetroDevice_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "IU1115_AskReopenAccountYN_DM (Complex)", "Grammars": ["IU1115_AskReopenAccountYN_DM.grxml", "IU1115_AskReopenAccountYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes"]}, {"Name": "no", "Vocab": ["no"]}], "EntityType": "Boolean"}, {"Entity": "IU1210_RepeatFindMDNInfoYN_DM", "Grammars": ["IU1210_RepeatFindMDNInfoYN_DM.grxml", "IU1210_RepeatFindMDNInfoYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yea", "yes I would", "repeat that", "yes repeat please"]}, {"Name": "no", "Vocab": ["no thank you"]}], "EntityType": "ClosedList"}, {"Entity": "IU1305_MoreOptions_DM (Complex)", "Grammars": ["IU1305_MoreOptions_DM.grxml", "IU1305_MoreOptions_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "**********", "Vocab": ["area code six five two six one nine one seven zero two"]}, {"Name": "**********", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "IU1315_ReactivateOptions_DM", "Grammars": ["IU1315_ReactivateOptions_DM.grxml", "IU1315_ReactivateOptions_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "unsuspend", "Vocab": ["lift a suspension", "i want to would like to lift a suspension"]}, {"Name": "reactivateold", "Vocab": ["get my number back", "i want to would like to get my number back"]}, {"Name": "activatenew", "Vocab": ["open a new account", "i want to would like to open a new account"]}], "EntityType": "ClosedList"}, {"Entity": "LG1010_GetAccountNumber_DM (Complex)", "Grammars": ["LG1010_GetAccountNumber_DM.grxml", "LG1010_GetAccountNumber_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "**********", "Vocab": ["four zero five four hundred seven eight five oh"]}, {"Name": "**********", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "LG1025_LoginFailure_DM", "Grammars": ["LG1025_LoginFailure_DM.grxml", "LG1025_LoginFailure_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "activate-new_phone", "Vocab": ["phone", "activate a my the phone"]}, {"Name": "inquire-store_location", "Vocab": ["find a metro p c s store"]}], "EntityType": "ClosedList"}, {"Entity": "LG1115_GetSecurityCode_DM (Complex)", "Grammars": ["LG1115_GetSecurityCode_DM.grxml", "LG1115_GetSecurityCode_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "6193462", "Vocab": ["my code is six one nine three four six two"]}, {"Name": "596676", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "LG1120_SecurityCodeWait_DM", "Grammars": ["LG1120_SecurityCodeWait_DM.grxml", "LG1120_SecurityCodeWait_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "0989225", "Vocab": []}, {"Name": "dont_know", "Vocab": ["i don't know it"]}, {"Name": "ready", "Vocab": ["okay", "continue", "i am ready", "i'm ready now"]}], "EntityType": "ClosedList"}, {"Entity": "LG1127_AskResetInformation_DM", "Grammars": ["LG1127_AskResetInformation_DM.grxml", "LG1127_AskResetInformation_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "LG1610_ConfirmMDNYN_DM", "Grammars": ["LG1610_ConfirmMDNYN_DM.grxml", "LG1610_ConfirmMDNYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yes", "yeah"]}, {"Name": "no", "Vocab": ["no", "nope"]}, {"Name": "repeat", "Vocab": ["repeat that please", "please repeat that"]}], "EntityType": "ClosedList"}, {"Entity": "LP1005_LostPhoneHandling_DM", "Grammars": ["LP1005_LostPhoneHandling_DM.grxml", "LP1005_LostPhoneHandling_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "suspend", "Vocab": ["suspend line my phone"]}, {"Name": "insurance", "Vocab": ["metroguard", "phone insurance"]}], "EntityType": "ClosedList"}, {"Entity": "LP1020_InsuranceInfoWait_DM", "Grammars": ["LP1020_InsuranceInfoWait_DM.grxml", "LP1020_InsuranceInfoWait_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["continue"]}], "EntityType": "ClosedList"}, {"Entity": "LP1025_PhoneInsuranceInfo_DM", "Grammars": ["LP1025_PhoneInsuranceInfo_DM.grxml", "LP1025_PhoneInsuranceInfo_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yes please", "yes i would", "yeah i would"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "LP1030_SuspendLineYN_DM", "Grammars": ["LP1030_SuspendLineYN_DM.grxml", "LP1030_SuspendLineYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "sure", "yes please", "yes i would", "yeah i would"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "MM1030_MainMenuHVM_DM", "Grammars": ["MM1030_MainMenuHVM_DM.grxml", "MM1030_MainMenuHVM_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "acct_balance", "Vocab": ["travel talk", "account balance", "payment date dates", "balance info information", "get a my the your balance", "what's a my the your balance", "what are a my the your payment dates", "what's a my the your payment date dates"]}, {"Name": "set_up_phone", "Vocab": ["change a my the your phone number", "activate set up switch a my the your phone"]}, {"Name": "add_feature", "Vocab": ["add a feature"]}, {"Name": "auto_pay", "Vocab": ["set up autopay"]}, {"Name": "change_plan", "Vocab": ["change a my the your plan"]}, {"Name": "plan_details", "Vocab": ["tell me about a my the your plan"]}, {"Name": "make_pmt", "Vocab": ["make payments", "add money to metro connect", "make a my the your payment", "make payments on a my the your account", "make a my the your payment on a my the your account"]}, {"Name": "acct_bal", "Vocab": []}, {"Name": "add_line", "Vocab": []}, {"Name": "device_handling", "Vocab": []}, {"Name": "help_me_out", "Vocab": ["help me out", "information on a feature", "information on features roaming", "detailed feature features information", "billing roaming services information"]}, {"Name": "more_options", "Vocab": ["something else", "further more options", "more choices options", "none of them these the above"]}], "EntityType": "ClosedList"}, {"Entity": "MM1031_MainMenu_DM", "Grammars": ["MM1031_MainMenu_DM.grxml", "MM1031_MainMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "tmomoney", "Vocab": ["t_mobile money"]}, {"Name": "make-payment", "Vocab": ["pay now", "make a payment"]}, {"Name": "home-internet", "Vocab": ["home internet"]}, {"Name": "inquire-acp", "Vocab": ["a_c_p"]}, {"Name": "inquire-balance_mm", "Vocab": ["balance and payments"]}, {"Name": "plan_addon", "Vocab": ["add-ons", "plans and services", "add feauture service"]}, {"Name": "activate-phone_mm", "Vocab": ["activate phone"]}, {"Name": "request-extension", "Vocab": ["payment extension"]}, {"Name": "vague-data_mm", "Vocab": ["data options"]}, {"Name": "more_options", "Vocab": ["more options", "something else"]}], "EntityType": "ClosedList"}, {"Entity": "MM1032_BalanceandPaymentsMenu_DM", "Grammars": ["MM1032_BalanceandPaymentsMenu_DM.grxml", "MM1032_BalanceandPaymentsMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "request-extension", "Vocab": ["extension"]}, {"Name": "vague-autopay", "Vocab": ["auto pay"]}, {"Name": "make-payment", "Vocab": ["make a payment"]}, {"Name": "payment-help", "Vocab": ["payment help"]}, {"Name": "hear-plan_details", "Vocab": ["balance details"]}, {"Name": "hear_plan_details", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "MM1033_ActivatePhoneMenu_DM", "Grammars": ["MM1033_ActivatePhoneMenu_DM.grxml", "MM1033_ActivatePhoneMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "activate-new_account_mm", "Vocab": ["activate a device", "open a new account"]}, {"Name": "switch-phone", "Vocab": ["switch your device", "change switch phone", "i have a new device"]}, {"Name": "reactivate-mm", "Vocab": ["reactivate a device"]}, {"Name": "add-line", "Vocab": ["add a line"]}], "EntityType": "ClosedList"}, {"Entity": "MM1034_DataMenu_DM", "Grammars": ["MM1034_DataMenu_DM.grxml", "MM1034_DataMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "hear-data_usage", "Vocab": ["data usage"]}, {"Name": "add-data", "Vocab": ["add adding data"]}, {"Name": "vague-troubleshooting_disambig", "Vocab": ["troubleshoot", "troubleshooting tips"]}], "EntityType": "ClosedList"}, {"Entity": "MM1035_MyAccount_DM", "Grammars": ["MM1035_MyAccount_DM.grxml", "MM1035_MyAccount_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "make_pmt", "Vocab": ["my bill", "payment", "make a payment", "pay a my the bill", "i need to wanna want to would like to make a payment"]}, {"Name": "main_menu", "Vocab": ["go back", "main menu"]}, {"Name": "plan_details", "Vocab": ["my plan"]}, {"Name": "my_features", "Vocab": ["my features"]}, {"Name": "acct_bal", "Vocab": ["balance", "what's my balance", "when is my payment due"]}, {"Name": "reset_pin", "Vocab": ["voicemail", "voicemail password pin reset", "get my voicemail password pin", "reset my voicemail password pin"]}, {"Name": "mdn_change", "Vocab": ["change a my phone number", "i need to wanna want to would like to change a my phone number"]}, {"Name": "add_line", "Vocab": ["family plan", "add a device line phone tablet", "i need to wanna want to would like to add a device line phone tablet"]}], "EntityType": "ClosedList"}, {"Entity": "MM1037_PlansandAddonsMenu_DM", "Grammars": ["MM1037_PlansandAddonsMenu_DM.grxml", "MM1037_PlansandAddonsMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "hear-plan_details", "Vocab": ["current my plan", "current plan plans and services", "current plan plans service services"]}, {"Name": "change-plan", "Vocab": ["change plan"]}, {"Name": "add-feature", "Vocab": ["add a feature features service services"]}, {"Name": "remove-feature", "Vocab": ["remove a feature features service services"]}, {"Name": "add-line", "Vocab": ["add a line"]}, {"Name": "cancel-line", "Vocab": ["cancel remove a line"]}], "EntityType": "ClosedList"}, {"Entity": "MM1038_MoreOptionsMenu_DM", "Grammars": ["MM1038_MoreOptionsMenu_DM.grxml", "MM1038_MoreOptionsMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "voicemail", "Vocab": ["voicemail help"]}, {"Name": "home-internet", "Vocab": ["home internet"]}, {"Name": "suspend-service", "Vocab": ["suspend my service", "my phone is was lost stolen"]}, {"Name": "inquire-account_number", "Vocab": ["get my account number"]}, {"Name": "forgot-account_pin", "Vocab": ["i forgot my account pin"]}, {"Name": "vague-unlock_phone", "Vocab": ["unlock phone"]}, {"Name": "change-phone_number", "Vocab": ["change phone number"]}, {"Name": "switch-lines", "Vocab": ["switch my line lines"]}, {"Name": "go_back", "Vocab": ["go back"]}], "EntityType": "ClosedList"}, {"Entity": "MM1039_ReactivateMenu_DM", "Grammars": ["MM1039_ReactivateMenu_DM.grxml", "MM1039_ReactivateMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "unsuspend", "Vocab": ["lift a suspension"]}, {"Name": "reactivate-old_account_mm", "Vocab": ["get my number back"]}, {"Name": "activate-new_account", "Vocab": ["open a new account"]}], "EntityType": "ClosedList"}, {"Entity": "MM1040_VoicemailMenu_DM", "Grammars": ["MM1040_VoicemailMenu_DM.grxml", "MM1040_VoicemailMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes"]}, {"Name": "no", "Vocab": ["no"]}], "EntityType": "Boolean"}, {"Entity": "MM1045_HomeInternetMenu_DM", "Grammars": ["MM1045_HomeInternetMenu_DM.grxml", "MM1045_HomeInternetMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "hear-plan_details", "Vocab": ["plan details"]}, {"Name": "internet-usage_information", "Vocab": ["usage information"]}, {"Name": "something-else_homeinternet", "Vocab": ["something else"]}], "EntityType": "ClosedList"}, {"Entity": "MM1047_Disambig_Home_Internet_DM", "Grammars": ["MM1047_Disambig_Home_Internet_DM.grxml", "MM1047_Disambig_Home_Internet_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "home-internet", "Vocab": ["home internet"]}, {"Name": "mobile-phone", "Vocab": ["mobile phone"]}], "EntityType": "ClosedList"}, {"Entity": "MM1415_Outage_DM", "Grammars": ["MM1415_Outage_DM.grxml", "MM1415_Outage_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "vague-troubleshooting_disambig", "Vocab": ["troubleshoot troubleshooting tips"]}, {"Name": "open_account", "Vocab": ["open an account"]}, {"Name": "inquire-store_location", "Vocab": ["find a store"]}], "EntityType": "ClosedList"}, {"Entity": "MP2025_PaymentOptions_DM", "Grammars": ["MP2025_PaymentOptions_DM.grxml", "MP2025_PaymentOptions_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "make-payment", "Vocab": ["pay now", "make a payment now"]}, {"Name": "find_payment_center", "Vocab": ["i want to pay by cash", "i'd like to pay by cash", "find a an the authorized store", "find a an the authorized metro p_c_s store", "find a an the authorized payment center location"]}, {"Name": "vague-autopay", "Vocab": ["setup automatic payments auto pay", "i need to wanna want to would like to setup automatic payments auto pay"]}, {"Name": "change-plan", "Vocab": ["change a my the rate plan", "i need to wanna want to would like to change a my the rate plan"]}, {"Name": "switch-account_mp", "Vocab": ["switch a my the account", "i need to wanna want to would like to switch a my the account"]}], "EntityType": "ClosedList"}, {"Entity": "MP2050_PayByPhoneConfirmYN_DM", "Grammars": ["MP2050_PayByPhoneConfirmYN_DM.grxml", "MP2050_PayByPhoneConfirmYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "sure", "yeah i would", "yes i would please"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you", "no i wouldn't would not"]}], "EntityType": "ClosedList"}, {"Entity": "MS1005_OfferSecuritySMSYN_DM", "Grammars": ["MS1005_OfferSecuritySMSYN_DM.grxml", "MS1005_OfferSecuritySMSYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes text me the link", "sure yeah yep maam please thanks thank you", "yes yes yes yes maam please thanks thank you", "that is correct right maam please thanks thank you", "that's correct right maam please thanks thank you"]}, {"Name": "no", "Vocab": ["no no no no maam thank you"]}], "EntityType": "ClosedList"}, {"Entity": "MW1015_OneCardInWallet_DM", "Grammars": ["MW1015_OneCardInWallet_DM.grxml", "MW1015_OneCardInWallet_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "MW1020_ManageCardsMenu_DM", "Grammars": ["MW1020_ManageCardsMenu_DM.grxml", "MW1020_ManageCardsMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "change-primary_card", "Vocab": ["i'd like to change a my the primary payment card cards please", "i'd like to change a my the primary payment card cards on my account please", "i need to want to would like to change a my the primary payment card cards please", "i need to want to would like to change a my the primary payment card cards on my account please"]}, {"Name": "add-card", "Vocab": ["i'd like to add a my the card cards please", "i need to want to would like to add a my the card cards please"]}, {"Name": "remove-card", "Vocab": ["i'd like to remove a my the card cards please", "i need to want to would like to remove a my the card cards please"]}, {"Name": "update-exp", "Vocab": ["i'd like to update a my the expiration date please", "i need to want to would like to update a my the expiration date please"]}], "EntityType": "ClosedList"}, {"Entity": "MW1065_RemoveAutopayCard_DM", "Grammars": ["MW1065_RemoveAutopayCard_DM.grxml", "MW1065_RemoveAutopayCard_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "replace-card", "Vocab": ["i'd like to replace a my the card cards please", "i need to want to would like to replace a my the card cards please"]}, {"Name": "cancel-autopay", "Vocab": ["i'd like to cancel a my the automatic payments autopay please", "i need to want to would like to cancel a my the automatic payments autopay please"]}], "EntityType": "ClosedList"}, {"Entity": "MW1066_RemoveAutopayCardMoreCards_DM", "Grammars": ["MW1066_RemoveAutopayCardMoreCards_DM.grxml", "MW1066_RemoveAutopayCardMoreCards_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "cancel-autopay", "Vocab": ["i'd like to cancel a my the automatic payments autopay please", "i need to want to would like to cancel a my the automatic payments autopay please"]}, {"Name": "remove-different_card", "Vocab": ["i'd like to remove a my the different card cards please", "i need to want to would like to remove a my the different card cards please"]}], "EntityType": "ClosedList"}, {"Entity": "MW1070_ChooseFromCardList_DM (Complex)", "Grammars": ["MW1070_ChooseFromCardList_DM.grxml", "MW1070_ChooseFromCardList_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "3276", "Vocab": ["three two seven six"]}, {"Name": "5725", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "MW1075_ConfirmCard_DM", "Grammars": ["MW1075_ConfirmCard_DM.grxml", "MW1075_ConfirmCard_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "MW1200_AskDebitOrCredit_DM", "Grammars": ["MW1200_AskDebitOrCredit_DM.grxml", "MW1200_AskDebitOrCredit_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "debit", "Vocab": ["debit card please"]}, {"Name": "credit", "Vocab": ["credit card please"]}], "EntityType": "ClosedList"}, {"Entity": "MW1205_GetCardNumber_DM (Complex)", "Grammars": ["MW1205_GetCardNumber_DM.grxml", "MW1205_GetCardNumber_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "871210842548851", "Vocab": ["eight seven one two one zero eight four two five four eight eight five one"]}, {"Name": "8624297719218206", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "MW1215_UseOtherCardYN_DM", "Grammars": ["MW1215_UseOtherCardYN_DM.grxml", "MW1215_UseOtherCardYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "MW1225_CardAlreadyInWallet_DM", "Grammars": ["MW1225_CardAlreadyInWallet_DM.grxml", "MW1225_CardAlreadyInWallet_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "MW1227_AskIfOtherCardhandy_DM", "Grammars": ["MW1227_AskIfOtherCardhandy_DM.grxml", "MW1227_AskIfOtherCardhandy_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "MW1300_GetExpirationDate_DM (Complex)", "Grammars": ["MW1300_GetExpirationDate_DM.grxml", "MW1300_GetExpirationDate_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "0860", "Vocab": ["eight six oh"]}, {"Name": "1142", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "MW1400_GetBillingZipCode_DM (Complex)", "Grammars": ["MW1400_GetBillingZipCode_DM.grxml", "MW1400_GetBillingZipCode_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "42755", "Vocab": ["four two seven five five"]}, {"Name": "14019", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "MW1510_AskIfPrimary_DM", "Grammars": ["MW1510_AskIfPrimary_DM.grxml", "MW1510_AskIfPrimary_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ND1005_VagueBillingDisambig_DM (Complex)", "Grammars": ["ND1005_VagueBillingDisambig_DM.grxml", "ND1005_VagueBillingDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "make-payment", "Vocab": ["pay bill"]}, {"Name": "request-extension", "Vocab": ["extend paymrnt"]}, {"Name": "change-payment_date", "Vocab": ["due date change"]}, {"Name": "inquire-billing", "Vocab": ["credit"]}, {"Name": "billing-disambig_other", "Vocab": ["none of them"]}], "EntityType": "ClosedList"}, {"Entity": "ND1010_VagueForgotPinDisambig_DM (Complex)", "Grammars": ["ND1010_VagueForgotPinDisambig_DM.grxml", "ND1010_VagueForgotPinDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "temporary-pin", "Vocab": ["temporary pin"]}, {"Name": "forgot-account_pin", "Vocab": ["account pin"]}, {"Name": "request-transfer_pin", "Vocab": ["transfer pin"]}, {"Name": "request-puk_code", "Vocab": ["unlock phone"]}, {"Name": "reset-voicemail_pin", "Vocab": ["voicemail pin"]}, {"Name": "payment-pin", "Vocab": ["payment pin"]}, {"Name": "something-else_forgotpin", "Vocab": ["more options"]}], "EntityType": "ClosedList"}, {"Entity": "ND1015_VaguePlanDisambig_DM (Complex)", "Grammars": ["ND1015_VaguePlanDisambig_DM.grxml", "ND1015_VaguePlanDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "hear-plan_details", "Vocab": ["details"]}, {"Name": "change-plan", "Vocab": ["edit plan"]}, {"Name": "add-line", "Vocab": ["add line"]}, {"Name": "cancel-line", "Vocab": ["remove line"]}], "EntityType": "ClosedList"}, {"Entity": "ND1020_VagueAddServiceDisambig_DM (Complex)", "Grammars": ["ND1020_VagueAddServiceDisambig_DM.grxml", "ND1020_VagueAddServiceDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "add-line", "Vocab": ["open my line"]}, {"Name": "buy-data_topup", "Vocab": ["add data"]}, {"Name": "add-feature", "Vocab": ["add a feature"]}, {"Name": "activate-new_account", "Vocab": ["new account"]}, {"Name": "something-else_addservice", "Vocab": ["something else"]}], "EntityType": "ClosedList"}, {"Entity": "ND1024_ConfirmExistingCustomer_DM", "Grammars": ["ND1024_ConfirmExistingCustomer_DM.grxml", "ND1024_ConfirmExistingCustomer_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ND1025_OpenAccountDisambig_DM (Complex)", "Grammars": ["ND1025_OpenAccountDisambig_DM.grxml", "ND1025_OpenAccountDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "switch-phone", "Vocab": ["reset phone"]}, {"Name": "activate-new_account", "Vocab": ["new account"]}, {"Name": "add-line", "Vocab": ["add line"]}, {"Name": "something-else_openaccount", "Vocab": ["more options"]}], "EntityType": "ClosedList"}, {"Entity": "ND1105_VagueDataDisambig_DM", "Grammars": ["ND1105_VagueDataDisambig_DM.grxml", "ND1105_VagueDataDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "troubleshooting-data_vague", "Vocab": ["tech support troubleshoot troubleshooting please", "i'd like tech support troubleshoot troubleshooting please", "i would like tech support troubleshoot troubleshooting please", "i need want tech support troubleshoot troubleshooting please"]}, {"Name": "buy-data_topup", "Vocab": ["hotspot", "data top up", "add more data", "top up please", "i'd like to buy a data top up please", "i'd like to top up a my the data please", "i need to want to would like to buy a data top up please", "i need to want to would like to top up a my the data please"]}, {"Name": "hear-data_usage", "Vocab": ["hear a my the data usage please", "i'd like a my the data usage please", "i would like a my the data usage please", "i need want a my the data usage please", "i'd like to hear a my the data usage please", "i need to want to would like to hear a my the data usage please"]}], "EntityType": "ClosedList"}, {"Entity": "ND1110_VaguePaymentArrangementDisambig_DM", "Grammars": ["ND1110_VaguePaymentArrangementDisambig_DM.grxml", "ND1110_VaguePaymentArrangementDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "request-extension", "Vocab": ["a payment extension please", "i'd like a payment extension please", "i would like a payment extension please", "i need want a payment extension please"]}, {"Name": "change-payment_date", "Vocab": ["i'd like to change a my the payment date please", "i need to want to would like to change a my the payment date please"]}, {"Name": "faq-payment_methods", "Vocab": ["ways to pay please", "i'd like ways to pay please", "i would like ways to pay please", "i need want ways to pay please"]}], "EntityType": "ClosedList"}, {"Entity": "ND1120_VagueTransferNumberDisambig_DM (Complex)", "Grammars": ["ND1120_VagueTransferNumberDisambig_DM.grxml", "ND1120_VagueTransferNumberDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "switch-phone", "Vocab": ["reset phone"]}, {"Name": "cancel-service_transfer", "Vocab": ["cancel service"]}, {"Name": "switch-lines", "Vocab": ["switch line"]}, {"Name": "change-sim", "Vocab": ["change sim card"]}, {"Name": "change-phone_number", "Vocab": ["change number"]}, {"Name": "something-else_xfernum", "Vocab": ["more options"]}], "EntityType": "ClosedList"}, {"Entity": "ND1205_UpgradePhoneDisambigYN_DM", "Grammars": ["ND1205_UpgradePhoneDisambigYN_DM.grxml", "ND1205_UpgradePhoneDisambigYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "switch-phone", "Vocab": ["yep", "yeah", "yes please", "yes i do have", "yes i have it ready", "yes i have one ready"]}, {"Name": "upgrade-phone_purchase", "Vocab": ["no", "nope", "not ready yet"]}], "EntityType": "ClosedList"}, {"Entity": "ND1210_VagueAddMoneyDisambig_DM (Complex)", "Grammars": ["ND1210_VagueAddMoneyDisambig_DM.grxml", "ND1210_VagueAddMoneyDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "make-payment", "Vocab": ["bill pay"]}, {"Name": "buy-data_topup", "Vocab": ["top up"]}], "EntityType": "ClosedList"}, {"Entity": "ND1220_VagueChangeFeature_DM", "Grammars": ["ND1220_VagueChangeFeature_DM.grxml", "ND1220_VagueChangeFeature_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "add-feature", "Vocab": ["i'd like to add a my the feature please", "i need to want to would like to add a my the feature please"]}, {"Name": "remove-feature", "Vocab": ["i'd like to remove a my the feature please", "i need to want to would like to remove a my the feature please"]}], "EntityType": "ClosedList"}, {"Entity": "ND1305_VagueAgentDestination_DM", "Grammars": ["ND1305_VagueAgentDestination_DM.grxml", "ND1305_VagueAgentDestination_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "transfer-disambig_troubleshooting", "Vocab": ["tech support troubleshooting please", "i'd like tech support troubleshooting please", "i would like tech support troubleshooting please", "i need want tech support troubleshooting please"]}, {"Name": "transfer-disambig_payment", "Vocab": ["making a my the payment please", "to make a my the payment please", "i'd like make a my the payment please", "i'd like to make a my the payment please", "i would like make a my the payment please", "i need want make a my the payment please", "i need to want to would like to make a my the payment please"]}, {"Name": "transfer-disambig_billing", "Vocab": ["help with a my the payment please", "help with a my the bill billing please", "i'd like help with a my the payment please", "i have a my the billing question please", "i have a my the payment questions please", "i would like help with a my the payment please", "i'd like help with a my the bill billing please", "i need want help with a my the payment please", "i would like help with a my the bill billing please", "i need want help with a my the bill billing please"]}, {"Name": "transfer-disambig_plan", "Vocab": ["changing a my the plan please", "to change a my the plan please", "i'd like change a my the plan please", "i'd like to change a my the plan please", "i would like change a my the plan please", "i need want change a my the plan please", "i need to want to would like to change a my the plan please"]}, {"Name": "transfer-disambig_other", "Vocab": ["i'd like something else please", "i would like something else please", "i need want something else please", "none of those something else please"]}], "EntityType": "ClosedList"}, {"Entity": "ND1310_VagueActivatePhone_DM", "Grammars": ["ND1310_VagueActivatePhone_DM.grxml", "ND1310_VagueActivatePhone_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "switch-phone", "Vocab": ["change switch my the phone", "i want to would like to change switch my the phone"]}, {"Name": "add-line", "Vocab": ["add a new line", "i want to would like to add a new line"]}, {"Name": "activate-new_account", "Vocab": ["open a account", "open a new account", "i want would like a new account", "i want to would like to open a new account"]}, {"Name": "reactivate-old_account", "Vocab": ["reactivate an old account", "i want to would like to reactivate an old account"]}, {"Name": "change-sim", "Vocab": ["change my sim", "i want to would like to change my sim"]}, {"Name": "something-else_activatephone", "Vocab": ["its something else"]}], "EntityType": "ClosedList"}, {"Entity": "ND1315_VagueReactivatePhoneActive_DM", "Grammars": ["ND1315_VagueReactivatePhoneActive_DM.grxml", "ND1315_VagueReactivatePhoneActive_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "troubleshooting-phone_service", "Vocab": ["i am having i have issues problems with my service"]}, {"Name": "reactivate-old_account", "Vocab": ["i'd like to reactivate a phone please", "i'd like to reactivate an old phone please", "i'd like to reactivate my the old phone please", "i need to want to would like to reactivate a phone please", "i need to want to would like to reactivate an old phone please", "i need to want to would like to reactivate my the old phone please"]}, {"Name": "activate-new_account", "Vocab": ["i'd like to activate open a my the new account please", "i need to want to would like to activate open a my the new account please"]}, {"Name": "something-else_reactivatephone", "Vocab": ["it's something else", "i'd like something else please", "i would like something else please", "i need want something else please", "more options something else please"]}], "EntityType": "ClosedList"}, {"Entity": "ND1320_VagueReactivatePhoneSusp_DM", "Grammars": ["ND1320_VagueReactivatePhoneSusp_DM.grxml", "ND1320_VagueReactivatePhoneSusp_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "make-payment", "Vocab": ["yup", "yes yeah", "yeah yes", "yes yes yes", "yes i am sure thank you", "yes i did do guess so have will would"]}, {"Name": "payment-help", "Vocab": ["nah no", "no nope", "no no no", "no i would not", "no i am i'm it's not", "no it isn't thanks thank you", "no i didn't don't won't wouldn't"]}], "EntityType": "ClosedList"}, {"Entity": "ND1405_VagueCancelPlanMulti_DM", "Grammars": ["ND1405_VagueCancelPlanMulti_DM.grxml", "ND1405_VagueCancelPlanMulti_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "cancel-line", "Vocab": ["cancel remove a the line", "i want to would like to cancel remove a the line"]}, {"Name": "cancel-service", "Vocab": ["cancel close my the account", "i want to would like to cancel close my the account"]}, {"Name": "cancel-plan_change", "Vocab": ["cancel a my the plan change", "i want to would like to cancel a my the plan change"]}, {"Name": "something-else_transfer", "Vocab": ["it is something else", "none of them these those"]}], "EntityType": "ClosedList"}, {"Entity": "ND1410_VagueCancelPlanSingle_DM", "Grammars": ["ND1410_VagueCancelPlanSingle_DM.grxml", "ND1410_VagueCancelPlanSingle_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "cancel-service", "Vocab": ["cancel close my the account", "i want to would like to cancel close my the account"]}, {"Name": "cancel-plan_change", "Vocab": ["cancel a my the plan change", "i want to would like to cancel a my the plan change"]}, {"Name": "vague-cancel_plan_unknown", "Vocab": ["it is something else", "none of them these those"]}], "EntityType": "ClosedList"}, {"Entity": "ND1415_VagueUpdgrade_DM", "Grammars": ["ND1415_VagueUpdgrade_DM.grxml", "ND1415_VagueUpdgrade_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "upgrade-phone", "Vocab": ["upgrade my phone", "i want to would like to upgrade my phone"]}, {"Name": "change-plan", "Vocab": ["change upgrade my plan", "i want to would like to change upgrade my plan"]}], "EntityType": "ClosedList"}, {"Entity": "ND1420_VagueAutoFeature_DM", "Grammars": ["ND1420_VagueAutoFeature_DM.grxml", "ND1420_VagueAutoFeature_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ND1430_ChangePhone_DM (Complex)", "Grammars": ["ND1430_ChangePhone_DM.grxml", "ND1430_ChangePhone_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "switch-phone", "Vocab": ["reset phone"]}, {"Name": "add-line", "Vocab": ["add line"]}, {"Name": "cancel-line", "Vocab": ["remove a line"]}, {"Name": "something-else_changephone", "Vocab": ["more options"]}], "EntityType": "ClosedList"}, {"Entity": "ND1435_CancelService_DM", "Grammars": ["ND1435_CancelService_DM.grxml", "ND1435_CancelService_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ND1436_CancelWhatService_DM (Complex)", "Grammars": ["ND1436_CancelWhatService_DM.grxml", "ND1436_CancelWhatService_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "cancel-line", "Vocab": ["remove a line"]}, {"Name": "close", "Vocab": ["close account"]}, {"Name": "remove-feature", "Vocab": ["remove a feature"]}, {"Name": "1", "Vocab": []}, {"Name": "2", "Vocab": []}, {"Name": "3", "Vocab": []}, {"Name": "4", "Vocab": []}, {"Name": "something-else_cancelservice", "Vocab": ["something else"]}], "EntityType": "ClosedList"}, {"Entity": "ND1441_AddOrRemoveLines_DM (Complex)", "Grammars": ["ND1441_AddOrRemoveLines_DM.grxml", "ND1441_AddOrRemoveLines_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "add-line", "Vocab": ["add line"]}, {"Name": "cancel-line", "Vocab": ["cancel a line"]}], "EntityType": "ClosedList"}, {"Entity": "ND1450_VagueSim_DM (Complex)", "Grammars": ["ND1450_VagueSim_DM.grxml", "ND1450_VagueSim_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "switch-phone", "Vocab": ["reset phone"]}, {"Name": "change-sim", "Vocab": ["change sim"]}, {"Name": "request-puk_code", "Vocab": ["unlock phone"]}, {"Name": "something-else_vaguesim", "Vocab": ["something else"]}], "EntityType": "ClosedList"}, {"Entity": "ND1455_PrepaidPinPreCheck_DM", "Grammars": ["ND1455_PrepaidPinPreCheck_DM.grxml", "ND1455_PrepaidPinPreCheck_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ND1456_PrepaidInfo_DM", "Grammars": ["ND1456_PrepaidInfo_DM.grxml", "ND1456_PrepaidInfo_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ND1460_ChangeSim_DM", "Grammars": ["ND1460_ChangeSim_DM.grxml", "ND1460_ChangeSim_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ND1465_VagueBenefits_DM", "Grammars": ["ND1465_VagueBenefits_DM.grxml", "ND1465_VagueBenefits_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "ND1470_VagueUnlock_DM", "Grammars": ["ND1470_VagueUnlock_DM.grxml", "ND1470_VagueUnlock_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "cancel-service_transfer", "Vocab": ["i'd like to cancel a my the service please", "i'd like to transfer to another company provider please", "i'd like to transfer to a different company provider please", "i'd like to transfer my service to another company provider please", "i need to want to would like to cancel a my the service please", "i'd like to transfer my service to a different company provider please", "i need to want to would like to transfer to another company provider please", "i need to want to would like to transfer to a different company provider please", "i need to want to would like to transfer my service to another company provider please", "i need to want to would like to transfer my service to a different company provider please"]}, {"Name": "request-puk_code", "Vocab": ["unlock a my the phone please", "a my the phone unlocked please", "a my the phone unlock code please", "i'd like a my the phone unlocked please", "i'd like a my the phone unlock code please", "i would like a my the phone unlocked please", "i need want a my the phone unlocked please", "i would like a my the phone unlock code please", "i need want a my the phone unlock code please"]}, {"Name": "unlock-sim", "Vocab": ["unlock my sim card", "i want to would like to unlock my sim card"]}], "EntityType": "ClosedList"}, {"Entity": "NL1005_NLUMainMenu_DM (Complex)", "Grammars": ["NL1005_NLUMainMenu_DM.grxml", "NL1005_NLUMainMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "request-spanish", "Vocab": []}, {"Name": "add-feature", "Vocab": ["add a feature"]}, {"Name": "add-line", "Vocab": ["add my line"]}, {"Name": "vague-add_service", "Vocab": ["add service"]}, {"Name": "request-representative", "Vocab": ["agent"]}, {"Name": "make-payment", "Vocab": ["bill pay"]}, {"Name": "hear-plan_details", "Vocab": ["details"]}, {"Name": "change-plan", "Vocab": ["edit data"]}, {"Name": "forgot-account_pin", "Vocab": ["forgot account pin"]}, {"Name": "backoff-menu", "Vocab": ["hear options"]}, {"Name": "vague-forgot_pin", "Vocab": ["passcode"]}, {"Name": "vague-billing", "Vocab": ["pay"]}, {"Name": "vague-plan", "Vocab": ["plan"]}, {"Name": "inquire-billing", "Vocab": ["refund"]}, {"Name": "switch-device", "Vocab": ["reset device"]}, {"Name": "vague-service", "Vocab": ["service"]}, {"Name": "reset-voicemail_pin", "Vocab": ["voicemail reset"]}], "EntityType": "ClosedList"}, {"Entity": "NL1035_BackOffMenu_DM (Complex)", "Grammars": ["NL1035_BackOffMenu_DM.grxml", "NL1035_BackOffMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "bal-payments_BO", "Vocab": ["balance"]}, {"Name": "plans-services_BO", "Vocab": ["plans"]}, {"Name": "activations_BO", "Vocab": ["activation"]}, {"Name": "request-extension_BO", "Vocab": ["extension"]}, {"Name": "data-options_BO", "Vocab": ["data"]}, {"Name": "add-feature", "Vocab": ["add a feature"]}, {"Name": "vague-add_service", "Vocab": ["add service"]}, {"Name": "request-representative", "Vocab": ["agent"]}, {"Name": "inquire-billing", "Vocab": ["credit"]}, {"Name": "hear-plan_details", "Vocab": ["details"]}, {"Name": "change-plan", "Vocab": ["extend plan"]}, {"Name": "vague-forgot_pin", "Vocab": ["fix my pin"]}, {"Name": "forgot-account_pin", "Vocab": ["forgot account pin"]}, {"Name": "backoff-menu", "Vocab": ["more options"]}, {"Name": "add-line", "Vocab": ["open my line"]}, {"Name": "vague-billing", "Vocab": ["pay"]}, {"Name": "vague-plan", "Vocab": ["plan"]}, {"Name": "vague-service", "Vocab": ["service"]}, {"Name": "switch-device", "Vocab": ["switch phone"]}, {"Name": "reset-voicemail_pin", "Vocab": ["voicemail reset"]}], "EntityType": "ClosedList"}, {"Entity": "NL1035_SimulatedBackoffMenu_DM (Complex)", "Grammars": ["NL1035_SimulatedBackoffMenu_DM.grxml", "NL1035_SimulatedBackoffMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "make-payment", "Vocab": ["bill pay"]}, {"Name": "vague-troubleshooting", "Vocab": []}, {"Name": "vague-phone", "Vocab": ["phone"]}, {"Name": "open-account", "Vocab": ["get started"]}, {"Name": "manage-account", "Vocab": ["manage my account"]}, {"Name": "add-feature", "Vocab": ["add a feature"]}, {"Name": "vague-add_service", "Vocab": ["add service"]}, {"Name": "request-representative", "Vocab": ["agent"]}, {"Name": "inquire-billing", "Vocab": ["credit"]}, {"Name": "hear-plan_details", "Vocab": ["details"]}, {"Name": "change-plan", "Vocab": ["edit plan"]}, {"Name": "forgot-account_pin", "Vocab": ["forgot account pin"]}, {"Name": "backoff-menu", "Vocab": ["more options"]}, {"Name": "add-line", "Vocab": ["open a line"]}, {"Name": "vague-forgot_pin", "Vocab": ["passcode"]}, {"Name": "vague-billing", "Vocab": ["pay"]}, {"Name": "vague-plan", "Vocab": ["plan"]}, {"Name": "switch-device", "Vocab": ["reset phone"]}, {"Name": "vague-service", "Vocab": ["service"]}, {"Name": "vague-troubleshoot", "Vocab": ["troubleshoot"]}, {"Name": "reset-voicemail_pin", "Vocab": ["voicemail reset"]}], "EntityType": "ClosedList"}, {"Entity": "NL1105_NLUConfirmTagYN_DM", "Grammars": ["NL1105_NLUConfirmTagYN_DM.grxml", "NL1105_NLUConfirmTagYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["see", "yep", "yeah", "yes please", "yes i agree", "yes that's correct right"]}, {"Name": "no", "Vocab": ["nope", "no i disagree", "no thanks thank you", "no that's incorrect wrong"]}, {"Name": "operator", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "NL1240_PrepaidPinPreCheck_DM", "Grammars": ["NL1240_PrepaidPinPreCheck_DM.grxml", "NL1240_PrepaidPinPreCheck_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "NL1245_PrepaidInfo_DM", "Grammars": ["NL1245_PrepaidInfo_DM.grxml", "NL1245_PrepaidInfo_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "NLU_GlobalCommands (Complex)", "Grammars": ["NLU_GlobalCommands.grxml", "NLU_GlobalCommands_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat"]}, {"Name": "request-representative", "Vocab": ["agent"]}], "EntityType": "ClosedList"}, {"Entity": "NP1015_AskCustomerCarrierorDealer_DM", "Grammars": ["NP1015_AskCustomerCarrierorDealer_DM.grxml", "NP1015_AskCustomerCarrierorDealer_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "customer", "Vocab": ["customer", "I am a customer", "I'm a customer"]}, {"Name": "carrier", "Vocab": ["carrier", "I am a carrier", "I'm a carrier"]}, {"Name": "dealer", "Vocab": ["dealer", "I am a dealer", "I'm a dealer"]}], "EntityType": "ClosedList"}, {"Entity": "PH1210_OfferPlanOrFeatures_DM", "Grammars": ["PH1210_OfferPlanOrFeatures_DM.grxml", "PH1210_OfferPlanOrFeatures_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "something-else_ph", "Vocab": ["no", "neither", "it's something else", "none of them these those options", "I don't do not want any of these those options"]}, {"Name": "change-plan", "Vocab": ["change my the plan"]}, {"Name": "remove-feature", "Vocab": ["remove some add on ons", "remove some addon addons feature features service services"]}, {"Name": "cancel-line", "Vocab": ["cancel a my the line"]}], "EntityType": "ClosedList"}, {"Entity": "PH1215_AskChangePlanYN_DM", "Grammars": ["PH1215_AskChangePlanYN_DM.grxml", "PH1215_AskChangePlanYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes please"]}, {"Name": "no", "Vocab": ["no thank you"]}], "EntityType": "ClosedList"}, {"Entity": "PH1330_RepeatChargesYN_DM", "Grammars": ["PH1330_RepeatChargesYN_DM.grxml", "PH1330_RepeatChargesYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["repeat that", "hear that again", "yes please repeat"]}, {"Name": "no", "Vocab": ["no thank you"]}], "EntityType": "ClosedList"}, {"Entity": "PH1415_RepeatPmtDeclYN_DM", "Grammars": ["PH1415_RepeatPmtDeclYN_DM.grxml", "PH1415_RepeatPmtDeclYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["repeat that", "hear that again", "yes please repeat"]}, {"Name": "no", "Vocab": ["no thank you"]}], "EntityType": "ClosedList"}, {"Entity": "PL1030_AskHearAgain_DM", "Grammars": ["PL1030_AskHearAgain_DM.grxml", "PL1030_AskHearAgain_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "please repeat that"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "PR1015_AskCustomerDealerorEmployee_DM", "Grammars": ["PR1015_AskCustomerDealerorEmployee_DM.grxml", "PR1015_AskCustomerDealerorEmployee_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "customer", "Vocab": ["customer", "I am a customer", "I'm a customer"]}, {"Name": "dealer", "Vocab": ["dealer", "I am a dealer", "I'm a dealer"]}, {"Name": "employee", "Vocab": ["employee", "I am an employee", "I'm an employee"]}], "EntityType": "ClosedList"}, {"Entity": "PR1030_PasswordResetDisambig_DM (Complex)", "Grammars": ["PR1030_PasswordResetDisambig_DM.grxml", "PR1030_PasswordResetDisambig_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "temporary-pin", "Vocab": ["temporary pin"]}, {"Name": "forgot-account_pin", "Vocab": ["account pin"]}, {"Name": "number-transfer_pin", "Vocab": ["transfer pin"]}, {"Name": "request-puk_code", "Vocab": ["unlock phone"]}, {"Name": "reset-voicemail_pin", "Vocab": ["voicemail pin"]}, {"Name": "payment-pin", "Vocab": ["payment pin"]}, {"Name": "something-else_pwdreset", "Vocab": ["more options"]}], "EntityType": "ClosedList"}, {"Entity": "RP0024_AskAddRemoveLines_DM", "Grammars": ["RP0024_AskAddRemoveLines_DM.grxml", "RP0024_AskAddRemoveLines_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "change-plan_rp", "Vocab": ["change", "change rate plan"]}, {"Name": "add-line", "Vocab": ["add", "add a line"]}, {"Name": "cancel-line", "Vocab": ["cancel", "cancel a line"]}], "EntityType": "ClosedList"}, {"Entity": "RP0026_AskThisLineOnly_DM", "Grammars": ["RP0026_AskThisLineOnly_DM.grxml", "RP0026_AskThisLineOnly_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "RP0130_CurrentPlanOptions_DM", "Grammars": ["RP0130_CurrentPlanOptions_DM.grxml", "RP0130_CurrentPlanOptions_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": []}, {"Name": "tell_me_more", "Vocab": ["tell me more", "more info information"]}, {"Name": "change-plan", "Vocab": ["change plans", "change a my plan", "i need to wanna want to would like to change plans", "i need to wanna want to would like to change a my plan"]}, {"Name": "my_features", "Vocab": ["my feature features"]}], "EntityType": "ClosedList"}, {"Entity": "RP0140_InternetRatePlanWrapMenu_DM", "Grammars": ["RP0140_InternetRatePlanWrapMenu_DM.grxml", "RP0140_InternetRatePlanWrapMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": []}, {"Name": "hear-data_usage", "Vocab": ["usage information"]}, {"Name": "main_menu", "Vocab": ["main menu"]}], "EntityType": "ClosedList"}, {"Entity": "RP0305_OfferFutureDatedPlan_DM", "Grammars": ["RP0305_OfferFutureDatedPlan_DM.grxml", "RP0305_OfferFutureDatedPlan_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "immediate", "Vocab": ["today", "right now", "right straignt away"]}, {"Name": "futuredate", "Vocab": ["next billing cycle", "next bill due date month"]}], "EntityType": "ClosedList"}, {"Entity": "RP0317_AcceptImmediateOrFutureChangeYN_DM", "Grammars": ["RP0317_AcceptImmediateOrFutureChangeYN_DM.grxml", "RP0317_AcceptImmediateOrFutureChangeYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "approve_change", "Vocab": ["approve change"]}, {"Name": "cancel", "Vocab": ["no", "cancel change"]}], "EntityType": "ClosedList"}, {"Entity": "RP0317_AcceptImmediateOrFutureChange_DM", "Grammars": ["RP0317_AcceptImmediateOrFutureChange_DM.grxml", "RP0317_AcceptImmediateOrFutureChange_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "approve_change", "Vocab": ["approve change"]}, {"Name": "cancel", "Vocab": ["no", "cancel change"]}], "EntityType": "ClosedList"}, {"Entity": "RP0320_AcceptImmediateChangeYN_DM", "Grammars": ["RP0320_AcceptImmediateChangeYN_DM.grxml", "RP0320_AcceptImmediateChangeYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "go ahead", "yes that's okay", "yes go ahead please yes"]}, {"Name": "no", "Vocab": ["nope", "no no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "RP0325_AcceptFutureDateYN_DM", "Grammars": ["RP0325_AcceptFutureDateYN_DM.grxml", "RP0325_AcceptFutureDateYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "yes i want please yes", "yes schedule that change now"]}, {"Name": "no", "Vocab": ["nope", "not right now", "no i don't want no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "RP0511_AskAddOrRemoveLines_DM", "Grammars": ["RP0511_AskAddOrRemoveLines_DM.grxml", "RP0511_AskAddOrRemoveLines_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "sure", "yeah", "add remove a line"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "RP0515_OnePlanAvailable_DM", "Grammars": ["RP0515_OnePlanAvailable_DM.grxml", "RP0515_OnePlanAvailable_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": []}, {"Name": "yes", "Vocab": ["yep", "yeah", "yes please", "yes i would", "yes i agree", "yes that's correct right"]}, {"Name": "no", "Vocab": ["nope", "no i disagree", "no thanks thank you", "no i wouldn't would not", "no that's incorrect wrong"]}], "EntityType": "ClosedList"}, {"Entity": "RP0526_DescribePlanAskChange_DM", "Grammars": ["RP0526_DescribePlanAskChange_DM.grxml", "RP0526_DescribePlanAskChange_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "RP0605_PayFirstYN_DM", "Grammars": ["RP0605_PayFirstYN_DM.grxml", "RP0605_PayFirstYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "yes please", "yes i would", "yes i agree", "yes that's correct right"]}, {"Name": "no", "Vocab": ["nope", "no i disagree", "no thanks thank you", "no i wouldn't would not", "no that's incorrect wrong"]}], "EntityType": "ClosedList"}, {"Entity": "RP1035_OnePlanAvailable_DM", "Grammars": ["RP1035_OnePlanAvailable_DM.grxml", "RP1035_OnePlanAvailable_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yea yes yup please"]}, {"Name": "no", "Vocab": ["no", "nope"]}], "EntityType": "ClosedList"}, {"Entity": "RP1305_ConfirmRatePlan_DM", "Grammars": ["RP1305_ConfirmRatePlan_DM.grxml", "RP1305_ConfirmRatePlan_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yea", "yes", "yup"]}, {"Name": "no", "Vocab": ["no", "nope"]}], "EntityType": "ClosedList"}, {"Entity": "RP1330_ChangePlanSuccess_DM", "Grammars": ["RP1330_ChangePlanSuccess_DM.grxml", "RP1330_ChangePlanSuccess_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yea", "yes"]}, {"Name": "no", "Vocab": ["no", "nope"]}], "EntityType": "ClosedList"}, {"Entity": "RP1410_AskHearAgain_DM", "Grammars": ["RP1410_AskHearAgain_DM.grxml", "RP1410_AskHearAgain_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yea", "yes", "yup"]}, {"Name": "no", "Vocab": ["no", "nope"]}, {"Name": "signed_up", "Vocab": ["other features", "what am i signed up for", "other features i'm signed up for"]}], "EntityType": "ClosedList"}, {"Entity": "RP1410_CurrentPlanOptions_DM", "Grammars": ["RP1410_CurrentPlanOptions_DM.grxml", "RP1410_CurrentPlanOptions_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat that", "hear that again"]}, {"Name": "tell_me_more", "Vocab": ["tell me more", "more info information"]}, {"Name": "change_plan", "Vocab": ["change plans", "change a my plan", "i need to wanna want to would like to change plans", "i need to wanna want to would like to change a my plan"]}, {"Name": "my_features", "Vocab": ["my feature features"]}], "EntityType": "ClosedList"}, {"Entity": "RP1411_OfferOnePlanYN_DM (Complex)", "Grammars": ["RP1411_OfferOnePlanYN_DM.grxml", "RP1411_OfferOnePlanYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat"]}, {"Name": "yes", "Vocab": ["yep"]}, {"Name": "no", "Vocab": ["no"]}, {"Name": "operator", "Vocab": ["rep"]}], "EntityType": "ClosedList"}, {"Entity": "RP1415_BTMPlanDetails_DM", "Grammars": ["RP1415_BTMPlanDetails_DM.grxml", "RP1415_BTMPlanDetails_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yea"]}, {"Name": "no", "Vocab": ["no", "nope"]}], "EntityType": "ClosedList"}, {"Entity": "RP1416_DescribePlanAskChange_DM", "Grammars": ["RP1416_DescribePlanAskChange_DM.grxml", "RP1416_DescribePlanAskChange_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "RP1425_NoOtherPlans_DM (Complex)", "Grammars": ["RP1425_NoOtherPlans_DM.grxml", "RP1425_NoOtherPlans_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "request-extension", "Vocab": ["extension"]}, {"Name": "go_back", "Vocab": ["repeat"]}, {"Name": "operator", "Vocab": ["rep"]}, {"Name": "no", "Vocab": ["no"]}, {"Name": "yes", "Vocab": ["yes"]}], "EntityType": "ClosedList"}, {"Entity": "RP1510_PayNowYN_DM", "Grammars": ["RP1510_PayNowYN_DM.grxml", "RP1510_PayNowYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes"]}, {"Name": "no", "Vocab": ["no"]}], "EntityType": "Boolean"}, {"Entity": "SE2130_AskMakeChanges_DM", "Grammars": ["SE2130_AskMakeChanges_DM.grxml", "SE2130_AskMakeChanges_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}, {"Name": "repeat", "Vocab": ["repeat that"]}], "EntityType": "ClosedList"}, {"Entity": "SE2135_WhatToChangeBothBlocked_DM", "Grammars": ["SE2135_WhatToChangeBothBlocked_DM.grxml", "SE2135_WhatToChangeBothBlocked_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "port_outs", "Vocab": ["port out outs", "mobile number transfer transfers"]}, {"Name": "sim_changes", "Vocab": ["sim change changes swap swaps"]}, {"Name": "both_of_them", "Vocab": ["both of them"]}], "EntityType": "ClosedList"}, {"Entity": "SE2140_WhatToChangeBothAllowed_DM", "Grammars": ["SE2140_WhatToChangeBothAllowed_DM.grxml", "SE2140_WhatToChangeBothAllowed_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "port_outs", "Vocab": ["port out outs", "mobile number transfer transfers"]}, {"Name": "sim_changes", "Vocab": ["sim change changes swap swaps"]}, {"Name": "both_of_them", "Vocab": ["both of them"]}], "EntityType": "ClosedList"}, {"Entity": "SE2145_WhatToChangeMix1_DM", "Grammars": ["SE2145_WhatToChangeMix1_DM.grxml", "SE2145_WhatToChangeMix1_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "allow_sim_changes", "Vocab": ["allow sim change changes swap swaps"]}, {"Name": "block_port_outs", "Vocab": ["block restrict port out outs", "block restrict mobile number transfer transfers"]}], "EntityType": "ClosedList"}, {"Entity": "SE2150_WhatToChangeMix2_DM", "Grammars": ["SE2150_WhatToChangeMix2_DM.grxml", "SE2150_WhatToChangeMix2_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "block_sim_changes", "Vocab": ["block restrict sim change changes swap swaps"]}, {"Name": "allow_port_outs", "Vocab": ["allow port out outs", "allow mobile number transfer transfers"]}], "EntityType": "ClosedList"}, {"Entity": "SE2156_PlayUnblockInfo_DM", "Grammars": ["SE2156_PlayUnblockInfo_DM.grxml", "SE2156_PlayUnblockInfo_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["say that again", "repeat please that"]}, {"Name": "send_text", "Vocab": ["text me", "send me a the text"]}, {"Name": "main-menu", "Vocab": ["return to main menu"]}], "EntityType": "ClosedList"}, {"Entity": "SH1009_AskPayNow_DM", "Grammars": ["SH1009_AskPayNow_DM.grxml", "SH1009_AskPayNow_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yea", "yup", "pay my bill", "make a payment"]}, {"Name": "no", "Vocab": ["no", "nope"]}], "EntityType": "ClosedList"}, {"Entity": "SH1030_PayNowYN_DM", "Grammars": ["SH1030_PayNowYN_DM.grxml", "SH1030_PayNowYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "extension", "Vocab": ["get an extension please", "i'd like an extension please", "i would like an extension please", "i need want an extension please", "i wanna get an extension please", "i'd like to get an extension please", "i need to want to would like to get an extension please"]}, {"Name": "yes", "Vocab": ["sure yeah yep maam please thanks thank you", "yes yes yes yes maam please thanks thank you", "that is correct right maam please thanks thank you", "that's correct right maam please thanks thank you"]}, {"Name": "no", "Vocab": ["no no no no maam thank you"]}, {"Name": "go_back", "Vocab": ["i want to go back please", "i want to hear it again please"]}, {"Name": "operator", "Vocab": ["someone please", "no customer please", "someone human please", "a human being please", "a representative please", "a customer rep please", "a live real person please", "a no customer service please", "an no agent operator please", "a customer care service please", "speak talk to someone human please", "speak talk to a human being please", "i want to speak talk to someone please", "speak talk to an agent operator please", "speak talk to a live real person please", "a customer care service representative please", "i want to speak talk to someone human please", "i want to speak talk to a human being please", "talk to a customer service representative please", "i want to speak talk to a customer rep please", "i want to speak talk to a representative please", "i want to speak talk to an agent operator please", "i want to speak talk to a live real person please", "speak talk to a customer rep representative please", "i want to speak talk to a customer care service please", "i want to speak to a customer service representative please", "speak talk to a customer care service representative please", "i want to speak talk to a customer care service representative please"]}], "EntityType": "ClosedList"}, {"Entity": "SH1110_OfferRatePlanChangeYN_DM (Complex)", "Grammars": ["SH1110_OfferRatePlanChangeYN_DM.grxml", "SH1110_OfferRatePlanChangeYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "true", "Vocab": ["yep"]}, {"Name": "false", "Vocab": ["no"]}, {"Name": "operator", "Vocab": ["agent"]}], "EntityType": "ClosedList"}, {"Entity": "SH1215_PayNowYN_DM (Complex)", "Grammars": ["SH1215_PayNowYN_DM.grxml", "SH1215_PayNowYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "true", "Vocab": ["yes"]}, {"Name": "false", "Vocab": ["no"]}, {"Name": "operator", "Vocab": ["a rep"]}], "EntityType": "ClosedList"}, {"Entity": "SH1305_OfferExtensionYN_DM (Complex)", "Grammars": ["SH1305_OfferExtensionYN_DM.grxml", "SH1305_OfferExtensionYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes"]}, {"Name": "no", "Vocab": ["no"]}, {"Name": "operator", "Vocab": ["agent"]}], "EntityType": "ClosedList"}, {"Entity": "SH1312_PayNowYN_DM", "Grammars": ["SH1312_PayNowYN_DM.grxml", "SH1312_PayNowYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yep", "yeah", "yes please"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "SO1110_ApproveAutoCorrectYN_DM", "Grammars": ["SO1110_ApproveAutoCorrectYN_DM.grxml", "SO1110_ApproveAutoCorrectYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["sure", "yes yes maam please thanks thank you", "yeah yep maam please thanks thank you", "that's correct right maam please thanks thank you"]}, {"Name": "no", "Vocab": ["no no maam thanks thank you", "nope wrong maam thanks thank you"]}, {"Name": "operator", "Vocab": ["someone human please", "a human being please", "an agent operator please", "a live real person please", "speak talk to someone please", "i want to speak to someone please", "speak talk to someone human please", "speak talk to a human being please", "a customer rep representative please", "i want to talk to someone human please", "i want to speak to someone human please", "speak talk to an agent operator please", "speak talk to a live real person please", "i want to speak talk to a human being please", "i want to speak talk to an agent operator please", "i want to speak talk to a live real person please", "speak talk to a customer rep representative please", "i want to speak talk to a customer rep representative please"]}], "EntityType": "ClosedList"}, {"Entity": "SO1405_ApproveNewPayment_DM", "Grammars": ["SO1405_ApproveNewPayment_DM.grxml", "SO1405_ApproveNewPayment_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["yes", "continue"]}, {"Name": "cancel", "Vocab": ["no", "cancel"]}], "EntityType": "ClosedList"}, {"Entity": "SO1410_ApproveNewPaymentFeature_DM", "Grammars": ["SO1410_ApproveNewPaymentFeature_DM.grxml", "SO1410_ApproveNewPaymentFeature_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": ["yes", "continue"]}, {"Name": "cancel", "Vocab": ["cancel no"]}], "EntityType": "ClosedList"}, {"Entity": "SO1410_ApproveUpdates_DM", "Grammars": ["SO1410_ApproveUpdates_DM.grxml", "SO1410_ApproveUpdates_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "approve", "Vocab": ["approve this change"]}, {"Name": "repeat", "Vocab": ["repeat"]}, {"Name": "cancel", "Vocab": ["cancel this"]}], "EntityType": "ClosedList"}, {"Entity": "SW1105_GetNewLineMDN_DM (Complex)", "Grammars": ["SW1105_GetNewLineMDN_DM.grxml", "SW1105_GetNewLineMDN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "9007007015", "Vocab": ["nine hundred seven hundred seven zero one five"]}, {"Name": "9863853214", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "SW1205_GetNewLinePIN_DM (Complex)", "Grammars": ["SW1205_GetNewLinePIN_DM.grxml", "SW1205_GetNewLinePIN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "510044", "Vocab": ["security code five one zero zero four four"]}, {"Name": "890880", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "SW1211_AskResetInformation_DM", "Grammars": ["SW1211_AskResetInformation_DM.grxml", "SW1211_AskResetInformation_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yes", "yeah", "sure"]}, {"Name": "no", "Vocab": ["nope", "no thanks thank you"]}], "EntityType": "ClosedList"}, {"Entity": "TS1005_AskSupportType_DM", "Grammars": ["TS1005_AskSupportType_DM.grxml", "TS1005_AskSupportType_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "mobile", "Vocab": ["internet", "mobile data", "data connection"]}, {"Name": "hotspot", "Vocab": ["connecting to a hotspot"]}, {"Name": "something-else_troubleshooting", "Vocab": ["neither", "none of those", "something else"]}], "EntityType": "ClosedList"}, {"Entity": "TS1215_MobileTipsWrapMenu_DM (Complex)", "Grammars": ["TS1215_MobileTipsWrapMenu_DM.grxml", "TS1215_MobileTipsWrapMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat"]}, {"Name": "tried", "Vocab": ["agent"]}], "EntityType": "ClosedList"}, {"Entity": "TS1315_HotspotTipsWrapMenu_DM (Complex)", "Grammars": ["TS1315_HotspotTipsWrapMenu_DM.grxml", "TS1315_HotspotTipsWrapMenu_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat"]}, {"Name": "tried", "Vocab": ["agent"]}], "EntityType": "ClosedList"}, {"Entity": "TS1330_OfferTopupYN_DM", "Grammars": ["TS1330_OfferTopupYN_DM.grxml", "TS1330_OfferTopupYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": ["repeat that", "say that again"]}, {"Name": "yes", "Vocab": ["yes add it", "yes please", "add yes top up"]}, {"Name": "no", "Vocab": ["no thank you"]}, {"Name": "tips", "Vocab": ["more tips", "something else"]}], "EntityType": "ClosedList"}, {"Entity": "UA1005_ExistingAccountYN_DM", "Grammars": ["UA1005_ExistingAccountYN_DM.grxml", "UA1005_ExistingAccountYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["i do", "sure", "yes yes maam please thanks thank you", "yeah yep maam please thanks thank you", "that's correct right maam please thanks thank you"]}, {"Name": "no", "Vocab": ["no no maam thanks thank you", "nope wrong maam thanks thank you"]}, {"Name": "spanish", "Vocab": ["spanish"]}, {"Name": "operator", "Vocab": ["someone please", "someone human please", "a human being please", "an agent operator please", "a live real person please", "speak talk to someone human please", "speak talk to a human being please", "a customer rep representative please", "i want to speak talk to someone please", "speak talk to an agent operator please", "speak talk to a live real person please", "i want to speak talk to someone human please", "i want to speak talk to a human being please", "i want to speak talk to an agent operator please", "i want to speak talk to a live real person please", "speak talk to a customer rep representative please", "i want to speak talk to a customer rep representative please"]}], "EntityType": "ClosedList"}, {"Entity": "UA1105_CollectMDN_DM (Complex)", "Grammars": ["UA1105_CollectMDN_DM.grxml", "UA1105_CollectMDN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "9955203154", "Vocab": ["nine nine five five twenty three one five four"]}, {"Name": "8638932748", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "UA1125_ExistingCustomerInfoYN_DM (Complex)", "Grammars": ["UA1125_ExistingCustomerInfoYN_DM.grxml", "UA1125_ExistingCustomerInfoYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes"]}, {"Name": "no", "Vocab": ["no"]}, {"Name": "operator", "Vocab": ["a rep"]}], "EntityType": "ClosedList"}, {"Entity": "UA1205_NewCustomerInfoYN_DM", "Grammars": ["UA1205_NewCustomerInfoYN_DM.grxml", "UA1205_NewCustomerInfoYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["sure", "yes yes maam please thanks thank you", "yeah yep maam please thanks thank you", "that's correct right maam please thanks thank you"]}, {"Name": "no", "Vocab": ["no no maam thanks thank you", "nope wrong maam thanks thank you"]}, {"Name": "operator", "Vocab": ["someone please", "someone human please", "a human being please", "an agent operator please", "a live real person please", "speak talk to someone human please", "speak talk to a human being please", "a customer rep representative please", "i want to speak talk to someone please", "speak talk to an agent operator please", "speak talk to a live real person please", "i want to speak talk to someone human please", "i want to speak talk to a human being please", "i want to speak talk to an agent operator please", "i want to speak talk to a live real person please", "speak talk to a customer rep representative please", "i want to speak talk to a customer rep representative please"]}], "EntityType": "ClosedList"}, {"Entity": "UP1020_OfferSalesAgentYN_DM (Complex)", "Grammars": ["UP1020_OfferSalesAgentYN_DM.grxml", "UP1020_OfferSalesAgentYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup"]}, {"Name": "no", "Vocab": ["no"]}], "EntityType": "ClosedList"}, {"Entity": "VS1025_SignUp_DM", "Grammars": ["VS1025_SignUp_DM.grxml", "VS1025_SignUp_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yup", "yeah"]}, {"Name": "no", "Vocab": ["nope", "no thank you"]}, {"Name": "repeat", "Vocab": ["repeat that", "hear say that again"]}], "EntityType": "ClosedList"}, {"Entity": "VS1030_TravelTalkSignup_DM", "Grammars": ["VS1030_TravelTalkSignup_DM.grxml", "VS1030_TravelTalkSignup_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yea yup now", "yes sign up now", "yea yup sign up now"]}, {"Name": "no", "Vocab": ["no", "nope"]}], "EntityType": "ClosedList"}, {"Entity": "VS1145_AddFeaturePostamble_DM", "Grammars": ["VS1145_AddFeaturePostamble_DM.grxml", "VS1145_AddFeaturePostamble_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yes", "yea", "yup"]}, {"Name": "no", "Vocab": ["no", "nope"]}, {"Name": "make_payment", "Vocab": ["pay", "make a payment", "pay for it now"]}, {"Name": "main_menu", "Vocab": ["main menu"]}], "EntityType": "ClosedList"}, {"Entity": "VS1405_OfferFutureDatedFeature_DM", "Grammars": ["VS1405_OfferFutureDatedFeature_DM.grxml", "VS1405_OfferFutureDatedFeature_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "immediate", "Vocab": ["today", "right now", "right straignt away"]}, {"Name": "futuredate", "Vocab": ["next billing cycle", "next bill due date month"]}], "EntityType": "ClosedList"}, {"Entity": "WR1000_CancelSetupAutopayWrap_DM", "Grammars": ["WR1000_CancelSetupAutopayWrap_DM.grxml", "WR1000_CancelSetupAutopayWrap_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "main_menu", "Vocab": ["main menu"]}], "EntityType": "ClosedList"}, {"Entity": "XC1105_CollectSecurityCode_DM (Complex)", "Grammars": ["XC1105_CollectSecurityCode_DM.grxml", "XC1105_CollectSecurityCode_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "003898", "Vocab": ["code zero zero three eight nine eight"]}, {"Name": "3747099", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "XR1010_ConfirmOperatorRequest_DM", "Grammars": ["XR1010_ConfirmOperatorRequest_DM.grxml", "XR1010_ConfirmOperatorRequest_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": ["yup", "yea", "yes"]}, {"Name": "no", "Vocab": ["no", "nope"]}], "EntityType": "ClosedList"}, {"Entity": "confirmation", "Grammars": ["confirmation.grxml", "confirmation_dtmf.grxml"], "SWI_meaning": [{"Name": "true", "Vocab": ["yep", "okay", "sure", "yeah", "yes i you did yes", "yes i did would yes", "yes it is maam please yes", "yeah yep that is correct right yes", "yeah yep that's correct right yes", "yes yes that is correct right yes", "yes yes that's correct right yes"]}, {"Name": "false", "Vocab": ["nope", "wrong", "no no"]}], "EntityType": "ClosedList"}, {"Entity": "phone (Complex)", "Grammars": ["phone.grxml", "phone_dtmf.grxml"], "SWI_meaning": [{"Name": "7595004045", "Vocab": ["area code seven five nine five hundred four oh four five"]}, {"Name": "8819135494", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "AX1706_DSGExtensionTerms_DM_dtmf", "Grammars": ["AX1706_DSGExtensionTerms_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": []}, {"Name": "repeat", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "DS3005_AskLanguage_DM_dtmf", "Grammars": ["DS3005_AskLanguage_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "Spanish", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "DS3010_DealerOrCustomer_DM_dtmf (Complex)", "Grammars": ["DS3010_DealerOrCustomer_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "star", "Vocab": []}, {"Name": "00195724", "Vocab": []}, {"Name": "00345942", "Vocab": []}, {"Name": "00391355", "Vocab": []}, {"Name": "01014737", "Vocab": []}, {"Name": "01165567", "Vocab": []}, {"Name": "01195106", "Vocab": []}, {"Name": "01224256", "Vocab": []}, {"Name": "01326204", "Vocab": []}, {"Name": "01342018", "Vocab": []}, {"Name": "01466772", "Vocab": []}, {"Name": "01507203", "Vocab": []}, {"Name": "01558418", "Vocab": []}, {"Name": "01628265", "Vocab": []}, {"Name": "01762445", "Vocab": []}, {"Name": "01836897", "Vocab": []}, {"Name": "01860170", "Vocab": []}, {"Name": "02087785", "Vocab": []}, {"Name": "02118681", "Vocab": []}, {"Name": "03028657", "Vocab": []}, {"Name": "03197808", "Vocab": []}, {"Name": "03681830", "Vocab": []}, {"Name": "03701300", "Vocab": []}, {"Name": "03949332", "Vocab": []}, {"Name": "04471541", "Vocab": []}, {"Name": "04546993", "Vocab": []}, {"Name": "04557704", "Vocab": []}, {"Name": "04626282", "Vocab": []}, {"Name": "05032790", "Vocab": []}, {"Name": "05391988", "Vocab": []}, {"Name": "05417271", "Vocab": []}, {"Name": "05444554", "Vocab": []}, {"Name": "05506047", "Vocab": []}, {"Name": "05575901", "Vocab": []}, {"Name": "05750523", "Vocab": []}, {"Name": "05940832", "Vocab": []}, {"Name": "06330798", "Vocab": []}, {"Name": "06531688", "Vocab": []}, {"Name": "06920608", "Vocab": []}, {"Name": "07060802", "Vocab": []}, {"Name": "07250779", "Vocab": []}, {"Name": "07823214", "Vocab": []}, {"Name": "07939056", "Vocab": []}, {"Name": "07967377", "Vocab": []}, {"Name": "10076430", "Vocab": []}, {"Name": "10105714", "Vocab": []}, {"Name": "10118223", "Vocab": []}, {"Name": "10595638", "Vocab": []}, {"Name": "10674874", "Vocab": []}, {"Name": "10768905", "Vocab": []}, {"Name": "10773661", "Vocab": []}, {"Name": "11207652", "Vocab": []}, {"Name": "11710576", "Vocab": []}, {"Name": "11753409", "Vocab": []}, {"Name": "12124633", "Vocab": []}, {"Name": "12138245", "Vocab": []}, {"Name": "12346534", "Vocab": []}, {"Name": "12717833", "Vocab": []}, {"Name": "12783574", "Vocab": []}, {"Name": "13229662", "Vocab": []}, {"Name": "13236392", "Vocab": []}, {"Name": "13306518", "Vocab": []}, {"Name": "13412184", "Vocab": []}, {"Name": "13598775", "Vocab": []}, {"Name": "13800755", "Vocab": []}, {"Name": "13850249", "Vocab": []}, {"Name": "13935004", "Vocab": []}, {"Name": "14229376", "Vocab": []}, {"Name": "14430135", "Vocab": []}, {"Name": "14561794", "Vocab": []}, {"Name": "14706319", "Vocab": []}, {"Name": "14923160", "Vocab": []}, {"Name": "15150902", "Vocab": []}, {"Name": "15739250", "Vocab": []}, {"Name": "15824118", "Vocab": []}, {"Name": "15891947", "Vocab": []}, {"Name": "16681851", "Vocab": []}, {"Name": "16721049", "Vocab": []}, {"Name": "16761219", "Vocab": []}, {"Name": "16897162", "Vocab": []}, {"Name": "16976429", "Vocab": []}, {"Name": "17157111", "Vocab": []}, {"Name": "17456684", "Vocab": []}, {"Name": "17651691", "Vocab": []}, {"Name": "17819278", "Vocab": []}, {"Name": "18027957", "Vocab": []}, {"Name": "18424615", "Vocab": []}, {"Name": "18444247", "Vocab": []}, {"Name": "18511326", "Vocab": []}, {"Name": "18954164", "Vocab": []}, {"Name": "19105967", "Vocab": []}, {"Name": "19449071", "Vocab": []}, {"Name": "19638498", "Vocab": []}, {"Name": "19640539", "Vocab": []}, {"Name": "21092774", "Vocab": []}, {"Name": "21168061", "Vocab": []}, {"Name": "21199655", "Vocab": []}, {"Name": "21518310", "Vocab": []}, {"Name": "21809775", "Vocab": []}, {"Name": "22144335", "Vocab": []}, {"Name": "22203663", "Vocab": []}, {"Name": "22229017", "Vocab": []}, {"Name": "22647340", "Vocab": []}, {"Name": "22710217", "Vocab": []}, {"Name": "22737809", "Vocab": []}, {"Name": "22759825", "Vocab": []}, {"Name": "22965731", "Vocab": []}, {"Name": "23771589", "Vocab": []}, {"Name": "24212334", "Vocab": []}, {"Name": "24233759", "Vocab": []}, {"Name": "24542790", "Vocab": []}, {"Name": "24668131", "Vocab": []}, {"Name": "24861619", "Vocab": []}, {"Name": "25733999", "Vocab": []}, {"Name": "25734810", "Vocab": []}, {"Name": "26192400", "Vocab": []}, {"Name": "26273370", "Vocab": []}, {"Name": "26519607", "Vocab": []}, {"Name": "26565111", "Vocab": []}, {"Name": "26582756", "Vocab": []}, {"Name": "26775320", "Vocab": []}, {"Name": "26922603", "Vocab": []}, {"Name": "27192959", "Vocab": []}, {"Name": "27203108", "Vocab": []}, {"Name": "28482684", "Vocab": []}, {"Name": "29273708", "Vocab": []}, {"Name": "29751100", "Vocab": []}, {"Name": "29824522", "Vocab": []}, {"Name": "30121942", "Vocab": []}, {"Name": "30179830", "Vocab": []}, {"Name": "30327178", "Vocab": []}, {"Name": "30529235", "Vocab": []}, {"Name": "30683239", "Vocab": []}, {"Name": "30819220", "Vocab": []}, {"Name": "30998064", "Vocab": []}, {"Name": "31177453", "Vocab": []}, {"Name": "31912141", "Vocab": []}, {"Name": "31960428", "Vocab": []}, {"Name": "32534620", "Vocab": []}, {"Name": "32564408", "Vocab": []}, {"Name": "32640363", "Vocab": []}, {"Name": "32762407", "Vocab": []}, {"Name": "33191911", "Vocab": []}, {"Name": "33212327", "Vocab": []}, {"Name": "33470291", "Vocab": []}, {"Name": "33556974", "Vocab": []}, {"Name": "33704105", "Vocab": []}, {"Name": "34071192", "Vocab": []}, {"Name": "34140982", "Vocab": []}, {"Name": "34351785", "Vocab": []}, {"Name": "34593864", "Vocab": []}, {"Name": "34671381", "Vocab": []}, {"Name": "35311233", "Vocab": []}, {"Name": "35325486", "Vocab": []}, {"Name": "35737744", "Vocab": []}, {"Name": "35810461", "Vocab": []}, {"Name": "35823666", "Vocab": []}, {"Name": "36045842", "Vocab": []}, {"Name": "36222012", "Vocab": []}, {"Name": "36424398", "Vocab": []}, {"Name": "36552282", "Vocab": []}, {"Name": "36792638", "Vocab": []}, {"Name": "36825736", "Vocab": []}, {"Name": "36876141", "Vocab": []}, {"Name": "36889188", "Vocab": []}, {"Name": "37000990", "Vocab": []}, {"Name": "37063967", "Vocab": []}, {"Name": "37283128", "Vocab": []}, {"Name": "37425032", "Vocab": []}, {"Name": "37476547", "Vocab": []}, {"Name": "37749001", "Vocab": []}, {"Name": "37813455", "Vocab": []}, {"Name": "38000836", "Vocab": []}, {"Name": "38024321", "Vocab": []}, {"Name": "38257786", "Vocab": []}, {"Name": "38270106", "Vocab": []}, {"Name": "38905960", "Vocab": []}, {"Name": "39038573", "Vocab": []}, {"Name": "39228707", "Vocab": []}, {"Name": "39930860", "Vocab": []}, {"Name": "41023349", "Vocab": []}, {"Name": "41240695", "Vocab": []}, {"Name": "41443549", "Vocab": []}, {"Name": "41556212", "Vocab": []}, {"Name": "41586667", "Vocab": []}, {"Name": "41931526", "Vocab": []}, {"Name": "42233735", "Vocab": []}, {"Name": "42376494", "Vocab": []}, {"Name": "42456173", "Vocab": []}, {"Name": "42472154", "Vocab": []}, {"Name": "42477383", "Vocab": []}, {"Name": "42574072", "Vocab": []}, {"Name": "43023981", "Vocab": []}, {"Name": "43109004", "Vocab": []}, {"Name": "43292136", "Vocab": []}, {"Name": "43475336", "Vocab": []}, {"Name": "43538714", "Vocab": []}, {"Name": "43542306", "Vocab": []}, {"Name": "43620993", "Vocab": []}, {"Name": "43677826", "Vocab": []}, {"Name": "43739131", "Vocab": []}, {"Name": "43767752", "Vocab": []}, {"Name": "43782210", "Vocab": []}, {"Name": "43858079", "Vocab": []}, {"Name": "44087436", "Vocab": []}, {"Name": "44318107", "Vocab": []}, {"Name": "44775786", "Vocab": []}, {"Name": "44835892", "Vocab": []}, {"Name": "44904619", "Vocab": []}, {"Name": "45086628", "Vocab": []}, {"Name": "45312899", "Vocab": []}, {"Name": "45914941", "Vocab": []}, {"Name": "46153237", "Vocab": []}, {"Name": "46156073", "Vocab": []}, {"Name": "46239746", "Vocab": []}, {"Name": "46542621", "Vocab": []}, {"Name": "46781078", "Vocab": []}, {"Name": "46826015", "Vocab": []}, {"Name": "47347955", "Vocab": []}, {"Name": "47399609", "Vocab": []}, {"Name": "47526182", "Vocab": []}, {"Name": "47677468", "Vocab": []}, {"Name": "47820642", "Vocab": []}, {"Name": "48349092", "Vocab": []}, {"Name": "48730898", "Vocab": []}, {"Name": "49058590", "Vocab": []}, {"Name": "49100990", "Vocab": []}, {"Name": "49320569", "Vocab": []}, {"Name": "49321829", "Vocab": []}, {"Name": "49570194", "Vocab": []}, {"Name": "49647212", "Vocab": []}, {"Name": "49815355", "Vocab": []}, {"Name": "50022374", "Vocab": []}, {"Name": "50085359", "Vocab": []}, {"Name": "50102062", "Vocab": []}, {"Name": "50181271", "Vocab": []}, {"Name": "50531752", "Vocab": []}, {"Name": "50616892", "Vocab": []}, {"Name": "50659417", "Vocab": []}, {"Name": "50920747", "Vocab": []}, {"Name": "51226230", "Vocab": []}, {"Name": "51229194", "Vocab": []}, {"Name": "51530294", "Vocab": []}, {"Name": "51530884", "Vocab": []}, {"Name": "51576321", "Vocab": []}, {"Name": "51956709", "Vocab": []}, {"Name": "52415816", "Vocab": []}, {"Name": "52649986", "Vocab": []}, {"Name": "52998421", "Vocab": []}, {"Name": "53504964", "Vocab": []}, {"Name": "53529198", "Vocab": []}, {"Name": "53579601", "Vocab": []}, {"Name": "53746385", "Vocab": []}, {"Name": "53836720", "Vocab": []}, {"Name": "54027762", "Vocab": []}, {"Name": "54167858", "Vocab": []}, {"Name": "54168665", "Vocab": []}, {"Name": "54226907", "Vocab": []}, {"Name": "54311954", "Vocab": []}, {"Name": "54487916", "Vocab": []}, {"Name": "54532997", "Vocab": []}, {"Name": "54736271", "Vocab": []}, {"Name": "55046203", "Vocab": []}, {"Name": "55213856", "Vocab": []}, {"Name": "55238150", "Vocab": []}, {"Name": "55480325", "Vocab": []}, {"Name": "55490955", "Vocab": []}, {"Name": "55582965", "Vocab": []}, {"Name": "55633978", "Vocab": []}, {"Name": "55716197", "Vocab": []}, {"Name": "56064356", "Vocab": []}, {"Name": "56356364", "Vocab": []}, {"Name": "56740516", "Vocab": []}, {"Name": "57153632", "Vocab": []}, {"Name": "57236468", "Vocab": []}, {"Name": "57396039", "Vocab": []}, {"Name": "57471216", "Vocab": []}, {"Name": "58480522", "Vocab": []}, {"Name": "59330262", "Vocab": []}, {"Name": "59484608", "Vocab": []}, {"Name": "59489837", "Vocab": []}, {"Name": "59790258", "Vocab": []}, {"Name": "59816369", "Vocab": []}, {"Name": "60337698", "Vocab": []}, {"Name": "61014967", "Vocab": []}, {"Name": "61021438", "Vocab": []}, {"Name": "61136253", "Vocab": []}, {"Name": "61288356", "Vocab": []}, {"Name": "61392976", "Vocab": []}, {"Name": "61505016", "Vocab": []}, {"Name": "61865041", "Vocab": []}, {"Name": "61878674", "Vocab": []}, {"Name": "62128556", "Vocab": []}, {"Name": "62141991", "Vocab": []}, {"Name": "62199555", "Vocab": []}, {"Name": "62458232", "Vocab": []}, {"Name": "62539131", "Vocab": []}, {"Name": "62911687", "Vocab": []}, {"Name": "63097005", "Vocab": []}, {"Name": "63189061", "Vocab": []}, {"Name": "63873947", "Vocab": []}, {"Name": "63976618", "Vocab": []}, {"Name": "64212547", "Vocab": []}, {"Name": "64292442", "Vocab": []}, {"Name": "64458045", "Vocab": []}, {"Name": "64586263", "Vocab": []}, {"Name": "64681401", "Vocab": []}, {"Name": "64848881", "Vocab": []}, {"Name": "64889979", "Vocab": []}, {"Name": "65026789", "Vocab": []}, {"Name": "65196586", "Vocab": []}, {"Name": "65413548", "Vocab": []}, {"Name": "65504889", "Vocab": []}, {"Name": "65591283", "Vocab": []}, {"Name": "66141805", "Vocab": []}, {"Name": "66162310", "Vocab": []}, {"Name": "66367489", "Vocab": []}, {"Name": "66387543", "Vocab": []}, {"Name": "66498560", "Vocab": []}, {"Name": "66539007", "Vocab": []}, {"Name": "66869187", "Vocab": []}, {"Name": "67069533", "Vocab": []}, {"Name": "67315996", "Vocab": []}, {"Name": "67363190", "Vocab": []}, {"Name": "67650754", "Vocab": []}, {"Name": "67751754", "Vocab": []}, {"Name": "67775018", "Vocab": []}, {"Name": "67820338", "Vocab": []}, {"Name": "67933729", "Vocab": []}, {"Name": "67991795", "Vocab": []}, {"Name": "67999753", "Vocab": []}, {"Name": "68061627", "Vocab": []}, {"Name": "68346308", "Vocab": []}, {"Name": "68709655", "Vocab": []}, {"Name": "68884967", "Vocab": []}, {"Name": "69448563", "Vocab": []}, {"Name": "70034508", "Vocab": []}, {"Name": "70105976", "Vocab": []}, {"Name": "70113161", "Vocab": []}, {"Name": "70181244", "Vocab": []}, {"Name": "70277295", "Vocab": []}, {"Name": "70721725", "Vocab": []}, {"Name": "70925113", "Vocab": []}, {"Name": "70956490", "Vocab": []}, {"Name": "71281530", "Vocab": []}, {"Name": "71314612", "Vocab": []}, {"Name": "71416195", "Vocab": []}, {"Name": "71818183", "Vocab": []}, {"Name": "71832373", "Vocab": []}, {"Name": "72134852", "Vocab": []}, {"Name": "72223317", "Vocab": []}, {"Name": "72240404", "Vocab": []}, {"Name": "72338359", "Vocab": []}, {"Name": "72454322", "Vocab": []}, {"Name": "72744208", "Vocab": []}, {"Name": "73196872", "Vocab": []}, {"Name": "73526484", "Vocab": []}, {"Name": "73842207", "Vocab": []}, {"Name": "73860761", "Vocab": []}, {"Name": "73868980", "Vocab": []}, {"Name": "74095227", "Vocab": []}, {"Name": "74121267", "Vocab": []}, {"Name": "74421966", "Vocab": []}, {"Name": "74946023", "Vocab": []}, {"Name": "74967627", "Vocab": []}, {"Name": "75088508", "Vocab": []}, {"Name": "75311995", "Vocab": []}, {"Name": "75600177", "Vocab": []}, {"Name": "75760842", "Vocab": []}, {"Name": "75820772", "Vocab": []}, {"Name": "75933869", "Vocab": []}, {"Name": "76062909", "Vocab": []}, {"Name": "76109992", "Vocab": []}, {"Name": "76165022", "Vocab": []}, {"Name": "76515242", "Vocab": []}, {"Name": "76695610", "Vocab": []}, {"Name": "76779880", "Vocab": []}, {"Name": "76875695", "Vocab": []}, {"Name": "76977215", "Vocab": []}, {"Name": "77339943", "Vocab": []}, {"Name": "77794884", "Vocab": []}, {"Name": "78013497", "Vocab": []}, {"Name": "78077572", "Vocab": []}, {"Name": "78606269", "Vocab": []}, {"Name": "78829768", "Vocab": []}, {"Name": "79011341", "Vocab": []}, {"Name": "79079156", "Vocab": []}, {"Name": "79134032", "Vocab": []}, {"Name": "79157598", "Vocab": []}, {"Name": "79623828", "Vocab": []}, {"Name": "79630434", "Vocab": []}, {"Name": "79964651", "Vocab": []}, {"Name": "79968563", "Vocab": []}, {"Name": "80222886", "Vocab": []}, {"Name": "80292005", "Vocab": []}, {"Name": "80352910", "Vocab": []}, {"Name": "80448800", "Vocab": []}, {"Name": "80637357", "Vocab": []}, {"Name": "80692142", "Vocab": []}, {"Name": "81035433", "Vocab": []}, {"Name": "81495077", "Vocab": []}, {"Name": "81964016", "Vocab": []}, {"Name": "82390360", "Vocab": []}, {"Name": "82418087", "Vocab": []}, {"Name": "82576421", "Vocab": []}, {"Name": "82606669", "Vocab": []}, {"Name": "82697896", "Vocab": []}, {"Name": "83058027", "Vocab": []}, {"Name": "83252409", "Vocab": []}, {"Name": "83277483", "Vocab": []}, {"Name": "83408473", "Vocab": []}, {"Name": "83491072", "Vocab": []}, {"Name": "83552085", "Vocab": []}, {"Name": "83773997", "Vocab": []}, {"Name": "83791538", "Vocab": []}, {"Name": "84031261", "Vocab": []}, {"Name": "84629477", "Vocab": []}, {"Name": "84722782", "Vocab": []}, {"Name": "84821267", "Vocab": []}, {"Name": "84969957", "Vocab": []}, {"Name": "85005862", "Vocab": []}, {"Name": "85025360", "Vocab": []}, {"Name": "85085721", "Vocab": []}, {"Name": "85244564", "Vocab": []}, {"Name": "85364992", "Vocab": []}, {"Name": "85585780", "Vocab": []}, {"Name": "85599592", "Vocab": []}, {"Name": "85608539", "Vocab": []}, {"Name": "85634787", "Vocab": []}, {"Name": "85792854", "Vocab": []}, {"Name": "86014921", "Vocab": []}, {"Name": "86355939", "Vocab": []}, {"Name": "86585548", "Vocab": []}, {"Name": "86658442", "Vocab": []}, {"Name": "86905850", "Vocab": []}, {"Name": "86955883", "Vocab": []}, {"Name": "87155400", "Vocab": []}, {"Name": "87202441", "Vocab": []}, {"Name": "87684552", "Vocab": []}, {"Name": "87698193", "Vocab": []}, {"Name": "88255259", "Vocab": []}, {"Name": "88532897", "Vocab": []}, {"Name": "88943622", "Vocab": []}, {"Name": "89054358", "Vocab": []}, {"Name": "89139786", "Vocab": []}, {"Name": "89508352", "Vocab": []}, {"Name": "89913428", "Vocab": []}, {"Name": "89923296", "Vocab": []}, {"Name": "90274466", "Vocab": []}, {"Name": "90306269", "Vocab": []}, {"Name": "90316936", "Vocab": []}, {"Name": "90409952", "Vocab": []}, {"Name": "90619889", "Vocab": []}, {"Name": "90661507", "Vocab": []}, {"Name": "90754286", "Vocab": []}, {"Name": "90915632", "Vocab": []}, {"Name": "90957952", "Vocab": []}, {"Name": "90980020", "Vocab": []}, {"Name": "91351570", "Vocab": []}, {"Name": "91416161", "Vocab": []}, {"Name": "91484539", "Vocab": []}, {"Name": "92610561", "Vocab": []}, {"Name": "92637008", "Vocab": []}, {"Name": "93372396", "Vocab": []}, {"Name": "93688613", "Vocab": []}, {"Name": "93730740", "Vocab": []}, {"Name": "93809493", "Vocab": []}, {"Name": "94161174", "Vocab": []}, {"Name": "94582424", "Vocab": []}, {"Name": "95099813", "Vocab": []}, {"Name": "95729731", "Vocab": []}, {"Name": "95977043", "Vocab": []}, {"Name": "96083691", "Vocab": []}, {"Name": "96158132", "Vocab": []}, {"Name": "96327917", "Vocab": []}, {"Name": "96519608", "Vocab": []}, {"Name": "96879271", "Vocab": []}, {"Name": "97123255", "Vocab": []}, {"Name": "97271352", "Vocab": []}, {"Name": "97445024", "Vocab": []}, {"Name": "97734269", "Vocab": []}, {"Name": "97772497", "Vocab": []}, {"Name": "97773411", "Vocab": []}, {"Name": "98023474", "Vocab": []}, {"Name": "98117193", "Vocab": []}, {"Name": "98395612", "Vocab": []}, {"Name": "98609540", "Vocab": []}, {"Name": "98651194", "Vocab": []}, {"Name": "99138371", "Vocab": []}, {"Name": "99314878", "Vocab": []}, {"Name": "99400822", "Vocab": []}, {"Name": "99403569", "Vocab": []}, {"Name": "99576129", "Vocab": []}, {"Name": "99713370", "Vocab": []}, {"Name": "99725691", "Vocab": []}, {"Name": "99766695", "Vocab": []}, {"Name": "99962084", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "DS3011_DSGOptions_DM_dtmf", "Grammars": ["DS3011_DSGOptions_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "request-extension_dsg", "Vocab": []}, {"Name": "edge_pw", "Vocab": []}, {"Name": "access", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "DS3011_OfferExtension_DM_dtmf", "Grammars": ["DS3011_OfferExtension_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "extension", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "ES1505_SubmitSwapTransition_DM_dtmf", "Grammars": ["ES1505_SubmitSwapTransition_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommands_CF1010_dtmf", "Grammars": ["GlobalCommands_CF1010_dtmf.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "GlobalCommands_dtmf_MM1038", "Grammars": ["GlobalCommands_dtmf_MM1038.grxml"], "SWI_meaning": [{"Name": "repeat", "Vocab": []}, {"Name": "operator", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "IH1030_TeachLanguage_DM_dtmf", "Grammars": ["IH1030_TeachLanguage_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "spanish", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "IH2005_AskCallingAboutConfigYN_DM_dtmf", "Grammars": ["IH2005_AskCallingAboutConfigYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "IH2110_AskWantSMSYN_DM_dtmf", "Grammars": ["IH2110_AskWantSMSYN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "LG1405_DSGCollectMDN_DM_dtmf (Complex)", "Grammars": ["LG1405_DSGCollectMDN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "0001759014", "Vocab": []}, {"Name": "0012939864", "Vocab": []}, {"Name": "0026479300", "Vocab": []}, {"Name": "0044859459", "Vocab": []}, {"Name": "0051522731", "Vocab": []}, {"Name": "0059291187", "Vocab": []}, {"Name": "0062851660", "Vocab": []}, {"Name": "0064003852", "Vocab": []}, {"Name": "0067346914", "Vocab": []}, {"Name": "0092484885", "Vocab": []}, {"Name": "0095018359", "Vocab": []}, {"Name": "0100226515", "Vocab": []}, {"Name": "0122728324", "Vocab": []}, {"Name": "0134643152", "Vocab": []}, {"Name": "0140044768", "Vocab": []}, {"Name": "0140963815", "Vocab": []}, {"Name": "0144313385", "Vocab": []}, {"Name": "0146407729", "Vocab": []}, {"Name": "0149042582", "Vocab": []}, {"Name": "0163724540", "Vocab": []}, {"Name": "0166691747", "Vocab": []}, {"Name": "0216126851", "Vocab": []}, {"Name": "0219893555", "Vocab": []}, {"Name": "0221726944", "Vocab": []}, {"Name": "0223742880", "Vocab": []}, {"Name": "0228039087", "Vocab": []}, {"Name": "0239540566", "Vocab": []}, {"Name": "0239600739", "Vocab": []}, {"Name": "0278145915", "Vocab": []}, {"Name": "0293803758", "Vocab": []}, {"Name": "0296054116", "Vocab": []}, {"Name": "0305620284", "Vocab": []}, {"Name": "0321260214", "Vocab": []}, {"Name": "0343776968", "Vocab": []}, {"Name": "0364291035", "Vocab": []}, {"Name": "0379700040", "Vocab": []}, {"Name": "0388347692", "Vocab": []}, {"Name": "0389329138", "Vocab": []}, {"Name": "0410252055", "Vocab": []}, {"Name": "0411221475", "Vocab": []}, {"Name": "0434197350", "Vocab": []}, {"Name": "0457502973", "Vocab": []}, {"Name": "0459706622", "Vocab": []}, {"Name": "0461899042", "Vocab": []}, {"Name": "0462834453", "Vocab": []}, {"Name": "0467054237", "Vocab": []}, {"Name": "0472877859", "Vocab": []}, {"Name": "0484581243", "Vocab": []}, {"Name": "0486826729", "Vocab": []}, {"Name": "0541887602", "Vocab": []}, {"Name": "0544118415", "Vocab": []}, {"Name": "0557934905", "Vocab": []}, {"Name": "0568281009", "Vocab": []}, {"Name": "0573350447", "Vocab": []}, {"Name": "0582392142", "Vocab": []}, {"Name": "0597405329", "Vocab": []}, {"Name": "0607153995", "Vocab": []}, {"Name": "0612046376", "Vocab": []}, {"Name": "0621614824", "Vocab": []}, {"Name": "0634588582", "Vocab": []}, {"Name": "0647674277", "Vocab": []}, {"Name": "0659660856", "Vocab": []}, {"Name": "0659768728", "Vocab": []}, {"Name": "0669509327", "Vocab": []}, {"Name": "0672206996", "Vocab": []}, {"Name": "0673148640", "Vocab": []}, {"Name": "0681210610", "Vocab": []}, {"Name": "0691332396", "Vocab": []}, {"Name": "0719355197", "Vocab": []}, {"Name": "0725298235", "Vocab": []}, {"Name": "0753337919", "Vocab": []}, {"Name": "0760414348", "Vocab": []}, {"Name": "0768874684", "Vocab": []}, {"Name": "0790185324", "Vocab": []}, {"Name": "0798568603", "Vocab": []}, {"Name": "0807361381", "Vocab": []}, {"Name": "0811295504", "Vocab": []}, {"Name": "0823119374", "Vocab": []}, {"Name": "0829196348", "Vocab": []}, {"Name": "0843333429", "Vocab": []}, {"Name": "0854739054", "Vocab": []}, {"Name": "0861836997", "Vocab": []}, {"Name": "0884313715", "Vocab": []}, {"Name": "0884752622", "Vocab": []}, {"Name": "0888950455", "Vocab": []}, {"Name": "0902881899", "Vocab": []}, {"Name": "0914909354", "Vocab": []}, {"Name": "0920764517", "Vocab": []}, {"Name": "0928149444", "Vocab": []}, {"Name": "0928639486", "Vocab": []}, {"Name": "0935743525", "Vocab": []}, {"Name": "0940718913", "Vocab": []}, {"Name": "0971105524", "Vocab": []}, {"Name": "0972195541", "Vocab": []}, {"Name": "0972562363", "Vocab": []}, {"Name": "0992944731", "Vocab": []}, {"Name": "0999322200", "Vocab": []}, {"Name": "1009955954", "Vocab": []}, {"Name": "1017804646", "Vocab": []}, {"Name": "1021691064", "Vocab": []}, {"Name": "1024167198", "Vocab": []}, {"Name": "1029936094", "Vocab": []}, {"Name": "1044411565", "Vocab": []}, {"Name": "1054395142", "Vocab": []}, {"Name": "1056126452", "Vocab": []}, {"Name": "1074355046", "Vocab": []}, {"Name": "1076563917", "Vocab": []}, {"Name": "1078159507", "Vocab": []}, {"Name": "1084868039", "Vocab": []}, {"Name": "1095973246", "Vocab": []}, {"Name": "1108744277", "Vocab": []}, {"Name": "1110512035", "Vocab": []}, {"Name": "1135004676", "Vocab": []}, {"Name": "1143327649", "Vocab": []}, {"Name": "1153123030", "Vocab": []}, {"Name": "1160455210", "Vocab": []}, {"Name": "1179633041", "Vocab": []}, {"Name": "1181372755", "Vocab": []}, {"Name": "1183249253", "Vocab": []}, {"Name": "1187965528", "Vocab": []}, {"Name": "1208213381", "Vocab": []}, {"Name": "1215520708", "Vocab": []}, {"Name": "1219483211", "Vocab": []}, {"Name": "1222670709", "Vocab": []}, {"Name": "1229359122", "Vocab": []}, {"Name": "1241942468", "Vocab": []}, {"Name": "1266395719", "Vocab": []}, {"Name": "1283480687", "Vocab": []}, {"Name": "1296589481", "Vocab": []}, {"Name": "1302264483", "Vocab": []}, {"Name": "1309553356", "Vocab": []}, {"Name": "1324016611", "Vocab": []}, {"Name": "1330041565", "Vocab": []}, {"Name": "1330337190", "Vocab": []}, {"Name": "1332017329", "Vocab": []}, {"Name": "1332046474", "Vocab": []}, {"Name": "1345877687", "Vocab": []}, {"Name": "1345985918", "Vocab": []}, {"Name": "1348225702", "Vocab": []}, {"Name": "1351738773", "Vocab": []}, {"Name": "1361042952", "Vocab": []}, {"Name": "1371213980", "Vocab": []}, {"Name": "1379430090", "Vocab": []}, {"Name": "1382878349", "Vocab": []}, {"Name": "1387330318", "Vocab": []}, {"Name": "1391537653", "Vocab": []}, {"Name": "1395671634", "Vocab": []}, {"Name": "1397784301", "Vocab": []}, {"Name": "1400214503", "Vocab": []}, {"Name": "1414430683", "Vocab": []}, {"Name": "1422872758", "Vocab": []}, {"Name": "1423845717", "Vocab": []}, {"Name": "1426056468", "Vocab": []}, {"Name": "1427810263", "Vocab": []}, {"Name": "1436064938", "Vocab": []}, {"Name": "1458933835", "Vocab": []}, {"Name": "1468074901", "Vocab": []}, {"Name": "1473478166", "Vocab": []}, {"Name": "1482110345", "Vocab": []}, {"Name": "1501684343", "Vocab": []}, {"Name": "1503060800", "Vocab": []}, {"Name": "1506492188", "Vocab": []}, {"Name": "1506541522", "Vocab": []}, {"Name": "1516717569", "Vocab": []}, {"Name": "1524694997", "Vocab": []}, {"Name": "1534902293", "Vocab": []}, {"Name": "1553800991", "Vocab": []}, {"Name": "1577287520", "Vocab": []}, {"Name": "1580726898", "Vocab": []}, {"Name": "1583738042", "Vocab": []}, {"Name": "1584814239", "Vocab": []}, {"Name": "1588266810", "Vocab": []}, {"Name": "1591650870", "Vocab": []}, {"Name": "1598862174", "Vocab": []}, {"Name": "1600131832", "Vocab": []}, {"Name": "1602578982", "Vocab": []}, {"Name": "1604990678", "Vocab": []}, {"Name": "1637681143", "Vocab": []}, {"Name": "1639442350", "Vocab": []}, {"Name": "1639567361", "Vocab": []}, {"Name": "1647845787", "Vocab": []}, {"Name": "1648130174", "Vocab": []}, {"Name": "1651327517", "Vocab": []}, {"Name": "1658199436", "Vocab": []}, {"Name": "1671500954", "Vocab": []}, {"Name": "1693762179", "Vocab": []}, {"Name": "1701595410", "Vocab": []}, {"Name": "1710687753", "Vocab": []}, {"Name": "1717024877", "Vocab": []}, {"Name": "1717732983", "Vocab": []}, {"Name": "1717835221", "Vocab": []}, {"Name": "1731694118", "Vocab": []}, {"Name": "1733835305", "Vocab": []}, {"Name": "1759372359", "Vocab": []}, {"Name": "1780303292", "Vocab": []}, {"Name": "1786470355", "Vocab": []}, {"Name": "1788848349", "Vocab": []}, {"Name": "1803765180", "Vocab": []}, {"Name": "1804504247", "Vocab": []}, {"Name": "1813468694", "Vocab": []}, {"Name": "1823097789", "Vocab": []}, {"Name": "1850063167", "Vocab": []}, {"Name": "1853246687", "Vocab": []}, {"Name": "1868090061", "Vocab": []}, {"Name": "1896829327", "Vocab": []}, {"Name": "1932385810", "Vocab": []}, {"Name": "1951861385", "Vocab": []}, {"Name": "1957390655", "Vocab": []}, {"Name": "1968631455", "Vocab": []}, {"Name": "1971348681", "Vocab": []}, {"Name": "1975088886", "Vocab": []}, {"Name": "1975745366", "Vocab": []}, {"Name": "1981236451", "Vocab": []}, {"Name": "1985361953", "Vocab": []}, {"Name": "1991835061", "Vocab": []}, {"Name": "2029288084", "Vocab": []}, {"Name": "2044485786", "Vocab": []}, {"Name": "2050181497", "Vocab": []}, {"Name": "2061215915", "Vocab": []}, {"Name": "2069724882", "Vocab": []}, {"Name": "2086593938", "Vocab": []}, {"Name": "2097112848", "Vocab": []}, {"Name": "2100050458", "Vocab": []}, {"Name": "2102139290", "Vocab": []}, {"Name": "2110770057", "Vocab": []}, {"Name": "2122258039", "Vocab": []}, {"Name": "2159601831", "Vocab": []}, {"Name": "2160872598", "Vocab": []}, {"Name": "2187999514", "Vocab": []}, {"Name": "2193155157", "Vocab": []}, {"Name": "2203185146", "Vocab": []}, {"Name": "2204450325", "Vocab": []}, {"Name": "2219207257", "Vocab": []}, {"Name": "2223118180", "Vocab": []}, {"Name": "2234439430", "Vocab": []}, {"Name": "2236329361", "Vocab": []}, {"Name": "2242674560", "Vocab": []}, {"Name": "2256570962", "Vocab": []}, {"Name": "2277431082", "Vocab": []}, {"Name": "2283461825", "Vocab": []}, {"Name": "2298405756", "Vocab": []}, {"Name": "2314952214", "Vocab": []}, {"Name": "2322213745", "Vocab": []}, {"Name": "2325228476", "Vocab": []}, {"Name": "2327011674", "Vocab": []}, {"Name": "2346356150", "Vocab": []}, {"Name": "2348998984", "Vocab": []}, {"Name": "2359410145", "Vocab": []}, {"Name": "2362641062", "Vocab": []}, {"Name": "2364241621", "Vocab": []}, {"Name": "2371136038", "Vocab": []}, {"Name": "2373609517", "Vocab": []}, {"Name": "2381654169", "Vocab": []}, {"Name": "2406572562", "Vocab": []}, {"Name": "2416342734", "Vocab": []}, {"Name": "2422211771", "Vocab": []}, {"Name": "2424892802", "Vocab": []}, {"Name": "2430500618", "Vocab": []}, {"Name": "2433035748", "Vocab": []}, {"Name": "2441893628", "Vocab": []}, {"Name": "2450318456", "Vocab": []}, {"Name": "2451588006", "Vocab": []}, {"Name": "2467058517", "Vocab": []}, {"Name": "2467466013", "Vocab": []}, {"Name": "2473196670", "Vocab": []}, {"Name": "2499573557", "Vocab": []}, {"Name": "2501580867", "Vocab": []}, {"Name": "2524715602", "Vocab": []}, {"Name": "2541136121", "Vocab": []}, {"Name": "2563808190", "Vocab": []}, {"Name": "2564240443", "Vocab": []}, {"Name": "2567286351", "Vocab": []}, {"Name": "2588450536", "Vocab": []}, {"Name": "2606274237", "Vocab": []}, {"Name": "2617262257", "Vocab": []}, {"Name": "2628129455", "Vocab": []}, {"Name": "2652090927", "Vocab": []}, {"Name": "2654960065", "Vocab": []}, {"Name": "2656400617", "Vocab": []}, {"Name": "2656962911", "Vocab": []}, {"Name": "2657693562", "Vocab": []}, {"Name": "2662031889", "Vocab": []}, {"Name": "2683866465", "Vocab": []}, {"Name": "2684114708", "Vocab": []}, {"Name": "2692130854", "Vocab": []}, {"Name": "2700359003", "Vocab": []}, {"Name": "2712266341", "Vocab": []}, {"Name": "2752516462", "Vocab": []}, {"Name": "2771437717", "Vocab": []}, {"Name": "2790880904", "Vocab": []}, {"Name": "2795270094", "Vocab": []}, {"Name": "2803519109", "Vocab": []}, {"Name": "2803531586", "Vocab": []}, {"Name": "2807697933", "Vocab": []}, {"Name": "2849023206", "Vocab": []}, {"Name": "2850385405", "Vocab": []}, {"Name": "2856516746", "Vocab": []}, {"Name": "2892845631", "Vocab": []}, {"Name": "2897028389", "Vocab": []}, {"Name": "2905945451", "Vocab": []}, {"Name": "2908059766", "Vocab": []}, {"Name": "2908719208", "Vocab": []}, {"Name": "2920116933", "Vocab": []}, {"Name": "2924305185", "Vocab": []}, {"Name": "2930950319", "Vocab": []}, {"Name": "2961249079", "Vocab": []}, {"Name": "2972514079", "Vocab": []}, {"Name": "2989810913", "Vocab": []}, {"Name": "3002142023", "Vocab": []}, {"Name": "3005589608", "Vocab": []}, {"Name": "3011752900", "Vocab": []}, {"Name": "3027054125", "Vocab": []}, {"Name": "3035986776", "Vocab": []}, {"Name": "3044643697", "Vocab": []}, {"Name": "3065702952", "Vocab": []}, {"Name": "3066518402", "Vocab": []}, {"Name": "3067519507", "Vocab": []}, {"Name": "3067899595", "Vocab": []}, {"Name": "3075151331", "Vocab": []}, {"Name": "3111255544", "Vocab": []}, {"Name": "3124425376", "Vocab": []}, {"Name": "3144211399", "Vocab": []}, {"Name": "3145990351", "Vocab": []}, {"Name": "3150291712", "Vocab": []}, {"Name": "3158492945", "Vocab": []}, {"Name": "3171951408", "Vocab": []}, {"Name": "3186180053", "Vocab": []}, {"Name": "3186769428", "Vocab": []}, {"Name": "3189473028", "Vocab": []}, {"Name": "3206506284", "Vocab": []}, {"Name": "3213111882", "Vocab": []}, {"Name": "3232502886", "Vocab": []}, {"Name": "3238418937", "Vocab": []}, {"Name": "3241241360", "Vocab": []}, {"Name": "3249520478", "Vocab": []}, {"Name": "3259914915", "Vocab": []}, {"Name": "3272448681", "Vocab": []}, {"Name": "3273188351", "Vocab": []}, {"Name": "3274557532", "Vocab": []}, {"Name": "3278920041", "Vocab": []}, {"Name": "3283075186", "Vocab": []}, {"Name": "3293494801", "Vocab": []}, {"Name": "3299131960", "Vocab": []}, {"Name": "3300071604", "Vocab": []}, {"Name": "3306776706", "Vocab": []}, {"Name": "3325857833", "Vocab": []}, {"Name": "3330724673", "Vocab": []}, {"Name": "3331631277", "Vocab": []}, {"Name": "3352556097", "Vocab": []}, {"Name": "3371113382", "Vocab": []}, {"Name": "3374631345", "Vocab": []}, {"Name": "3384605380", "Vocab": []}, {"Name": "3389878450", "Vocab": []}, {"Name": "3396050701", "Vocab": []}, {"Name": "3415018225", "Vocab": []}, {"Name": "3418250167", "Vocab": []}, {"Name": "3422081288", "Vocab": []}, {"Name": "3422459295", "Vocab": []}, {"Name": "3423092711", "Vocab": []}, {"Name": "3436872377", "Vocab": []}, {"Name": "3442818096", "Vocab": []}, {"Name": "3444399825", "Vocab": []}, {"Name": "3445301547", "Vocab": []}, {"Name": "3452017832", "Vocab": []}, {"Name": "3454300092", "Vocab": []}, {"Name": "3457017152", "Vocab": []}, {"Name": "3465403056", "Vocab": []}, {"Name": "3480218702", "Vocab": []}, {"Name": "3486730734", "Vocab": []}, {"Name": "3496279679", "Vocab": []}, {"Name": "3497662727", "Vocab": []}, {"Name": "3516722659", "Vocab": []}, {"Name": "3517498905", "Vocab": []}, {"Name": "3518382344", "Vocab": []}, {"Name": "3520306735", "Vocab": []}, {"Name": "3526877233", "Vocab": []}, {"Name": "3558717884", "Vocab": []}, {"Name": "3574243043", "Vocab": []}, {"Name": "3576107496", "Vocab": []}, {"Name": "3590171294", "Vocab": []}, {"Name": "3590249207", "Vocab": []}, {"Name": "3591944278", "Vocab": []}, {"Name": "3592479405", "Vocab": []}, {"Name": "3616110386", "Vocab": []}, {"Name": "3624407605", "Vocab": []}, {"Name": "3626554070", "Vocab": []}, {"Name": "3641262237", "Vocab": []}, {"Name": "3641756295", "Vocab": []}, {"Name": "3644938139", "Vocab": []}, {"Name": "3652005272", "Vocab": []}, {"Name": "3661757289", "Vocab": []}, {"Name": "3664890211", "Vocab": []}, {"Name": "3673686433", "Vocab": []}, {"Name": "3674908009", "Vocab": []}, {"Name": "3680344859", "Vocab": []}, {"Name": "3692051761", "Vocab": []}, {"Name": "3697955889", "Vocab": []}, {"Name": "3702994344", "Vocab": []}, {"Name": "3722800569", "Vocab": []}, {"Name": "3725388046", "Vocab": []}, {"Name": "3727130451", "Vocab": []}, {"Name": "3734678144", "Vocab": []}, {"Name": "3741906393", "Vocab": []}, {"Name": "3744657928", "Vocab": []}, {"Name": "3751162425", "Vocab": []}, {"Name": "3761476524", "Vocab": []}, {"Name": "3761528329", "Vocab": []}, {"Name": "3776281964", "Vocab": []}, {"Name": "3778366530", "Vocab": []}, {"Name": "3805917257", "Vocab": []}, {"Name": "3807304224", "Vocab": []}, {"Name": "3845848398", "Vocab": []}, {"Name": "3859082785", "Vocab": []}, {"Name": "3869771020", "Vocab": []}, {"Name": "3871176314", "Vocab": []}, {"Name": "3876626154", "Vocab": []}, {"Name": "3883513849", "Vocab": []}, {"Name": "3909079758", "Vocab": []}, {"Name": "3924513687", "Vocab": []}, {"Name": "3930257912", "Vocab": []}, {"Name": "3943106755", "Vocab": []}, {"Name": "3948388849", "Vocab": []}, {"Name": "3955501406", "Vocab": []}, {"Name": "3957956338", "Vocab": []}, {"Name": "3974636123", "Vocab": []}, {"Name": "3978235377", "Vocab": []}, {"Name": "3985150357", "Vocab": []}, {"Name": "3991654360", "Vocab": []}, {"Name": "3995067396", "Vocab": []}, {"Name": "4029337475", "Vocab": []}, {"Name": "4042871465", "Vocab": []}, {"Name": "4043665526", "Vocab": []}, {"Name": "4067229778", "Vocab": []}, {"Name": "4079001283", "Vocab": []}, {"Name": "4107552997", "Vocab": []}, {"Name": "4149307139", "Vocab": []}, {"Name": "4155792165", "Vocab": []}, {"Name": "4161308609", "Vocab": []}, {"Name": "4179494779", "Vocab": []}, {"Name": "4188941771", "Vocab": []}, {"Name": "4212217629", "Vocab": []}, {"Name": "4221718226", "Vocab": []}, {"Name": "4224993251", "Vocab": []}, {"Name": "4231954799", "Vocab": []}, {"Name": "4247015844", "Vocab": []}, {"Name": "4263617856", "Vocab": []}, {"Name": "4278936454", "Vocab": []}, {"Name": "4281490346", "Vocab": []}, {"Name": "4290243498", "Vocab": []}, {"Name": "4303296334", "Vocab": []}, {"Name": "4313454890", "Vocab": []}, {"Name": "4313937264", "Vocab": []}, {"Name": "4314683159", "Vocab": []}, {"Name": "4317208267", "Vocab": []}, {"Name": "4320311542", "Vocab": []}, {"Name": "4322207050", "Vocab": []}, {"Name": "4326512101", "Vocab": []}, {"Name": "4351463073", "Vocab": []}, {"Name": "4368490175", "Vocab": []}, {"Name": "4378977401", "Vocab": []}, {"Name": "4384678999", "Vocab": []}, {"Name": "4385253944", "Vocab": []}, {"Name": "4387521470", "Vocab": []}, {"Name": "4398505792", "Vocab": []}, {"Name": "4398924510", "Vocab": []}, {"Name": "4400610831", "Vocab": []}, {"Name": "4403396355", "Vocab": []}, {"Name": "4405282067", "Vocab": []}, {"Name": "4410632355", "Vocab": []}, {"Name": "4410685076", "Vocab": []}, {"Name": "4432187179", "Vocab": []}, {"Name": "4466418151", "Vocab": []}, {"Name": "4475605397", "Vocab": []}, {"Name": "4476785122", "Vocab": []}, {"Name": "4502230703", "Vocab": []}, {"Name": "4507304253", "Vocab": []}, {"Name": "4533247572", "Vocab": []}, {"Name": "4547011822", "Vocab": []}, {"Name": "4549220116", "Vocab": []}, {"Name": "4580028677", "Vocab": []}, {"Name": "4589453672", "Vocab": []}, {"Name": "4593803649", "Vocab": []}, {"Name": "4598466395", "Vocab": []}, {"Name": "4600085329", "Vocab": []}, {"Name": "4609114823", "Vocab": []}, {"Name": "4609408855", "Vocab": []}, {"Name": "4636445833", "Vocab": []}, {"Name": "4653622635", "Vocab": []}, {"Name": "4661539413", "Vocab": []}, {"Name": "4681084559", "Vocab": []}, {"Name": "4692968368", "Vocab": []}, {"Name": "4695830727", "Vocab": []}, {"Name": "4703799599", "Vocab": []}, {"Name": "4718930991", "Vocab": []}, {"Name": "4728666314", "Vocab": []}, {"Name": "4747755785", "Vocab": []}, {"Name": "4750408789", "Vocab": []}, {"Name": "4769243793", "Vocab": []}, {"Name": "4771286593", "Vocab": []}, {"Name": "4792071177", "Vocab": []}, {"Name": "4792507079", "Vocab": []}, {"Name": "4795979088", "Vocab": []}, {"Name": "4805071128", "Vocab": []}, {"Name": "4806300149", "Vocab": []}, {"Name": "4811141858", "Vocab": []}, {"Name": "4825896878", "Vocab": []}, {"Name": "4842101763", "Vocab": []}, {"Name": "4842577653", "Vocab": []}, {"Name": "4849707219", "Vocab": []}, {"Name": "4851605117", "Vocab": []}, {"Name": "4895059195", "Vocab": []}, {"Name": "4898660601", "Vocab": []}, {"Name": "4935895936", "Vocab": []}, {"Name": "4943704309", "Vocab": []}, {"Name": "4949436934", "Vocab": []}, {"Name": "4963136027", "Vocab": []}, {"Name": "4979986926", "Vocab": []}, {"Name": "4998393720", "Vocab": []}, {"Name": "5001491535", "Vocab": []}, {"Name": "5010671000", "Vocab": []}, {"Name": "5013207259", "Vocab": []}, {"Name": "5016519852", "Vocab": []}, {"Name": "5023133393", "Vocab": []}, {"Name": "5025767659", "Vocab": []}, {"Name": "5030519690", "Vocab": []}, {"Name": "5047562632", "Vocab": []}, {"Name": "5053447553", "Vocab": []}, {"Name": "5055644959", "Vocab": []}, {"Name": "5079226519", "Vocab": []}, {"Name": "5080364406", "Vocab": []}, {"Name": "5086208465", "Vocab": []}, {"Name": "5097018329", "Vocab": []}, {"Name": "5117183430", "Vocab": []}, {"Name": "5147041777", "Vocab": []}, {"Name": "5164683440", "Vocab": []}, {"Name": "5181895457", "Vocab": []}, {"Name": "5192251707", "Vocab": []}, {"Name": "5201344481", "Vocab": []}, {"Name": "5203223436", "Vocab": []}, {"Name": "5207613932", "Vocab": []}, {"Name": "5227917351", "Vocab": []}, {"Name": "5278565635", "Vocab": []}, {"Name": "5283594347", "Vocab": []}, {"Name": "5289373148", "Vocab": []}, {"Name": "5308954682", "Vocab": []}, {"Name": "5314130278", "Vocab": []}, {"Name": "5320114068", "Vocab": []}, {"Name": "5341213556", "Vocab": []}, {"Name": "5346135846", "Vocab": []}, {"Name": "5356988844", "Vocab": []}, {"Name": "5361082700", "Vocab": []}, {"Name": "5366869112", "Vocab": []}, {"Name": "5384368442", "Vocab": []}, {"Name": "5390467074", "Vocab": []}, {"Name": "5421913880", "Vocab": []}, {"Name": "5438349193", "Vocab": []}, {"Name": "5457749392", "Vocab": []}, {"Name": "5470315545", "Vocab": []}, {"Name": "5473632739", "Vocab": []}, {"Name": "5475409888", "Vocab": []}, {"Name": "5481166321", "Vocab": []}, {"Name": "5481676703", "Vocab": []}, {"Name": "5484863860", "Vocab": []}, {"Name": "5487227676", "Vocab": []}, {"Name": "5502102773", "Vocab": []}, {"Name": "5518430996", "Vocab": []}, {"Name": "5527674632", "Vocab": []}, {"Name": "5537270707", "Vocab": []}, {"Name": "5553563092", "Vocab": []}, {"Name": "5561132745", "Vocab": []}, {"Name": "5561631534", "Vocab": []}, {"Name": "5572053673", "Vocab": []}, {"Name": "5577026098", "Vocab": []}, {"Name": "5585449300", "Vocab": []}, {"Name": "5599749490", "Vocab": []}, {"Name": "5605000141", "Vocab": []}, {"Name": "5635015352", "Vocab": []}, {"Name": "5640688396", "Vocab": []}, {"Name": "5650749704", "Vocab": []}, {"Name": "5671323071", "Vocab": []}, {"Name": "5685168013", "Vocab": []}, {"Name": "5693718484", "Vocab": []}, {"Name": "5704873424", "Vocab": []}, {"Name": "5711503035", "Vocab": []}, {"Name": "5716334714", "Vocab": []}, {"Name": "5726972334", "Vocab": []}, {"Name": "5734390073", "Vocab": []}, {"Name": "5739550855", "Vocab": []}, {"Name": "5745044669", "Vocab": []}, {"Name": "5775790321", "Vocab": []}, {"Name": "5781397264", "Vocab": []}, {"Name": "5788635020", "Vocab": []}, {"Name": "5795153441", "Vocab": []}, {"Name": "5828301266", "Vocab": []}, {"Name": "5833170833", "Vocab": []}, {"Name": "5851604780", "Vocab": []}, {"Name": "5857810920", "Vocab": []}, {"Name": "5864187246", "Vocab": []}, {"Name": "5868441202", "Vocab": []}, {"Name": "5868839217", "Vocab": []}, {"Name": "5893705401", "Vocab": []}, {"Name": "5915263486", "Vocab": []}, {"Name": "5921501326", "Vocab": []}, {"Name": "5941067791", "Vocab": []}, {"Name": "5952241596", "Vocab": []}, {"Name": "5957656262", "Vocab": []}, {"Name": "5972699535", "Vocab": []}, {"Name": "5997541772", "Vocab": []}, {"Name": "6003100005", "Vocab": []}, {"Name": "6008406263", "Vocab": []}, {"Name": "6010383843", "Vocab": []}, {"Name": "6010740123", "Vocab": []}, {"Name": "6017178493", "Vocab": []}, {"Name": "6032632565", "Vocab": []}, {"Name": "6035815482", "Vocab": []}, {"Name": "6044859804", "Vocab": []}, {"Name": "6048478650", "Vocab": []}, {"Name": "6090594687", "Vocab": []}, {"Name": "6091271018", "Vocab": []}, {"Name": "6106174930", "Vocab": []}, {"Name": "6115528153", "Vocab": []}, {"Name": "6121789020", "Vocab": []}, {"Name": "6128341438", "Vocab": []}, {"Name": "6160360380", "Vocab": []}, {"Name": "6183796154", "Vocab": []}, {"Name": "6189028375", "Vocab": []}, {"Name": "6203503624", "Vocab": []}, {"Name": "6212903312", "Vocab": []}, {"Name": "6217359792", "Vocab": []}, {"Name": "6242059655", "Vocab": []}, {"Name": "6246178054", "Vocab": []}, {"Name": "6246853900", "Vocab": []}, {"Name": "6247882770", "Vocab": []}, {"Name": "6251534080", "Vocab": []}, {"Name": "6253842282", "Vocab": []}, {"Name": "6261050997", "Vocab": []}, {"Name": "6270633449", "Vocab": []}, {"Name": "6278815091", "Vocab": []}, {"Name": "6286795246", "Vocab": []}, {"Name": "6300709284", "Vocab": []}, {"Name": "6301771928", "Vocab": []}, {"Name": "6308020502", "Vocab": []}, {"Name": "6325396549", "Vocab": []}, {"Name": "6338644333", "Vocab": []}, {"Name": "6340907581", "Vocab": []}, {"Name": "6345935238", "Vocab": []}, {"Name": "6349559409", "Vocab": []}, {"Name": "6351673466", "Vocab": []}, {"Name": "6352626383", "Vocab": []}, {"Name": "6354568327", "Vocab": []}, {"Name": "6356672760", "Vocab": []}, {"Name": "6362374639", "Vocab": []}, {"Name": "6388681449", "Vocab": []}, {"Name": "6408973949", "Vocab": []}, {"Name": "6445297018", "Vocab": []}, {"Name": "6445479827", "Vocab": []}, {"Name": "6460175328", "Vocab": []}, {"Name": "6475897331", "Vocab": []}, {"Name": "6491542630", "Vocab": []}, {"Name": "6496267432", "Vocab": []}, {"Name": "6517882134", "Vocab": []}, {"Name": "6523251352", "Vocab": []}, {"Name": "6566287497", "Vocab": []}, {"Name": "6580350059", "Vocab": []}, {"Name": "6591534030", "Vocab": []}, {"Name": "6593607853", "Vocab": []}, {"Name": "6604882816", "Vocab": []}, {"Name": "6615020344", "Vocab": []}, {"Name": "6616339946", "Vocab": []}, {"Name": "6630965070", "Vocab": []}, {"Name": "6632213981", "Vocab": []}, {"Name": "6650054942", "Vocab": []}, {"Name": "6663533399", "Vocab": []}, {"Name": "6668467620", "Vocab": []}, {"Name": "6674064539", "Vocab": []}, {"Name": "6674417539", "Vocab": []}, {"Name": "6699664748", "Vocab": []}, {"Name": "6708687509", "Vocab": []}, {"Name": "6718405555", "Vocab": []}, {"Name": "6725133085", "Vocab": []}, {"Name": "6746062776", "Vocab": []}, {"Name": "6757530482", "Vocab": []}, {"Name": "6770960563", "Vocab": []}, {"Name": "6779014185", "Vocab": []}, {"Name": "6786163151", "Vocab": []}, {"Name": "6799676222", "Vocab": []}, {"Name": "6806207199", "Vocab": []}, {"Name": "6818303343", "Vocab": []}, {"Name": "6820514819", "Vocab": []}, {"Name": "6832524346", "Vocab": []}, {"Name": "6833978761", "Vocab": []}, {"Name": "6861642745", "Vocab": []}, {"Name": "6899763360", "Vocab": []}, {"Name": "6908754082", "Vocab": []}, {"Name": "6922639614", "Vocab": []}, {"Name": "6931208297", "Vocab": []}, {"Name": "6957820001", "Vocab": []}, {"Name": "6990636568", "Vocab": []}, {"Name": "6993604437", "Vocab": []}, {"Name": "7003389024", "Vocab": []}, {"Name": "7012939188", "Vocab": []}, {"Name": "7015277076", "Vocab": []}, {"Name": "7015519316", "Vocab": []}, {"Name": "7059196010", "Vocab": []}, {"Name": "7060931299", "Vocab": []}, {"Name": "7072565265", "Vocab": []}, {"Name": "7075107610", "Vocab": []}, {"Name": "7101915031", "Vocab": []}, {"Name": "7102342584", "Vocab": []}, {"Name": "7102555009", "Vocab": []}, {"Name": "7103902323", "Vocab": []}, {"Name": "7105738621", "Vocab": []}, {"Name": "7141771266", "Vocab": []}, {"Name": "7142077173", "Vocab": []}, {"Name": "7146419742", "Vocab": []}, {"Name": "7150076715", "Vocab": []}, {"Name": "7154760686", "Vocab": []}, {"Name": "7161210964", "Vocab": []}, {"Name": "7161787520", "Vocab": []}, {"Name": "7169065315", "Vocab": []}, {"Name": "7170827867", "Vocab": []}, {"Name": "7172286931", "Vocab": []}, {"Name": "7175149487", "Vocab": []}, {"Name": "7177358887", "Vocab": []}, {"Name": "7182240310", "Vocab": []}, {"Name": "7183337052", "Vocab": []}, {"Name": "7183948293", "Vocab": []}, {"Name": "7187085630", "Vocab": []}, {"Name": "7189249894", "Vocab": []}, {"Name": "7202996779", "Vocab": []}, {"Name": "7204897424", "Vocab": []}, {"Name": "7216119852", "Vocab": []}, {"Name": "7240370937", "Vocab": []}, {"Name": "7250199892", "Vocab": []}, {"Name": "7265647632", "Vocab": []}, {"Name": "7286804082", "Vocab": []}, {"Name": "7292674726", "Vocab": []}, {"Name": "7310252362", "Vocab": []}, {"Name": "7322422052", "Vocab": []}, {"Name": "7323140238", "Vocab": []}, {"Name": "7323978621", "Vocab": []}, {"Name": "7346452425", "Vocab": []}, {"Name": "7369359411", "Vocab": []}, {"Name": "7379404660", "Vocab": []}, {"Name": "7381267353", "Vocab": []}, {"Name": "7411491836", "Vocab": []}, {"Name": "7416135290", "Vocab": []}, {"Name": "7416235283", "Vocab": []}, {"Name": "7416309115", "Vocab": []}, {"Name": "7436615757", "Vocab": []}, {"Name": "7467818420", "Vocab": []}, {"Name": "7475298436", "Vocab": []}, {"Name": "7475480440", "Vocab": []}, {"Name": "7475646204", "Vocab": []}, {"Name": "7475912087", "Vocab": []}, {"Name": "7484570139", "Vocab": []}, {"Name": "7489137920", "Vocab": []}, {"Name": "7499889892", "Vocab": []}, {"Name": "7505896109", "Vocab": []}, {"Name": "7519090844", "Vocab": []}, {"Name": "7532959044", "Vocab": []}, {"Name": "7546350356", "Vocab": []}, {"Name": "7584430416", "Vocab": []}, {"Name": "7606880874", "Vocab": []}, {"Name": "7624394971", "Vocab": []}, {"Name": "7654370367", "Vocab": []}, {"Name": "7695567452", "Vocab": []}, {"Name": "7696507153", "Vocab": []}, {"Name": "7710505857", "Vocab": []}, {"Name": "7711131635", "Vocab": []}, {"Name": "7752952620", "Vocab": []}, {"Name": "7760439505", "Vocab": []}, {"Name": "7768525324", "Vocab": []}, {"Name": "7772436389", "Vocab": []}, {"Name": "7780186058", "Vocab": []}, {"Name": "7784830632", "Vocab": []}, {"Name": "7797386229", "Vocab": []}, {"Name": "7801638958", "Vocab": []}, {"Name": "7804532585", "Vocab": []}, {"Name": "7805541204", "Vocab": []}, {"Name": "7807675765", "Vocab": []}, {"Name": "7810285625", "Vocab": []}, {"Name": "7819000880", "Vocab": []}, {"Name": "7841988717", "Vocab": []}, {"Name": "7857107867", "Vocab": []}, {"Name": "7881706579", "Vocab": []}, {"Name": "7890684966", "Vocab": []}, {"Name": "7897211163", "Vocab": []}, {"Name": "7907870780", "Vocab": []}, {"Name": "7914449914", "Vocab": []}, {"Name": "7936642453", "Vocab": []}, {"Name": "7936777798", "Vocab": []}, {"Name": "7939615886", "Vocab": []}, {"Name": "7942850802", "Vocab": []}, {"Name": "7957132334", "Vocab": []}, {"Name": "7963909487", "Vocab": []}, {"Name": "7978387195", "Vocab": []}, {"Name": "7989148817", "Vocab": []}, {"Name": "7993910857", "Vocab": []}, {"Name": "7996313662", "Vocab": []}, {"Name": "8020434150", "Vocab": []}, {"Name": "8029141148", "Vocab": []}, {"Name": "8034842161", "Vocab": []}, {"Name": "8038624009", "Vocab": []}, {"Name": "8048616224", "Vocab": []}, {"Name": "8053841676", "Vocab": []}, {"Name": "8104279059", "Vocab": []}, {"Name": "8131901230", "Vocab": []}, {"Name": "8138594488", "Vocab": []}, {"Name": "8146201602", "Vocab": []}, {"Name": "8159029733", "Vocab": []}, {"Name": "8159969962", "Vocab": []}, {"Name": "8166024374", "Vocab": []}, {"Name": "8173663028", "Vocab": []}, {"Name": "8182680028", "Vocab": []}, {"Name": "8188180176", "Vocab": []}, {"Name": "8188998311", "Vocab": []}, {"Name": "8197727160", "Vocab": []}, {"Name": "8209840352", "Vocab": []}, {"Name": "8219133234", "Vocab": []}, {"Name": "8232074551", "Vocab": []}, {"Name": "8237681979", "Vocab": []}, {"Name": "8246569992", "Vocab": []}, {"Name": "8259799403", "Vocab": []}, {"Name": "8264378133", "Vocab": []}, {"Name": "8273565397", "Vocab": []}, {"Name": "8277222230", "Vocab": []}, {"Name": "8277342262", "Vocab": []}, {"Name": "8286137719", "Vocab": []}, {"Name": "8293018937", "Vocab": []}, {"Name": "8295208113", "Vocab": []}, {"Name": "8347627887", "Vocab": []}, {"Name": "8351634455", "Vocab": []}, {"Name": "8372331285", "Vocab": []}, {"Name": "8376415933", "Vocab": []}, {"Name": "8379180221", "Vocab": []}, {"Name": "8390247553", "Vocab": []}, {"Name": "8390795901", "Vocab": []}, {"Name": "8393110063", "Vocab": []}, {"Name": "8416957777", "Vocab": []}, {"Name": "8442179578", "Vocab": []}, {"Name": "8442424030", "Vocab": []}, {"Name": "8452556404", "Vocab": []}, {"Name": "8454455740", "Vocab": []}, {"Name": "8456676749", "Vocab": []}, {"Name": "8480717137", "Vocab": []}, {"Name": "8484802250", "Vocab": []}, {"Name": "8490277682", "Vocab": []}, {"Name": "8495609964", "Vocab": []}, {"Name": "8510379427", "Vocab": []}, {"Name": "8513220683", "Vocab": []}, {"Name": "8513588907", "Vocab": []}, {"Name": "8542357502", "Vocab": []}, {"Name": "8548532184", "Vocab": []}, {"Name": "8552587315", "Vocab": []}, {"Name": "8557801563", "Vocab": []}, {"Name": "8560194027", "Vocab": []}, {"Name": "8570696528", "Vocab": []}, {"Name": "8572090236", "Vocab": []}, {"Name": "8574044767", "Vocab": []}, {"Name": "8579457256", "Vocab": []}, {"Name": "8581702772", "Vocab": []}, {"Name": "8594224216", "Vocab": []}, {"Name": "8599593750", "Vocab": []}, {"Name": "8647675157", "Vocab": []}, {"Name": "8657761057", "Vocab": []}, {"Name": "8666167285", "Vocab": []}, {"Name": "8666532541", "Vocab": []}, {"Name": "8690422156", "Vocab": []}, {"Name": "8702989486", "Vocab": []}, {"Name": "8714913829", "Vocab": []}, {"Name": "8717502046", "Vocab": []}, {"Name": "8732696853", "Vocab": []}, {"Name": "8740422603", "Vocab": []}, {"Name": "8742535597", "Vocab": []}, {"Name": "8753528721", "Vocab": []}, {"Name": "8760695408", "Vocab": []}, {"Name": "8763679285", "Vocab": []}, {"Name": "8764399440", "Vocab": []}, {"Name": "8767793791", "Vocab": []}, {"Name": "8779065447", "Vocab": []}, {"Name": "8783745008", "Vocab": []}, {"Name": "8806766339", "Vocab": []}, {"Name": "8810182996", "Vocab": []}, {"Name": "8824973349", "Vocab": []}, {"Name": "8839023710", "Vocab": []}, {"Name": "8844413548", "Vocab": []}, {"Name": "8848757353", "Vocab": []}, {"Name": "8851024793", "Vocab": []}, {"Name": "8862216824", "Vocab": []}, {"Name": "8866131280", "Vocab": []}, {"Name": "8881289749", "Vocab": []}, {"Name": "8912277214", "Vocab": []}, {"Name": "8919755410", "Vocab": []}, {"Name": "8921861517", "Vocab": []}, {"Name": "8931884345", "Vocab": []}, {"Name": "8981081459", "Vocab": []}, {"Name": "8986917227", "Vocab": []}, {"Name": "8990137304", "Vocab": []}, {"Name": "9037920855", "Vocab": []}, {"Name": "9046392169", "Vocab": []}, {"Name": "9055157129", "Vocab": []}, {"Name": "9073532153", "Vocab": []}, {"Name": "9079954745", "Vocab": []}, {"Name": "9086929457", "Vocab": []}, {"Name": "9106403137", "Vocab": []}, {"Name": "9109728585", "Vocab": []}, {"Name": "9110079736", "Vocab": []}, {"Name": "9110707316", "Vocab": []}, {"Name": "9117682203", "Vocab": []}, {"Name": "9121052823", "Vocab": []}, {"Name": "9127303010", "Vocab": []}, {"Name": "9149768242", "Vocab": []}, {"Name": "9177900142", "Vocab": []}, {"Name": "9180988352", "Vocab": []}, {"Name": "9183524228", "Vocab": []}, {"Name": "9211507508", "Vocab": []}, {"Name": "9214925833", "Vocab": []}, {"Name": "9217762292", "Vocab": []}, {"Name": "9226578692", "Vocab": []}, {"Name": "9227101738", "Vocab": []}, {"Name": "9228020066", "Vocab": []}, {"Name": "9234820777", "Vocab": []}, {"Name": "9236344675", "Vocab": []}, {"Name": "9236553331", "Vocab": []}, {"Name": "9241406927", "Vocab": []}, {"Name": "9281356984", "Vocab": []}, {"Name": "9283590898", "Vocab": []}, {"Name": "9285791968", "Vocab": []}, {"Name": "9296915790", "Vocab": []}, {"Name": "9311311293", "Vocab": []}, {"Name": "9316126643", "Vocab": []}, {"Name": "9316915109", "Vocab": []}, {"Name": "9325645173", "Vocab": []}, {"Name": "9333407514", "Vocab": []}, {"Name": "9348572041", "Vocab": []}, {"Name": "9350173847", "Vocab": []}, {"Name": "9366887933", "Vocab": []}, {"Name": "9391899283", "Vocab": []}, {"Name": "9415371962", "Vocab": []}, {"Name": "9427881613", "Vocab": []}, {"Name": "9475198490", "Vocab": []}, {"Name": "9491041451", "Vocab": []}, {"Name": "9495966974", "Vocab": []}, {"Name": "9496112779", "Vocab": []}, {"Name": "9503828743", "Vocab": []}, {"Name": "9516067586", "Vocab": []}, {"Name": "9516420240", "Vocab": []}, {"Name": "9520834587", "Vocab": []}, {"Name": "9551239668", "Vocab": []}, {"Name": "9558282086", "Vocab": []}, {"Name": "9567144242", "Vocab": []}, {"Name": "9575512075", "Vocab": []}, {"Name": "9577700136", "Vocab": []}, {"Name": "9598426209", "Vocab": []}, {"Name": "9598940619", "Vocab": []}, {"Name": "9603127810", "Vocab": []}, {"Name": "9607874126", "Vocab": []}, {"Name": "9618564551", "Vocab": []}, {"Name": "9637745640", "Vocab": []}, {"Name": "9644376322", "Vocab": []}, {"Name": "9665761756", "Vocab": []}, {"Name": "9666146014", "Vocab": []}, {"Name": "9669706359", "Vocab": []}, {"Name": "9680677099", "Vocab": []}, {"Name": "9693167225", "Vocab": []}, {"Name": "9705256663", "Vocab": []}, {"Name": "9727747380", "Vocab": []}, {"Name": "9733119557", "Vocab": []}, {"Name": "9750557259", "Vocab": []}, {"Name": "9757924695", "Vocab": []}, {"Name": "9765351823", "Vocab": []}, {"Name": "9766453111", "Vocab": []}, {"Name": "9780372326", "Vocab": []}, {"Name": "9804212423", "Vocab": []}, {"Name": "9809961717", "Vocab": []}, {"Name": "9810634715", "Vocab": []}, {"Name": "9820999803", "Vocab": []}, {"Name": "9823263596", "Vocab": []}, {"Name": "9837062236", "Vocab": []}, {"Name": "9838072863", "Vocab": []}, {"Name": "9851389719", "Vocab": []}, {"Name": "9853064328", "Vocab": []}, {"Name": "9870587319", "Vocab": []}, {"Name": "9873672495", "Vocab": []}, {"Name": "9884499718", "Vocab": []}, {"Name": "9889604315", "Vocab": []}, {"Name": "9902246487", "Vocab": []}, {"Name": "9909343167", "Vocab": []}, {"Name": "9910808940", "Vocab": []}, {"Name": "9913743679", "Vocab": []}, {"Name": "9923326363", "Vocab": []}, {"Name": "9942893069", "Vocab": []}, {"Name": "9943033016", "Vocab": []}, {"Name": "9951556603", "Vocab": []}, {"Name": "9960248774", "Vocab": []}, {"Name": "9961058606", "Vocab": []}, {"Name": "9965627579", "Vocab": []}, {"Name": "9970949083", "Vocab": []}, {"Name": "9987832960", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "LG1505_DSGCollectPIN_DM_dtmf (Complex)", "Grammars": ["LG1505_DSGCollectPIN_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "000518", "Vocab": []}, {"Name": "001424", "Vocab": []}, {"Name": "002271", "Vocab": []}, {"Name": "003569", "Vocab": []}, {"Name": "005055", "Vocab": []}, {"Name": "005419", "Vocab": []}, {"Name": "005693", "Vocab": []}, {"Name": "013931", "Vocab": []}, {"Name": "015646", "Vocab": []}, {"Name": "017569", "Vocab": []}, {"Name": "021789", "Vocab": []}, {"Name": "022658", "Vocab": []}, {"Name": "023151", "Vocab": []}, {"Name": "025724", "Vocab": []}, {"Name": "026236", "Vocab": []}, {"Name": "036159", "Vocab": []}, {"Name": "036279", "Vocab": []}, {"Name": "038229", "Vocab": []}, {"Name": "039260", "Vocab": []}, {"Name": "040960", "Vocab": []}, {"Name": "043614", "Vocab": []}, {"Name": "045188", "Vocab": []}, {"Name": "046496", "Vocab": []}, {"Name": "050277", "Vocab": []}, {"Name": "051171", "Vocab": []}, {"Name": "054949", "Vocab": []}, {"Name": "055017", "Vocab": []}, {"Name": "061113", "Vocab": []}, {"Name": "061780", "Vocab": []}, {"Name": "064054", "Vocab": []}, {"Name": "065233", "Vocab": []}, {"Name": "067741", "Vocab": []}, {"Name": "072054", "Vocab": []}, {"Name": "073088", "Vocab": []}, {"Name": "073285", "Vocab": []}, {"Name": "077725", "Vocab": []}, {"Name": "078292", "Vocab": []}, {"Name": "078766", "Vocab": []}, {"Name": "079260", "Vocab": []}, {"Name": "079548", "Vocab": []}, {"Name": "084540", "Vocab": []}, {"Name": "085054", "Vocab": []}, {"Name": "087244", "Vocab": []}, {"Name": "089265", "Vocab": []}, {"Name": "089814", "Vocab": []}, {"Name": "096426", "Vocab": []}, {"Name": "096700", "Vocab": []}, {"Name": "097819", "Vocab": []}, {"Name": "098130", "Vocab": []}, {"Name": "100407", "Vocab": []}, {"Name": "103925", "Vocab": []}, {"Name": "104011", "Vocab": []}, {"Name": "107454", "Vocab": []}, {"Name": "107659", "Vocab": []}, {"Name": "107811", "Vocab": []}, {"Name": "110883", "Vocab": []}, {"Name": "113498", "Vocab": []}, {"Name": "113715", "Vocab": []}, {"Name": "114599", "Vocab": []}, {"Name": "115064", "Vocab": []}, {"Name": "116467", "Vocab": []}, {"Name": "117978", "Vocab": []}, {"Name": "118975", "Vocab": []}, {"Name": "124587", "Vocab": []}, {"Name": "124591", "Vocab": []}, {"Name": "127780", "Vocab": []}, {"Name": "128102", "Vocab": []}, {"Name": "130059", "Vocab": []}, {"Name": "134503", "Vocab": []}, {"Name": "136480", "Vocab": []}, {"Name": "148264", "Vocab": []}, {"Name": "148693", "Vocab": []}, {"Name": "148763", "Vocab": []}, {"Name": "150791", "Vocab": []}, {"Name": "153342", "Vocab": []}, {"Name": "154592", "Vocab": []}, {"Name": "155009", "Vocab": []}, {"Name": "157670", "Vocab": []}, {"Name": "158417", "Vocab": []}, {"Name": "158438", "Vocab": []}, {"Name": "159098", "Vocab": []}, {"Name": "159801", "Vocab": []}, {"Name": "160594", "Vocab": []}, {"Name": "162631", "Vocab": []}, {"Name": "162821", "Vocab": []}, {"Name": "166878", "Vocab": []}, {"Name": "169977", "Vocab": []}, {"Name": "170595", "Vocab": []}, {"Name": "172029", "Vocab": []}, {"Name": "175877", "Vocab": []}, {"Name": "176191", "Vocab": []}, {"Name": "177008", "Vocab": []}, {"Name": "180372", "Vocab": []}, {"Name": "181258", "Vocab": []}, {"Name": "181948", "Vocab": []}, {"Name": "184187", "Vocab": []}, {"Name": "185181", "Vocab": []}, {"Name": "185424", "Vocab": []}, {"Name": "192377", "Vocab": []}, {"Name": "195341", "Vocab": []}, {"Name": "198643", "Vocab": []}, {"Name": "200744", "Vocab": []}, {"Name": "200753", "Vocab": []}, {"Name": "201348", "Vocab": []}, {"Name": "203662", "Vocab": []}, {"Name": "204168", "Vocab": []}, {"Name": "206407", "Vocab": []}, {"Name": "206807", "Vocab": []}, {"Name": "208217", "Vocab": []}, {"Name": "208380", "Vocab": []}, {"Name": "208565", "Vocab": []}, {"Name": "215654", "Vocab": []}, {"Name": "215911", "Vocab": []}, {"Name": "216089", "Vocab": []}, {"Name": "217376", "Vocab": []}, {"Name": "219386", "Vocab": []}, {"Name": "221745", "Vocab": []}, {"Name": "222962", "Vocab": []}, {"Name": "224703", "Vocab": []}, {"Name": "226086", "Vocab": []}, {"Name": "226712", "Vocab": []}, {"Name": "227111", "Vocab": []}, {"Name": "227230", "Vocab": []}, {"Name": "227528", "Vocab": []}, {"Name": "229774", "Vocab": []}, {"Name": "230959", "Vocab": []}, {"Name": "233818", "Vocab": []}, {"Name": "233831", "Vocab": []}, {"Name": "239403", "Vocab": []}, {"Name": "241819", "Vocab": []}, {"Name": "242344", "Vocab": []}, {"Name": "243587", "Vocab": []}, {"Name": "245396", "Vocab": []}, {"Name": "246624", "Vocab": []}, {"Name": "247011", "Vocab": []}, {"Name": "248153", "Vocab": []}, {"Name": "250623", "Vocab": []}, {"Name": "252746", "Vocab": []}, {"Name": "256548", "Vocab": []}, {"Name": "257076", "Vocab": []}, {"Name": "257753", "Vocab": []}, {"Name": "260669", "Vocab": []}, {"Name": "263495", "Vocab": []}, {"Name": "269063", "Vocab": []}, {"Name": "269227", "Vocab": []}, {"Name": "269670", "Vocab": []}, {"Name": "274737", "Vocab": []}, {"Name": "275373", "Vocab": []}, {"Name": "278768", "Vocab": []}, {"Name": "280302", "Vocab": []}, {"Name": "280680", "Vocab": []}, {"Name": "284220", "Vocab": []}, {"Name": "289638", "Vocab": []}, {"Name": "292979", "Vocab": []}, {"Name": "300833", "Vocab": []}, {"Name": "304181", "Vocab": []}, {"Name": "305726", "Vocab": []}, {"Name": "308679", "Vocab": []}, {"Name": "309271", "Vocab": []}, {"Name": "311220", "Vocab": []}, {"Name": "312338", "Vocab": []}, {"Name": "312717", "Vocab": []}, {"Name": "313822", "Vocab": []}, {"Name": "314664", "Vocab": []}, {"Name": "315065", "Vocab": []}, {"Name": "315867", "Vocab": []}, {"Name": "317826", "Vocab": []}, {"Name": "322131", "Vocab": []}, {"Name": "323038", "Vocab": []}, {"Name": "324262", "Vocab": []}, {"Name": "327176", "Vocab": []}, {"Name": "328510", "Vocab": []}, {"Name": "330010", "Vocab": []}, {"Name": "332072", "Vocab": []}, {"Name": "333760", "Vocab": []}, {"Name": "336364", "Vocab": []}, {"Name": "337675", "Vocab": []}, {"Name": "339246", "Vocab": []}, {"Name": "341323", "Vocab": []}, {"Name": "341489", "Vocab": []}, {"Name": "341795", "Vocab": []}, {"Name": "342686", "Vocab": []}, {"Name": "343341", "Vocab": []}, {"Name": "348377", "Vocab": []}, {"Name": "348579", "Vocab": []}, {"Name": "349843", "Vocab": []}, {"Name": "354330", "Vocab": []}, {"Name": "355300", "Vocab": []}, {"Name": "358151", "Vocab": []}, {"Name": "359249", "Vocab": []}, {"Name": "361061", "Vocab": []}, {"Name": "361700", "Vocab": []}, {"Name": "363056", "Vocab": []}, {"Name": "363490", "Vocab": []}, {"Name": "363586", "Vocab": []}, {"Name": "365838", "Vocab": []}, {"Name": "368002", "Vocab": []}, {"Name": "369887", "Vocab": []}, {"Name": "371660", "Vocab": []}, {"Name": "376972", "Vocab": []}, {"Name": "377556", "Vocab": []}, {"Name": "378710", "Vocab": []}, {"Name": "380422", "Vocab": []}, {"Name": "381612", "Vocab": []}, {"Name": "381811", "Vocab": []}, {"Name": "383391", "Vocab": []}, {"Name": "384525", "Vocab": []}, {"Name": "385382", "Vocab": []}, {"Name": "388134", "Vocab": []}, {"Name": "390246", "Vocab": []}, {"Name": "392782", "Vocab": []}, {"Name": "392841", "Vocab": []}, {"Name": "394204", "Vocab": []}, {"Name": "399850", "Vocab": []}, {"Name": "404130", "Vocab": []}, {"Name": "404420", "Vocab": []}, {"Name": "412423", "Vocab": []}, {"Name": "416141", "Vocab": []}, {"Name": "420313", "Vocab": []}, {"Name": "424314", "Vocab": []}, {"Name": "426696", "Vocab": []}, {"Name": "427496", "Vocab": []}, {"Name": "427937", "Vocab": []}, {"Name": "431939", "Vocab": []}, {"Name": "432643", "Vocab": []}, {"Name": "432782", "Vocab": []}, {"Name": "434328", "Vocab": []}, {"Name": "436799", "Vocab": []}, {"Name": "440008", "Vocab": []}, {"Name": "441278", "Vocab": []}, {"Name": "444089", "Vocab": []}, {"Name": "445594", "Vocab": []}, {"Name": "445873", "Vocab": []}, {"Name": "446001", "Vocab": []}, {"Name": "448980", "Vocab": []}, {"Name": "450225", "Vocab": []}, {"Name": "453729", "Vocab": []}, {"Name": "453746", "Vocab": []}, {"Name": "454313", "Vocab": []}, {"Name": "456684", "Vocab": []}, {"Name": "457807", "Vocab": []}, {"Name": "458379", "Vocab": []}, {"Name": "458465", "Vocab": []}, {"Name": "459386", "Vocab": []}, {"Name": "460019", "Vocab": []}, {"Name": "463428", "Vocab": []}, {"Name": "466510", "Vocab": []}, {"Name": "469051", "Vocab": []}, {"Name": "481111", "Vocab": []}, {"Name": "482585", "Vocab": []}, {"Name": "485622", "Vocab": []}, {"Name": "487976", "Vocab": []}, {"Name": "489843", "Vocab": []}, {"Name": "491398", "Vocab": []}, {"Name": "491982", "Vocab": []}, {"Name": "496111", "Vocab": []}, {"Name": "499630", "Vocab": []}, {"Name": "503221", "Vocab": []}, {"Name": "504521", "Vocab": []}, {"Name": "505295", "Vocab": []}, {"Name": "509778", "Vocab": []}, {"Name": "509792", "Vocab": []}, {"Name": "511687", "Vocab": []}, {"Name": "511725", "Vocab": []}, {"Name": "513580", "Vocab": []}, {"Name": "514005", "Vocab": []}, {"Name": "515750", "Vocab": []}, {"Name": "516113", "Vocab": []}, {"Name": "516625", "Vocab": []}, {"Name": "522257", "Vocab": []}, {"Name": "522305", "Vocab": []}, {"Name": "523921", "Vocab": []}, {"Name": "530463", "Vocab": []}, {"Name": "530888", "Vocab": []}, {"Name": "534125", "Vocab": []}, {"Name": "534211", "Vocab": []}, {"Name": "535722", "Vocab": []}, {"Name": "536182", "Vocab": []}, {"Name": "537937", "Vocab": []}, {"Name": "538142", "Vocab": []}, {"Name": "544940", "Vocab": []}, {"Name": "546888", "Vocab": []}, {"Name": "546898", "Vocab": []}, {"Name": "549526", "Vocab": []}, {"Name": "563560", "Vocab": []}, {"Name": "570872", "Vocab": []}, {"Name": "571676", "Vocab": []}, {"Name": "572642", "Vocab": []}, {"Name": "574601", "Vocab": []}, {"Name": "577547", "Vocab": []}, {"Name": "578405", "Vocab": []}, {"Name": "579195", "Vocab": []}, {"Name": "582887", "Vocab": []}, {"Name": "584570", "Vocab": []}, {"Name": "586383", "Vocab": []}, {"Name": "589635", "Vocab": []}, {"Name": "589793", "Vocab": []}, {"Name": "590972", "Vocab": []}, {"Name": "593197", "Vocab": []}, {"Name": "598047", "Vocab": []}, {"Name": "598107", "Vocab": []}, {"Name": "600448", "Vocab": []}, {"Name": "605268", "Vocab": []}, {"Name": "609535", "Vocab": []}, {"Name": "611701", "Vocab": []}, {"Name": "613131", "Vocab": []}, {"Name": "614842", "Vocab": []}, {"Name": "615652", "Vocab": []}, {"Name": "616736", "Vocab": []}, {"Name": "617010", "Vocab": []}, {"Name": "618737", "Vocab": []}, {"Name": "619033", "Vocab": []}, {"Name": "622277", "Vocab": []}, {"Name": "623665", "Vocab": []}, {"Name": "626662", "Vocab": []}, {"Name": "629913", "Vocab": []}, {"Name": "631648", "Vocab": []}, {"Name": "632191", "Vocab": []}, {"Name": "633289", "Vocab": []}, {"Name": "635383", "Vocab": []}, {"Name": "638296", "Vocab": []}, {"Name": "642390", "Vocab": []}, {"Name": "642803", "Vocab": []}, {"Name": "646793", "Vocab": []}, {"Name": "647323", "Vocab": []}, {"Name": "650506", "Vocab": []}, {"Name": "651042", "Vocab": []}, {"Name": "656304", "Vocab": []}, {"Name": "658052", "Vocab": []}, {"Name": "658326", "Vocab": []}, {"Name": "660255", "Vocab": []}, {"Name": "666782", "Vocab": []}, {"Name": "667755", "Vocab": []}, {"Name": "668353", "Vocab": []}, {"Name": "668975", "Vocab": []}, {"Name": "669049", "Vocab": []}, {"Name": "669299", "Vocab": []}, {"Name": "670283", "Vocab": []}, {"Name": "670319", "Vocab": []}, {"Name": "670778", "Vocab": []}, {"Name": "671294", "Vocab": []}, {"Name": "673808", "Vocab": []}, {"Name": "674297", "Vocab": []}, {"Name": "675839", "Vocab": []}, {"Name": "678764", "Vocab": []}, {"Name": "678794", "Vocab": []}, {"Name": "678984", "Vocab": []}, {"Name": "681451", "Vocab": []}, {"Name": "688244", "Vocab": []}, {"Name": "688818", "Vocab": []}, {"Name": "689838", "Vocab": []}, {"Name": "690414", "Vocab": []}, {"Name": "693612", "Vocab": []}, {"Name": "694475", "Vocab": []}, {"Name": "694904", "Vocab": []}, {"Name": "698274", "Vocab": []}, {"Name": "699310", "Vocab": []}, {"Name": "701746", "Vocab": []}, {"Name": "702924", "Vocab": []}, {"Name": "704562", "Vocab": []}, {"Name": "714653", "Vocab": []}, {"Name": "715140", "Vocab": []}, {"Name": "719695", "Vocab": []}, {"Name": "721202", "Vocab": []}, {"Name": "722939", "Vocab": []}, {"Name": "723709", "Vocab": []}, {"Name": "724021", "Vocab": []}, {"Name": "725502", "Vocab": []}, {"Name": "725759", "Vocab": []}, {"Name": "726687", "Vocab": []}, {"Name": "727319", "Vocab": []}, {"Name": "729301", "Vocab": []}, {"Name": "730095", "Vocab": []}, {"Name": "730365", "Vocab": []}, {"Name": "731408", "Vocab": []}, {"Name": "732197", "Vocab": []}, {"Name": "737470", "Vocab": []}, {"Name": "739187", "Vocab": []}, {"Name": "740004", "Vocab": []}, {"Name": "740140", "Vocab": []}, {"Name": "740765", "Vocab": []}, {"Name": "741133", "Vocab": []}, {"Name": "741136", "Vocab": []}, {"Name": "753895", "Vocab": []}, {"Name": "755840", "Vocab": []}, {"Name": "758531", "Vocab": []}, {"Name": "764750", "Vocab": []}, {"Name": "765312", "Vocab": []}, {"Name": "766624", "Vocab": []}, {"Name": "768567", "Vocab": []}, {"Name": "770092", "Vocab": []}, {"Name": "770690", "Vocab": []}, {"Name": "771612", "Vocab": []}, {"Name": "772899", "Vocab": []}, {"Name": "773180", "Vocab": []}, {"Name": "773685", "Vocab": []}, {"Name": "773741", "Vocab": []}, {"Name": "775937", "Vocab": []}, {"Name": "780168", "Vocab": []}, {"Name": "785192", "Vocab": []}, {"Name": "786259", "Vocab": []}, {"Name": "787787", "Vocab": []}, {"Name": "789119", "Vocab": []}, {"Name": "790520", "Vocab": []}, {"Name": "790830", "Vocab": []}, {"Name": "797499", "Vocab": []}, {"Name": "801752", "Vocab": []}, {"Name": "804701", "Vocab": []}, {"Name": "808072", "Vocab": []}, {"Name": "809664", "Vocab": []}, {"Name": "813042", "Vocab": []}, {"Name": "817376", "Vocab": []}, {"Name": "819048", "Vocab": []}, {"Name": "822041", "Vocab": []}, {"Name": "826758", "Vocab": []}, {"Name": "835749", "Vocab": []}, {"Name": "835860", "Vocab": []}, {"Name": "838023", "Vocab": []}, {"Name": "839223", "Vocab": []}, {"Name": "841594", "Vocab": []}, {"Name": "842170", "Vocab": []}, {"Name": "844280", "Vocab": []}, {"Name": "845482", "Vocab": []}, {"Name": "846062", "Vocab": []}, {"Name": "847559", "Vocab": []}, {"Name": "848754", "Vocab": []}, {"Name": "849861", "Vocab": []}, {"Name": "853609", "Vocab": []}, {"Name": "853821", "Vocab": []}, {"Name": "857351", "Vocab": []}, {"Name": "859348", "Vocab": []}, {"Name": "860137", "Vocab": []}, {"Name": "862606", "Vocab": []}, {"Name": "862993", "Vocab": []}, {"Name": "863445", "Vocab": []}, {"Name": "866532", "Vocab": []}, {"Name": "866658", "Vocab": []}, {"Name": "868717", "Vocab": []}, {"Name": "868920", "Vocab": []}, {"Name": "870751", "Vocab": []}, {"Name": "871149", "Vocab": []}, {"Name": "872471", "Vocab": []}, {"Name": "872753", "Vocab": []}, {"Name": "874612", "Vocab": []}, {"Name": "878551", "Vocab": []}, {"Name": "883232", "Vocab": []}, {"Name": "884555", "Vocab": []}, {"Name": "889596", "Vocab": []}, {"Name": "892114", "Vocab": []}, {"Name": "892548", "Vocab": []}, {"Name": "894355", "Vocab": []}, {"Name": "894737", "Vocab": []}, {"Name": "897415", "Vocab": []}, {"Name": "906463", "Vocab": []}, {"Name": "907146", "Vocab": []}, {"Name": "908861", "Vocab": []}, {"Name": "912012", "Vocab": []}, {"Name": "913583", "Vocab": []}, {"Name": "917141", "Vocab": []}, {"Name": "917220", "Vocab": []}, {"Name": "918025", "Vocab": []}, {"Name": "921719", "Vocab": []}, {"Name": "922698", "Vocab": []}, {"Name": "926094", "Vocab": []}, {"Name": "927024", "Vocab": []}, {"Name": "928584", "Vocab": []}, {"Name": "936827", "Vocab": []}, {"Name": "937364", "Vocab": []}, {"Name": "938162", "Vocab": []}, {"Name": "939107", "Vocab": []}, {"Name": "942801", "Vocab": []}, {"Name": "945278", "Vocab": []}, {"Name": "945335", "Vocab": []}, {"Name": "945640", "Vocab": []}, {"Name": "945900", "Vocab": []}, {"Name": "946816", "Vocab": []}, {"Name": "947525", "Vocab": []}, {"Name": "952493", "Vocab": []}, {"Name": "957591", "Vocab": []}, {"Name": "963762", "Vocab": []}, {"Name": "967593", "Vocab": []}, {"Name": "970743", "Vocab": []}, {"Name": "973595", "Vocab": []}, {"Name": "976026", "Vocab": []}, {"Name": "979736", "Vocab": []}, {"Name": "980241", "Vocab": []}, {"Name": "984247", "Vocab": []}, {"Name": "987665", "Vocab": []}, {"Name": "991400", "Vocab": []}, {"Name": "991626", "Vocab": []}, {"Name": "992136", "Vocab": []}, {"Name": "992368", "Vocab": []}, {"Name": "994498", "Vocab": []}, {"Name": "0195422", "Vocab": []}, {"Name": "0230994", "Vocab": []}, {"Name": "0237982", "Vocab": []}, {"Name": "0254629", "Vocab": []}, {"Name": "0258127", "Vocab": []}, {"Name": "0288396", "Vocab": []}, {"Name": "0353653", "Vocab": []}, {"Name": "0410532", "Vocab": []}, {"Name": "0463833", "Vocab": []}, {"Name": "0532424", "Vocab": []}, {"Name": "0573321", "Vocab": []}, {"Name": "0616201", "Vocab": []}, {"Name": "0642336", "Vocab": []}, {"Name": "0688466", "Vocab": []}, {"Name": "0689667", "Vocab": []}, {"Name": "0690954", "Vocab": []}, {"Name": "0699161", "Vocab": []}, {"Name": "0782671", "Vocab": []}, {"Name": "0793566", "Vocab": []}, {"Name": "0878467", "Vocab": []}, {"Name": "0880909", "Vocab": []}, {"Name": "0893440", "Vocab": []}, {"Name": "0947439", "Vocab": []}, {"Name": "1004058", "Vocab": []}, {"Name": "1009007", "Vocab": []}, {"Name": "1094547", "Vocab": []}, {"Name": "1162650", "Vocab": []}, {"Name": "1165252", "Vocab": []}, {"Name": "1199463", "Vocab": []}, {"Name": "1203744", "Vocab": []}, {"Name": "1224952", "Vocab": []}, {"Name": "1248157", "Vocab": []}, {"Name": "1251015", "Vocab": []}, {"Name": "1254296", "Vocab": []}, {"Name": "1276273", "Vocab": []}, {"Name": "1418630", "Vocab": []}, {"Name": "1486830", "Vocab": []}, {"Name": "1505751", "Vocab": []}, {"Name": "1643823", "Vocab": []}, {"Name": "1654641", "Vocab": []}, {"Name": "1699359", "Vocab": []}, {"Name": "1722566", "Vocab": []}, {"Name": "1733888", "Vocab": []}, {"Name": "1747686", "Vocab": []}, {"Name": "1760277", "Vocab": []}, {"Name": "1768969", "Vocab": []}, {"Name": "1774304", "Vocab": []}, {"Name": "1802330", "Vocab": []}, {"Name": "1831205", "Vocab": []}, {"Name": "1918729", "Vocab": []}, {"Name": "2033320", "Vocab": []}, {"Name": "2055881", "Vocab": []}, {"Name": "2059745", "Vocab": []}, {"Name": "2065034", "Vocab": []}, {"Name": "2077875", "Vocab": []}, {"Name": "2108338", "Vocab": []}, {"Name": "2131792", "Vocab": []}, {"Name": "2164822", "Vocab": []}, {"Name": "2212286", "Vocab": []}, {"Name": "2224274", "Vocab": []}, {"Name": "2233345", "Vocab": []}, {"Name": "2239355", "Vocab": []}, {"Name": "2305315", "Vocab": []}, {"Name": "2393925", "Vocab": []}, {"Name": "2398741", "Vocab": []}, {"Name": "2417737", "Vocab": []}, {"Name": "2499152", "Vocab": []}, {"Name": "2526145", "Vocab": []}, {"Name": "2592683", "Vocab": []}, {"Name": "2686819", "Vocab": []}, {"Name": "2691320", "Vocab": []}, {"Name": "2721750", "Vocab": []}, {"Name": "2803024", "Vocab": []}, {"Name": "2824312", "Vocab": []}, {"Name": "2833860", "Vocab": []}, {"Name": "3037097", "Vocab": []}, {"Name": "3051203", "Vocab": []}, {"Name": "3108782", "Vocab": []}, {"Name": "3144102", "Vocab": []}, {"Name": "3183104", "Vocab": []}, {"Name": "3203620", "Vocab": []}, {"Name": "3207010", "Vocab": []}, {"Name": "3345372", "Vocab": []}, {"Name": "3358366", "Vocab": []}, {"Name": "3444153", "Vocab": []}, {"Name": "3464723", "Vocab": []}, {"Name": "3486774", "Vocab": []}, {"Name": "3521741", "Vocab": []}, {"Name": "3559878", "Vocab": []}, {"Name": "3665830", "Vocab": []}, {"Name": "3695767", "Vocab": []}, {"Name": "3906596", "Vocab": []}, {"Name": "3992409", "Vocab": []}, {"Name": "4004847", "Vocab": []}, {"Name": "4039684", "Vocab": []}, {"Name": "4152039", "Vocab": []}, {"Name": "4171804", "Vocab": []}, {"Name": "4229371", "Vocab": []}, {"Name": "4268023", "Vocab": []}, {"Name": "4300362", "Vocab": []}, {"Name": "4394119", "Vocab": []}, {"Name": "4397137", "Vocab": []}, {"Name": "4455417", "Vocab": []}, {"Name": "4487121", "Vocab": []}, {"Name": "4506319", "Vocab": []}, {"Name": "4555911", "Vocab": []}, {"Name": "4578757", "Vocab": []}, {"Name": "4595787", "Vocab": []}, {"Name": "4611968", "Vocab": []}, {"Name": "4615314", "Vocab": []}, {"Name": "4712985", "Vocab": []}, {"Name": "4770539", "Vocab": []}, {"Name": "4781040", "Vocab": []}, {"Name": "4920495", "Vocab": []}, {"Name": "4938482", "Vocab": []}, {"Name": "4938498", "Vocab": []}, {"Name": "4958388", "Vocab": []}, {"Name": "4978821", "Vocab": []}, {"Name": "5005863", "Vocab": []}, {"Name": "5010254", "Vocab": []}, {"Name": "5028461", "Vocab": []}, {"Name": "5049228", "Vocab": []}, {"Name": "5067446", "Vocab": []}, {"Name": "5081883", "Vocab": []}, {"Name": "5111008", "Vocab": []}, {"Name": "5152097", "Vocab": []}, {"Name": "5170743", "Vocab": []}, {"Name": "5239280", "Vocab": []}, {"Name": "5259038", "Vocab": []}, {"Name": "5289325", "Vocab": []}, {"Name": "5292973", "Vocab": []}, {"Name": "5331155", "Vocab": []}, {"Name": "5457779", "Vocab": []}, {"Name": "5464260", "Vocab": []}, {"Name": "5518551", "Vocab": []}, {"Name": "5539862", "Vocab": []}, {"Name": "5603977", "Vocab": []}, {"Name": "5605277", "Vocab": []}, {"Name": "5667101", "Vocab": []}, {"Name": "5676522", "Vocab": []}, {"Name": "5698916", "Vocab": []}, {"Name": "5756588", "Vocab": []}, {"Name": "5785929", "Vocab": []}, {"Name": "5875373", "Vocab": []}, {"Name": "5965175", "Vocab": []}, {"Name": "5979525", "Vocab": []}, {"Name": "5998308", "Vocab": []}, {"Name": "6132353", "Vocab": []}, {"Name": "6150730", "Vocab": []}, {"Name": "6208498", "Vocab": []}, {"Name": "6209430", "Vocab": []}, {"Name": "6229745", "Vocab": []}, {"Name": "6275828", "Vocab": []}, {"Name": "6291080", "Vocab": []}, {"Name": "6294848", "Vocab": []}, {"Name": "6336933", "Vocab": []}, {"Name": "6341840", "Vocab": []}, {"Name": "6349370", "Vocab": []}, {"Name": "6371275", "Vocab": []}, {"Name": "6375930", "Vocab": []}, {"Name": "6407032", "Vocab": []}, {"Name": "6422455", "Vocab": []}, {"Name": "6489789", "Vocab": []}, {"Name": "6494729", "Vocab": []}, {"Name": "6523121", "Vocab": []}, {"Name": "6582300", "Vocab": []}, {"Name": "6658827", "Vocab": []}, {"Name": "6660765", "Vocab": []}, {"Name": "6690309", "Vocab": []}, {"Name": "6708557", "Vocab": []}, {"Name": "6720185", "Vocab": []}, {"Name": "6784990", "Vocab": []}, {"Name": "6824364", "Vocab": []}, {"Name": "6850300", "Vocab": []}, {"Name": "6977561", "Vocab": []}, {"Name": "6995733", "Vocab": []}, {"Name": "7185881", "Vocab": []}, {"Name": "7243931", "Vocab": []}, {"Name": "7257148", "Vocab": []}, {"Name": "7271364", "Vocab": []}, {"Name": "7286509", "Vocab": []}, {"Name": "7402476", "Vocab": []}, {"Name": "7435430", "Vocab": []}, {"Name": "7446532", "Vocab": []}, {"Name": "7544214", "Vocab": []}, {"Name": "7549771", "Vocab": []}, {"Name": "7565117", "Vocab": []}, {"Name": "7596096", "Vocab": []}, {"Name": "7611148", "Vocab": []}, {"Name": "7668169", "Vocab": []}, {"Name": "7695340", "Vocab": []}, {"Name": "7700094", "Vocab": []}, {"Name": "7713393", "Vocab": []}, {"Name": "7771408", "Vocab": []}, {"Name": "7815325", "Vocab": []}, {"Name": "7849404", "Vocab": []}, {"Name": "7870623", "Vocab": []}, {"Name": "7923650", "Vocab": []}, {"Name": "7929309", "Vocab": []}, {"Name": "8106486", "Vocab": []}, {"Name": "8118244", "Vocab": []}, {"Name": "8150667", "Vocab": []}, {"Name": "8237918", "Vocab": []}, {"Name": "8283013", "Vocab": []}, {"Name": "8297033", "Vocab": []}, {"Name": "8298277", "Vocab": []}, {"Name": "8299780", "Vocab": []}, {"Name": "8305010", "Vocab": []}, {"Name": "8309041", "Vocab": []}, {"Name": "8325041", "Vocab": []}, {"Name": "8342324", "Vocab": []}, {"Name": "8461835", "Vocab": []}, {"Name": "8555874", "Vocab": []}, {"Name": "8621807", "Vocab": []}, {"Name": "8641906", "Vocab": []}, {"Name": "8645248", "Vocab": []}, {"Name": "8689521", "Vocab": []}, {"Name": "8754043", "Vocab": []}, {"Name": "8760082", "Vocab": []}, {"Name": "8796687", "Vocab": []}, {"Name": "8864335", "Vocab": []}, {"Name": "8920332", "Vocab": []}, {"Name": "8923193", "Vocab": []}, {"Name": "9033291", "Vocab": []}, {"Name": "9093853", "Vocab": []}, {"Name": "9102608", "Vocab": []}, {"Name": "9125346", "Vocab": []}, {"Name": "9125936", "Vocab": []}, {"Name": "9131939", "Vocab": []}, {"Name": "9132715", "Vocab": []}, {"Name": "9136002", "Vocab": []}, {"Name": "9203524", "Vocab": []}, {"Name": "9214962", "Vocab": []}, {"Name": "9233441", "Vocab": []}, {"Name": "9253676", "Vocab": []}, {"Name": "9303366", "Vocab": []}, {"Name": "9308376", "Vocab": []}, {"Name": "9314617", "Vocab": []}, {"Name": "9331894", "Vocab": []}, {"Name": "9376829", "Vocab": []}, {"Name": "9383914", "Vocab": []}, {"Name": "9391765", "Vocab": []}, {"Name": "9405193", "Vocab": []}, {"Name": "9423603", "Vocab": []}, {"Name": "9470267", "Vocab": []}, {"Name": "9537841", "Vocab": []}, {"Name": "9543448", "Vocab": []}, {"Name": "9564957", "Vocab": []}, {"Name": "9575570", "Vocab": []}, {"Name": "9609381", "Vocab": []}, {"Name": "9646331", "Vocab": []}, {"Name": "9657573", "Vocab": []}, {"Name": "9673960", "Vocab": []}, {"Name": "9716699", "Vocab": []}, {"Name": "9779425", "Vocab": []}, {"Name": "9804506", "Vocab": []}, {"Name": "00622431", "Vocab": []}, {"Name": "00646718", "Vocab": []}, {"Name": "00683012", "Vocab": []}, {"Name": "02469135", "Vocab": []}, {"Name": "03234291", "Vocab": []}, {"Name": "03400613", "Vocab": []}, {"Name": "04892955", "Vocab": []}, {"Name": "05449039", "Vocab": []}, {"Name": "05839346", "Vocab": []}, {"Name": "06261514", "Vocab": []}, {"Name": "06425424", "Vocab": []}, {"Name": "06778463", "Vocab": []}, {"Name": "08144962", "Vocab": []}, {"Name": "08280096", "Vocab": []}, {"Name": "08736183", "Vocab": []}, {"Name": "11614268", "Vocab": []}, {"Name": "11708656", "Vocab": []}, {"Name": "12236797", "Vocab": []}, {"Name": "12507584", "Vocab": []}, {"Name": "12573066", "Vocab": []}, {"Name": "12652799", "Vocab": []}, {"Name": "13629717", "Vocab": []}, {"Name": "14198962", "Vocab": []}, {"Name": "14415042", "Vocab": []}, {"Name": "14595089", "Vocab": []}, {"Name": "15620491", "Vocab": []}, {"Name": "16790660", "Vocab": []}, {"Name": "18593536", "Vocab": []}, {"Name": "23400436", "Vocab": []}, {"Name": "25427720", "Vocab": []}, {"Name": "26339825", "Vocab": []}, {"Name": "27672459", "Vocab": []}, {"Name": "28152171", "Vocab": []}, {"Name": "28933775", "Vocab": []}, {"Name": "29479262", "Vocab": []}, {"Name": "30421220", "Vocab": []}, {"Name": "31469664", "Vocab": []}, {"Name": "31716600", "Vocab": []}, {"Name": "31980550", "Vocab": []}, {"Name": "32376722", "Vocab": []}, {"Name": "32632431", "Vocab": []}, {"Name": "33653552", "Vocab": []}, {"Name": "34200296", "Vocab": []}, {"Name": "34334286", "Vocab": []}, {"Name": "34833302", "Vocab": []}, {"Name": "36420855", "Vocab": []}, {"Name": "36598120", "Vocab": []}, {"Name": "37039280", "Vocab": []}, {"Name": "37143128", "Vocab": []}, {"Name": "37550566", "Vocab": []}, {"Name": "37648145", "Vocab": []}, {"Name": "38235652", "Vocab": []}, {"Name": "38542196", "Vocab": []}, {"Name": "38709728", "Vocab": []}, {"Name": "40096451", "Vocab": []}, {"Name": "41903698", "Vocab": []}, {"Name": "41939670", "Vocab": []}, {"Name": "42230181", "Vocab": []}, {"Name": "45172192", "Vocab": []}, {"Name": "45491780", "Vocab": []}, {"Name": "46841958", "Vocab": []}, {"Name": "47620128", "Vocab": []}, {"Name": "47910197", "Vocab": []}, {"Name": "50412259", "Vocab": []}, {"Name": "50567495", "Vocab": []}, {"Name": "51045950", "Vocab": []}, {"Name": "51916073", "Vocab": []}, {"Name": "52357879", "Vocab": []}, {"Name": "54429097", "Vocab": []}, {"Name": "54476218", "Vocab": []}, {"Name": "55886374", "Vocab": []}, {"Name": "57856346", "Vocab": []}, {"Name": "58081770", "Vocab": []}, {"Name": "59968651", "Vocab": []}, {"Name": "60034496", "Vocab": []}, {"Name": "63387371", "Vocab": []}, {"Name": "64144735", "Vocab": []}, {"Name": "64153011", "Vocab": []}, {"Name": "65741288", "Vocab": []}, {"Name": "66235125", "Vocab": []}, {"Name": "67629931", "Vocab": []}, {"Name": "67896296", "Vocab": []}, {"Name": "68636688", "Vocab": []}, {"Name": "70568564", "Vocab": []}, {"Name": "71767388", "Vocab": []}, {"Name": "72130492", "Vocab": []}, {"Name": "73286530", "Vocab": []}, {"Name": "73806042", "Vocab": []}, {"Name": "73987438", "Vocab": []}, {"Name": "74165946", "Vocab": []}, {"Name": "74372012", "Vocab": []}, {"Name": "75123143", "Vocab": []}, {"Name": "75422253", "Vocab": []}, {"Name": "77016457", "Vocab": []}, {"Name": "77679764", "Vocab": []}, {"Name": "77736665", "Vocab": []}, {"Name": "77932431", "Vocab": []}, {"Name": "80315149", "Vocab": []}, {"Name": "80761407", "Vocab": []}, {"Name": "81701784", "Vocab": []}, {"Name": "82108736", "Vocab": []}, {"Name": "83266763", "Vocab": []}, {"Name": "84032131", "Vocab": []}, {"Name": "86195342", "Vocab": []}, {"Name": "86399781", "Vocab": []}, {"Name": "87343222", "Vocab": []}, {"Name": "89663899", "Vocab": []}, {"Name": "90976331", "Vocab": []}, {"Name": "91613134", "Vocab": []}, {"Name": "91710006", "Vocab": []}, {"Name": "92214097", "Vocab": []}, {"Name": "93193469", "Vocab": []}, {"Name": "94530606", "Vocab": []}, {"Name": "95141403", "Vocab": []}, {"Name": "96964917", "Vocab": []}, {"Name": "97449654", "Vocab": []}, {"Name": "98831619", "Vocab": []}, {"Name": "017880160", "Vocab": []}, {"Name": "019595021", "Vocab": []}, {"Name": "020145879", "Vocab": []}, {"Name": "045444947", "Vocab": []}, {"Name": "070619544", "Vocab": []}, {"Name": "072805504", "Vocab": []}, {"Name": "082847557", "Vocab": []}, {"Name": "093566409", "Vocab": []}, {"Name": "099918400", "Vocab": []}, {"Name": "102878695", "Vocab": []}, {"Name": "134867395", "Vocab": []}, {"Name": "152624017", "Vocab": []}, {"Name": "167728431", "Vocab": []}, {"Name": "177053304", "Vocab": []}, {"Name": "186984607", "Vocab": []}, {"Name": "201719726", "Vocab": []}, {"Name": "206811280", "Vocab": []}, {"Name": "212326235", "Vocab": []}, {"Name": "217833160", "Vocab": []}, {"Name": "223505087", "Vocab": []}, {"Name": "225651965", "Vocab": []}, {"Name": "237229812", "Vocab": []}, {"Name": "276547020", "Vocab": []}, {"Name": "277246514", "Vocab": []}, {"Name": "287817451", "Vocab": []}, {"Name": "312200420", "Vocab": []}, {"Name": "332401232", "Vocab": []}, {"Name": "339203601", "Vocab": []}, {"Name": "341959798", "Vocab": []}, {"Name": "351529996", "Vocab": []}, {"Name": "360612697", "Vocab": []}, {"Name": "372044769", "Vocab": []}, {"Name": "382486434", "Vocab": []}, {"Name": "382988347", "Vocab": []}, {"Name": "411531511", "Vocab": []}, {"Name": "433830060", "Vocab": []}, {"Name": "434889666", "Vocab": []}, {"Name": "435840255", "Vocab": []}, {"Name": "438190016", "Vocab": []}, {"Name": "438389077", "Vocab": []}, {"Name": "504637008", "Vocab": []}, {"Name": "518611329", "Vocab": []}, {"Name": "526067194", "Vocab": []}, {"Name": "528006613", "Vocab": []}, {"Name": "528059957", "Vocab": []}, {"Name": "569549710", "Vocab": []}, {"Name": "612421890", "Vocab": []}, {"Name": "623717719", "Vocab": []}, {"Name": "633305469", "Vocab": []}, {"Name": "646746489", "Vocab": []}, {"Name": "656464789", "Vocab": []}, {"Name": "674766356", "Vocab": []}, {"Name": "688238801", "Vocab": []}, {"Name": "709759496", "Vocab": []}, {"Name": "727130342", "Vocab": []}, {"Name": "739805253", "Vocab": []}, {"Name": "814266296", "Vocab": []}, {"Name": "841443634", "Vocab": []}, {"Name": "851691120", "Vocab": []}, {"Name": "865115115", "Vocab": []}, {"Name": "866598901", "Vocab": []}, {"Name": "867440658", "Vocab": []}, {"Name": "878556922", "Vocab": []}, {"Name": "891338773", "Vocab": []}, {"Name": "898514968", "Vocab": []}, {"Name": "917516501", "Vocab": []}, {"Name": "918465052", "Vocab": []}, {"Name": "955699244", "Vocab": []}, {"Name": "983660620", "Vocab": []}, {"Name": "983821909", "Vocab": []}, {"Name": "0105259028", "Vocab": []}, {"Name": "0125495382", "Vocab": []}, {"Name": "0234496912", "Vocab": []}, {"Name": "0414010316", "Vocab": []}, {"Name": "0792018106", "Vocab": []}, {"Name": "1171178059", "Vocab": []}, {"Name": "1472673229", "Vocab": []}, {"Name": "1994688437", "Vocab": []}, {"Name": "2432250096", "Vocab": []}, {"Name": "2497395965", "Vocab": []}, {"Name": "3414917255", "Vocab": []}, {"Name": "4182093801", "Vocab": []}, {"Name": "4215924451", "Vocab": []}, {"Name": "4634156606", "Vocab": []}, {"Name": "5018964184", "Vocab": []}, {"Name": "5464061584", "Vocab": []}, {"Name": "5916609143", "Vocab": []}, {"Name": "6189677507", "Vocab": []}, {"Name": "6337941794", "Vocab": []}, {"Name": "6945305450", "Vocab": []}, {"Name": "7063001171", "Vocab": []}, {"Name": "7091948154", "Vocab": []}, {"Name": "7203365010", "Vocab": []}, {"Name": "7560122908", "Vocab": []}, {"Name": "7998121446", "Vocab": []}, {"Name": "8136449992", "Vocab": []}, {"Name": "8698758655", "Vocab": []}, {"Name": "9305918627", "Vocab": []}, {"Name": "9329186698", "Vocab": []}, {"Name": "03612003149", "Vocab": []}, {"Name": "04688548901", "Vocab": []}, {"Name": "11318623057", "Vocab": []}, {"Name": "28869600250", "Vocab": []}, {"Name": "29483680405", "Vocab": []}, {"Name": "30720451836", "Vocab": []}, {"Name": "38054091372", "Vocab": []}, {"Name": "40900651114", "Vocab": []}, {"Name": "43738172154", "Vocab": []}, {"Name": "48542489156", "Vocab": []}, {"Name": "49838164680", "Vocab": []}, {"Name": "52074559061", "Vocab": []}, {"Name": "55472225440", "Vocab": []}, {"Name": "65381567549", "Vocab": []}, {"Name": "73097256035", "Vocab": []}, {"Name": "76636219256", "Vocab": []}, {"Name": "78802141307", "Vocab": []}, {"Name": "86939606334", "Vocab": []}, {"Name": "88051247867", "Vocab": []}, {"Name": "93529593115", "Vocab": []}, {"Name": "95188665069", "Vocab": []}, {"Name": "95890570412", "Vocab": []}, {"Name": "97087107697", "Vocab": []}, {"Name": "379127904663", "Vocab": []}, {"Name": "482965169361", "Vocab": []}, {"Name": "594878811998", "Vocab": []}, {"Name": "935667964270", "Vocab": []}, {"Name": "0863401951386", "Vocab": []}, {"Name": "2227458279078", "Vocab": []}, {"Name": "6948038864588", "Vocab": []}, {"Name": "7231389333728", "Vocab": []}, {"Name": "104483472453534", "Vocab": []}, {"Name": "***************", "Vocab": []}, {"Name": "***************", "Vocab": []}, {"Name": "***************", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "MM1020_MainMenuBTM_DM_dtmf", "Grammars": ["MM1020_MainMenuBTM_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "refill", "Vocab": []}, {"Name": "acct_bal", "Vocab": []}, {"Name": "plan_details", "Vocab": []}, {"Name": "set_up_phone", "Vocab": []}, {"Name": "more_options", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "ND1440_ChangeAccount_DM_dtmf", "Grammars": ["ND1440_ChangeAccount_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "1", "Vocab": []}, {"Name": "2", "Vocab": []}, {"Name": "3", "Vocab": []}, {"Name": "4", "Vocab": []}, {"Name": "5", "Vocab": []}, {"Name": "6", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "NLU_GlobalCommands-NL1005_dtmf", "Grammars": ["NLU_GlobalCommands-NL1005_dtmf.grxml"], "SWI_meaning": [{"Name": "request-spanish", "Vocab": []}, {"Name": "request-representative", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "NLU_GlobalCommandsNoRepeat_dtmf", "Grammars": ["NLU_GlobalCommandsNoRepeat_dtmf.grxml"], "SWI_meaning": [{"Name": "request-representative", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "NP1010_AskLanguage_DM_dtmf", "Grammars": ["NP1010_AskLanguage_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "Spanish", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "NR5005_NRHWelcome_DM_dtmf", "Grammars": ["NR5005_NRHWelcome_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "1", "Vocab": []}, {"Name": "2", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "PC1010_AskLanguage_DM_dtmf", "Grammars": ["PC1010_AskLanguage_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "Spanish", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "PL1010_AskLanguage_DM_dtmf", "Grammars": ["PL1010_AskLanguage_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "Spanish", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "PR1010_AskLanguage_DM_dtmf", "Grammars": ["PR1010_AskLanguage_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "Spanish", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "RC0115_CustomerOrCarrier_DM_dtmf", "Grammars": ["RC0115_CustomerOrCarrier_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "carrier", "Vocab": []}, {"Name": "spanish", "Vocab": []}, {"Name": "customer", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "TEST_ANIEntry_DM_dtmf (Complex)", "Grammars": ["TEST_ANIEntry_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "0099330533", "Vocab": []}, {"Name": "0663710566", "Vocab": []}, {"Name": "0921753541", "Vocab": []}, {"Name": "1038911763", "Vocab": []}, {"Name": "1413516277", "Vocab": []}, {"Name": "1600356599", "Vocab": []}, {"Name": "1805943801", "Vocab": []}, {"Name": "1977832171", "Vocab": []}, {"Name": "2106368360", "Vocab": []}, {"Name": "2466226142", "Vocab": []}, {"Name": "3338043640", "Vocab": []}, {"Name": "4045740400", "Vocab": []}, {"Name": "4357464883", "Vocab": []}, {"Name": "4540379139", "Vocab": []}, {"Name": "5518854076", "Vocab": []}, {"Name": "5719060640", "Vocab": []}, {"Name": "5765380854", "Vocab": []}, {"Name": "5771751946", "Vocab": []}, {"Name": "5924130488", "Vocab": []}, {"Name": "6078781012", "Vocab": []}, {"Name": "6119436274", "Vocab": []}, {"Name": "6277051649", "Vocab": []}, {"Name": "6450335637", "Vocab": []}, {"Name": "6633845462", "Vocab": []}, {"Name": "6790602438", "Vocab": []}, {"Name": "7449173982", "Vocab": []}, {"Name": "7508885445", "Vocab": []}, {"Name": "8155031932", "Vocab": []}, {"Name": "8231678466", "Vocab": []}, {"Name": "8264479648", "Vocab": []}, {"Name": "8832378288", "Vocab": []}, {"Name": "9374290139", "Vocab": []}, {"Name": "9678625512", "Vocab": []}, {"Name": "9836605466", "Vocab": []}, {"Name": "9856749123", "Vocab": []}, {"Name": "9917513128", "Vocab": []}, {"Name": "01159721560", "Vocab": []}, {"Name": "02368647218", "Vocab": []}, {"Name": "02393114058", "Vocab": []}, {"Name": "03590031026", "Vocab": []}, {"Name": "04346910985", "Vocab": []}, {"Name": "04704949401", "Vocab": []}, {"Name": "04768162308", "Vocab": []}, {"Name": "04834288492", "Vocab": []}, {"Name": "05095173055", "Vocab": []}, {"Name": "07813870484", "Vocab": []}, {"Name": "08184572730", "Vocab": []}, {"Name": "09035901836", "Vocab": []}, {"Name": "09999123622", "Vocab": []}, {"Name": "10693813455", "Vocab": []}, {"Name": "10869446939", "Vocab": []}, {"Name": "11305806939", "Vocab": []}, {"Name": "11438250930", "Vocab": []}, {"Name": "13437742738", "Vocab": []}, {"Name": "13836144683", "Vocab": []}, {"Name": "15217802316", "Vocab": []}, {"Name": "17154168366", "Vocab": []}, {"Name": "18572448685", "Vocab": []}, {"Name": "20019576937", "Vocab": []}, {"Name": "21018945217", "Vocab": []}, {"Name": "21593144321", "Vocab": []}, {"Name": "21721969791", "Vocab": []}, {"Name": "22036198128", "Vocab": []}, {"Name": "22455474495", "Vocab": []}, {"Name": "23453413239", "Vocab": []}, {"Name": "24591831328", "Vocab": []}, {"Name": "24958625579", "Vocab": []}, {"Name": "26743436812", "Vocab": []}, {"Name": "27011910225", "Vocab": []}, {"Name": "27234307960", "Vocab": []}, {"Name": "27810305779", "Vocab": []}, {"Name": "28943117161", "Vocab": []}, {"Name": "29197308504", "Vocab": []}, {"Name": "29386411120", "Vocab": []}, {"Name": "31277528286", "Vocab": []}, {"Name": "31811646925", "Vocab": []}, {"Name": "32260746952", "Vocab": []}, {"Name": "32892363556", "Vocab": []}, {"Name": "34082759921", "Vocab": []}, {"Name": "36831684946", "Vocab": []}, {"Name": "37425609682", "Vocab": []}, {"Name": "38814518915", "Vocab": []}, {"Name": "38946425981", "Vocab": []}, {"Name": "38954615069", "Vocab": []}, {"Name": "39265044647", "Vocab": []}, {"Name": "39671356305", "Vocab": []}, {"Name": "41278829706", "Vocab": []}, {"Name": "41659902672", "Vocab": []}, {"Name": "42814671615", "Vocab": []}, {"Name": "43586606911", "Vocab": []}, {"Name": "45070303867", "Vocab": []}, {"Name": "46668101109", "Vocab": []}, {"Name": "46751059249", "Vocab": []}, {"Name": "47086954294", "Vocab": []}, {"Name": "48357709765", "Vocab": []}, {"Name": "49222409431", "Vocab": []}, {"Name": "49348954770", "Vocab": []}, {"Name": "49554381911", "Vocab": []}, {"Name": "49716166378", "Vocab": []}, {"Name": "50259421276", "Vocab": []}, {"Name": "50828580939", "Vocab": []}, {"Name": "51290853826", "Vocab": []}, {"Name": "51415919621", "Vocab": []}, {"Name": "52585456160", "Vocab": []}, {"Name": "53112662803", "Vocab": []}, {"Name": "53871403460", "Vocab": []}, {"Name": "53919649299", "Vocab": []}, {"Name": "54144020352", "Vocab": []}, {"Name": "54211295242", "Vocab": []}, {"Name": "55362937577", "Vocab": []}, {"Name": "55516855593", "Vocab": []}, {"Name": "56102555521", "Vocab": []}, {"Name": "56305210269", "Vocab": []}, {"Name": "56523973059", "Vocab": []}, {"Name": "56903385972", "Vocab": []}, {"Name": "57672484818", "Vocab": []}, {"Name": "58639315781", "Vocab": []}, {"Name": "58829349606", "Vocab": []}, {"Name": "59790071321", "Vocab": []}, {"Name": "60765137947", "Vocab": []}, {"Name": "60854597638", "Vocab": []}, {"Name": "61692429777", "Vocab": []}, {"Name": "62008296745", "Vocab": []}, {"Name": "62138791861", "Vocab": []}, {"Name": "62816764567", "Vocab": []}, {"Name": "65234865611", "Vocab": []}, {"Name": "65806010087", "Vocab": []}, {"Name": "66099130385", "Vocab": []}, {"Name": "66266521410", "Vocab": []}, {"Name": "66410226625", "Vocab": []}, {"Name": "66606189953", "Vocab": []}, {"Name": "67041754114", "Vocab": []}, {"Name": "67602707206", "Vocab": []}, {"Name": "67811263023", "Vocab": []}, {"Name": "68017752910", "Vocab": []}, {"Name": "70217820423", "Vocab": []}, {"Name": "70745186387", "Vocab": []}, {"Name": "71043025647", "Vocab": []}, {"Name": "71309619285", "Vocab": []}, {"Name": "72016642536", "Vocab": []}, {"Name": "72414222343", "Vocab": []}, {"Name": "73130236522", "Vocab": []}, {"Name": "74613614000", "Vocab": []}, {"Name": "75099573169", "Vocab": []}, {"Name": "75104975553", "Vocab": []}, {"Name": "75556135828", "Vocab": []}, {"Name": "76724531818", "Vocab": []}, {"Name": "77232509305", "Vocab": []}, {"Name": "77637873229", "Vocab": []}, {"Name": "77917079929", "Vocab": []}, {"Name": "77946849034", "Vocab": []}, {"Name": "78189131889", "Vocab": []}, {"Name": "79369146353", "Vocab": []}, {"Name": "81146619949", "Vocab": []}, {"Name": "81370322089", "Vocab": []}, {"Name": "81804971814", "Vocab": []}, {"Name": "81913813177", "Vocab": []}, {"Name": "81993989935", "Vocab": []}, {"Name": "82015356942", "Vocab": []}, {"Name": "82578961307", "Vocab": []}, {"Name": "82967263929", "Vocab": []}, {"Name": "83974269003", "Vocab": []}, {"Name": "84819586426", "Vocab": []}, {"Name": "85370170821", "Vocab": []}, {"Name": "86614983007", "Vocab": []}, {"Name": "86693051572", "Vocab": []}, {"Name": "87370104121", "Vocab": []}, {"Name": "87390617907", "Vocab": []}, {"Name": "88039275289", "Vocab": []}, {"Name": "88555180696", "Vocab": []}, {"Name": "88667509724", "Vocab": []}, {"Name": "89416763624", "Vocab": []}, {"Name": "89573002603", "Vocab": []}, {"Name": "90389039107", "Vocab": []}, {"Name": "90420010491", "Vocab": []}, {"Name": "90587482757", "Vocab": []}, {"Name": "91255935229", "Vocab": []}, {"Name": "91866926373", "Vocab": []}, {"Name": "92000098409", "Vocab": []}, {"Name": "92868747429", "Vocab": []}, {"Name": "94276605850", "Vocab": []}, {"Name": "96015927405", "Vocab": []}, {"Name": "96753376824", "Vocab": []}, {"Name": "97073903736", "Vocab": []}, {"Name": "97934380140", "Vocab": []}, {"Name": "98739570786", "Vocab": []}, {"Name": "98931165173", "Vocab": []}, {"Name": "99804136426", "Vocab": []}, {"Name": "002090574730", "Vocab": []}, {"Name": "010245087213", "Vocab": []}, {"Name": "011121345568", "Vocab": []}, {"Name": "014946132568", "Vocab": []}, {"Name": "021570294437", "Vocab": []}, {"Name": "022081724833", "Vocab": []}, {"Name": "025882974769", "Vocab": []}, {"Name": "026949369869", "Vocab": []}, {"Name": "030337360366", "Vocab": []}, {"Name": "034058019333", "Vocab": []}, {"Name": "034652102893", "Vocab": []}, {"Name": "036698566397", "Vocab": []}, {"Name": "038277325965", "Vocab": []}, {"Name": "044947353558", "Vocab": []}, {"Name": "050363752932", "Vocab": []}, {"Name": "059218437583", "Vocab": []}, {"Name": "061978309590", "Vocab": []}, {"Name": "062185244182", "Vocab": []}, {"Name": "062699237275", "Vocab": []}, {"Name": "063231563077", "Vocab": []}, {"Name": "064759668527", "Vocab": []}, {"Name": "066801735261", "Vocab": []}, {"Name": "077772142356", "Vocab": []}, {"Name": "077919485276", "Vocab": []}, {"Name": "078161292471", "Vocab": []}, {"Name": "081788029393", "Vocab": []}, {"Name": "083016640105", "Vocab": []}, {"Name": "087739544140", "Vocab": []}, {"Name": "090638726514", "Vocab": []}, {"Name": "091233916283", "Vocab": []}, {"Name": "097388778915", "Vocab": []}, {"Name": "099001553190", "Vocab": []}, {"Name": "101744843673", "Vocab": []}, {"Name": "105985708997", "Vocab": []}, {"Name": "109662622369", "Vocab": []}, {"Name": "111181353139", "Vocab": []}, {"Name": "112410297978", "Vocab": []}, {"Name": "121615036225", "Vocab": []}, {"Name": "127529166606", "Vocab": []}, {"Name": "127718418437", "Vocab": []}, {"Name": "130750028832", "Vocab": []}, {"Name": "131392047580", "Vocab": []}, {"Name": "137033309679", "Vocab": []}, {"Name": "139530941127", "Vocab": []}, {"Name": "140652844622", "Vocab": []}, {"Name": "142032656950", "Vocab": []}, {"Name": "142912899323", "Vocab": []}, {"Name": "145620719969", "Vocab": []}, {"Name": "148744396629", "Vocab": []}, {"Name": "155314437216", "Vocab": []}, {"Name": "158811673180", "Vocab": []}, {"Name": "162791471306", "Vocab": []}, {"Name": "168617395007", "Vocab": []}, {"Name": "169840142335", "Vocab": []}, {"Name": "172575556983", "Vocab": []}, {"Name": "180464999189", "Vocab": []}, {"Name": "181713713996", "Vocab": []}, {"Name": "183543891241", "Vocab": []}, {"Name": "191055079410", "Vocab": []}, {"Name": "192391604168", "Vocab": []}, {"Name": "199758164909", "Vocab": []}, {"Name": "203926424789", "Vocab": []}, {"Name": "206771991146", "Vocab": []}, {"Name": "210271058674", "Vocab": []}, {"Name": "213289538010", "Vocab": []}, {"Name": "220646166858", "Vocab": []}, {"Name": "222960071486", "Vocab": []}, {"Name": "223359038432", "Vocab": []}, {"Name": "224794830894", "Vocab": []}, {"Name": "227229894151", "Vocab": []}, {"Name": "227910566849", "Vocab": []}, {"Name": "228239224908", "Vocab": []}, {"Name": "230348351814", "Vocab": []}, {"Name": "231355956791", "Vocab": []}, {"Name": "240083011346", "Vocab": []}, {"Name": "240248461235", "Vocab": []}, {"Name": "240581876496", "Vocab": []}, {"Name": "242168546437", "Vocab": []}, {"Name": "244336657281", "Vocab": []}, {"Name": "245275894831", "Vocab": []}, {"Name": "245728614506", "Vocab": []}, {"Name": "248538605322", "Vocab": []}, {"Name": "255798161096", "Vocab": []}, {"Name": "259876074304", "Vocab": []}, {"Name": "267078289600", "Vocab": []}, {"Name": "272805381125", "Vocab": []}, {"Name": "273049313453", "Vocab": []}, {"Name": "273084835746", "Vocab": []}, {"Name": "277470486932", "Vocab": []}, {"Name": "279023034093", "Vocab": []}, {"Name": "279410285702", "Vocab": []}, {"Name": "280849815452", "Vocab": []}, {"Name": "283145719074", "Vocab": []}, {"Name": "284342423953", "Vocab": []}, {"Name": "285148870545", "Vocab": []}, {"Name": "288067602497", "Vocab": []}, {"Name": "295531046598", "Vocab": []}, {"Name": "305000349049", "Vocab": []}, {"Name": "305150835302", "Vocab": []}, {"Name": "309314840559", "Vocab": []}, {"Name": "314425729696", "Vocab": []}, {"Name": "314866965734", "Vocab": []}, {"Name": "315806953006", "Vocab": []}, {"Name": "317117203785", "Vocab": []}, {"Name": "317603430962", "Vocab": []}, {"Name": "318103268638", "Vocab": []}, {"Name": "318134050014", "Vocab": []}, {"Name": "319089946692", "Vocab": []}, {"Name": "326095425951", "Vocab": []}, {"Name": "328461410589", "Vocab": []}, {"Name": "337580490911", "Vocab": []}, {"Name": "342466631544", "Vocab": []}, {"Name": "350620039857", "Vocab": []}, {"Name": "351785322233", "Vocab": []}, {"Name": "354284469482", "Vocab": []}, {"Name": "354484552643", "Vocab": []}, {"Name": "355995048129", "Vocab": []}, {"Name": "359171007774", "Vocab": []}, {"Name": "360584329388", "Vocab": []}, {"Name": "363650242482", "Vocab": []}, {"Name": "365066708102", "Vocab": []}, {"Name": "366498385319", "Vocab": []}, {"Name": "371200096692", "Vocab": []}, {"Name": "372207806558", "Vocab": []}, {"Name": "375095921293", "Vocab": []}, {"Name": "381742257174", "Vocab": []}, {"Name": "393099793808", "Vocab": []}, {"Name": "396821656922", "Vocab": []}, {"Name": "404440628078", "Vocab": []}, {"Name": "405268876168", "Vocab": []}, {"Name": "411226681814", "Vocab": []}, {"Name": "412095206934", "Vocab": []}, {"Name": "424438291683", "Vocab": []}, {"Name": "425798717452", "Vocab": []}, {"Name": "436733921223", "Vocab": []}, {"Name": "443414949693", "Vocab": []}, {"Name": "443852711055", "Vocab": []}, {"Name": "450857039950", "Vocab": []}, {"Name": "451191618550", "Vocab": []}, {"Name": "454185856165", "Vocab": []}, {"Name": "455822894712", "Vocab": []}, {"Name": "457820558967", "Vocab": []}, {"Name": "459834934337", "Vocab": []}, {"Name": "460761723425", "Vocab": []}, {"Name": "465966151559", "Vocab": []}, {"Name": "467150912639", "Vocab": []}, {"Name": "469968201857", "Vocab": []}, {"Name": "474020524798", "Vocab": []}, {"Name": "477181243680", "Vocab": []}, {"Name": "493192450408", "Vocab": []}, {"Name": "494494558533", "Vocab": []}, {"Name": "499614325945", "Vocab": []}, {"Name": "505039278188", "Vocab": []}, {"Name": "505891605728", "Vocab": []}, {"Name": "510479918103", "Vocab": []}, {"Name": "510777453471", "Vocab": []}, {"Name": "511562681636", "Vocab": []}, {"Name": "527361337113", "Vocab": []}, {"Name": "528817056590", "Vocab": []}, {"Name": "530900656680", "Vocab": []}, {"Name": "533803205831", "Vocab": []}, {"Name": "537461708567", "Vocab": []}, {"Name": "538138168374", "Vocab": []}, {"Name": "538473875077", "Vocab": []}, {"Name": "541789798646", "Vocab": []}, {"Name": "544986574147", "Vocab": []}, {"Name": "547779200790", "Vocab": []}, {"Name": "548000355711", "Vocab": []}, {"Name": "557373386618", "Vocab": []}, {"Name": "560812885864", "Vocab": []}, {"Name": "562049716585", "Vocab": []}, {"Name": "564045475592", "Vocab": []}, {"Name": "566131021403", "Vocab": []}, {"Name": "567158895025", "Vocab": []}, {"Name": "567895297592", "Vocab": []}, {"Name": "575173802345", "Vocab": []}, {"Name": "579402210545", "Vocab": []}, {"Name": "583270022686", "Vocab": []}, {"Name": "583290499959", "Vocab": []}, {"Name": "583882423598", "Vocab": []}, {"Name": "584951898522", "Vocab": []}, {"Name": "584994094531", "Vocab": []}, {"Name": "585850384584", "Vocab": []}, {"Name": "588144376365", "Vocab": []}, {"Name": "588660757704", "Vocab": []}, {"Name": "591315544081", "Vocab": []}, {"Name": "591711494128", "Vocab": []}, {"Name": "592699269693", "Vocab": []}, {"Name": "596306351686", "Vocab": []}, {"Name": "597727363359", "Vocab": []}, {"Name": "599283453199", "Vocab": []}, {"Name": "604786184572", "Vocab": []}, {"Name": "609871523312", "Vocab": []}, {"Name": "612046273316", "Vocab": []}, {"Name": "612547935828", "Vocab": []}, {"Name": "612605394233", "Vocab": []}, {"Name": "613033185716", "Vocab": []}, {"Name": "614751358409", "Vocab": []}, {"Name": "616351047822", "Vocab": []}, {"Name": "620072504016", "Vocab": []}, {"Name": "620986360323", "Vocab": []}, {"Name": "626213288669", "Vocab": []}, {"Name": "630899621549", "Vocab": []}, {"Name": "632173957144", "Vocab": []}, {"Name": "632732971264", "Vocab": []}, {"Name": "634810125325", "Vocab": []}, {"Name": "636556916135", "Vocab": []}, {"Name": "638049158606", "Vocab": []}, {"Name": "641903478309", "Vocab": []}, {"Name": "643152677212", "Vocab": []}, {"Name": "643887229627", "Vocab": []}, {"Name": "645777931858", "Vocab": []}, {"Name": "651383336906", "Vocab": []}, {"Name": "654757066466", "Vocab": []}, {"Name": "655012168106", "Vocab": []}, {"Name": "667400889936", "Vocab": []}, {"Name": "671893148114", "Vocab": []}, {"Name": "673468377916", "Vocab": []}, {"Name": "674510525532", "Vocab": []}, {"Name": "683902439972", "Vocab": []}, {"Name": "687174800567", "Vocab": []}, {"Name": "695638232520", "Vocab": []}, {"Name": "700640865632", "Vocab": []}, {"Name": "700701463576", "Vocab": []}, {"Name": "702295905870", "Vocab": []}, {"Name": "702937017464", "Vocab": []}, {"Name": "706747859798", "Vocab": []}, {"Name": "709454888328", "Vocab": []}, {"Name": "710016666701", "Vocab": []}, {"Name": "713741468749", "Vocab": []}, {"Name": "719833885685", "Vocab": []}, {"Name": "720049552032", "Vocab": []}, {"Name": "721389738920", "Vocab": []}, {"Name": "721627395549", "Vocab": []}, {"Name": "721761845273", "Vocab": []}, {"Name": "724559232059", "Vocab": []}, {"Name": "726683560898", "Vocab": []}, {"Name": "727018987000", "Vocab": []}, {"Name": "732395331058", "Vocab": []}, {"Name": "732737113512", "Vocab": []}, {"Name": "739257925345", "Vocab": []}, {"Name": "741885437928", "Vocab": []}, {"Name": "742212541564", "Vocab": []}, {"Name": "745084491378", "Vocab": []}, {"Name": "758677598986", "Vocab": []}, {"Name": "759384369776", "Vocab": []}, {"Name": "760521558360", "Vocab": []}, {"Name": "765775372361", "Vocab": []}, {"Name": "766550834178", "Vocab": []}, {"Name": "768133276866", "Vocab": []}, {"Name": "768473331764", "Vocab": []}, {"Name": "773547560674", "Vocab": []}, {"Name": "773644258614", "Vocab": []}, {"Name": "777056798903", "Vocab": []}, {"Name": "779244124703", "Vocab": []}, {"Name": "786858667247", "Vocab": []}, {"Name": "787606885298", "Vocab": []}, {"Name": "794139501330", "Vocab": []}, {"Name": "801434557426", "Vocab": []}, {"Name": "803292583410", "Vocab": []}, {"Name": "805192989583", "Vocab": []}, {"Name": "806819504198", "Vocab": []}, {"Name": "814574722021", "Vocab": []}, {"Name": "820092982928", "Vocab": []}, {"Name": "822559694013", "Vocab": []}, {"Name": "824138963929", "Vocab": []}, {"Name": "828248761906", "Vocab": []}, {"Name": "839720678103", "Vocab": []}, {"Name": "839760031292", "Vocab": []}, {"Name": "842263723253", "Vocab": []}, {"Name": "842374991345", "Vocab": []}, {"Name": "845261477223", "Vocab": []}, {"Name": "850029340535", "Vocab": []}, {"Name": "850391861021", "Vocab": []}, {"Name": "852764286132", "Vocab": []}, {"Name": "854669557577", "Vocab": []}, {"Name": "855060197315", "Vocab": []}, {"Name": "860991633338", "Vocab": []}, {"Name": "867521758232", "Vocab": []}, {"Name": "870320621296", "Vocab": []}, {"Name": "872023652344", "Vocab": []}, {"Name": "872291785779", "Vocab": []}, {"Name": "872783901169", "Vocab": []}, {"Name": "881705151422", "Vocab": []}, {"Name": "886938273215", "Vocab": []}, {"Name": "889276782790", "Vocab": []}, {"Name": "889506776409", "Vocab": []}, {"Name": "891366569654", "Vocab": []}, {"Name": "896828720952", "Vocab": []}, {"Name": "898732551065", "Vocab": []}, {"Name": "902673035439", "Vocab": []}, {"Name": "914843296460", "Vocab": []}, {"Name": "918591771230", "Vocab": []}, {"Name": "919948452233", "Vocab": []}, {"Name": "924056624973", "Vocab": []}, {"Name": "928820385957", "Vocab": []}, {"Name": "931193215099", "Vocab": []}, {"Name": "933612779291", "Vocab": []}, {"Name": "934006465132", "Vocab": []}, {"Name": "938135981693", "Vocab": []}, {"Name": "941177769537", "Vocab": []}, {"Name": "943140168462", "Vocab": []}, {"Name": "947943637001", "Vocab": []}, {"Name": "952754344196", "Vocab": []}, {"Name": "966266342783", "Vocab": []}, {"Name": "966398459793", "Vocab": []}, {"Name": "968952563171", "Vocab": []}, {"Name": "971651988777", "Vocab": []}, {"Name": "974967051721", "Vocab": []}, {"Name": "976960125692", "Vocab": []}, {"Name": "977360480485", "Vocab": []}, {"Name": "982920280605", "Vocab": []}, {"Name": "985497650813", "Vocab": []}, {"Name": "994794845432", "Vocab": []}, {"Name": "998317594831", "Vocab": []}, {"Name": "0007259887262", "Vocab": []}, {"Name": "0014916582043", "Vocab": []}, {"Name": "0017148102948", "Vocab": []}, {"Name": "0059550420553", "Vocab": []}, {"Name": "0067099123480", "Vocab": []}, {"Name": "0091673550227", "Vocab": []}, {"Name": "0100532217760", "Vocab": []}, {"Name": "0103697978888", "Vocab": []}, {"Name": "0112519298913", "Vocab": []}, {"Name": "0194442806905", "Vocab": []}, {"Name": "0238178549027", "Vocab": []}, {"Name": "0304151358523", "Vocab": []}, {"Name": "0414130626864", "Vocab": []}, {"Name": "0415475213310", "Vocab": []}, {"Name": "0447136629962", "Vocab": []}, {"Name": "0453996187333", "Vocab": []}, {"Name": "0539818220524", "Vocab": []}, {"Name": "0575127251934", "Vocab": []}, {"Name": "0666519408308", "Vocab": []}, {"Name": "0736829096444", "Vocab": []}, {"Name": "0753784639372", "Vocab": []}, {"Name": "0771330845324", "Vocab": []}, {"Name": "0785731180580", "Vocab": []}, {"Name": "0817991856998", "Vocab": []}, {"Name": "0822434909450", "Vocab": []}, {"Name": "0871438438532", "Vocab": []}, {"Name": "0919936772259", "Vocab": []}, {"Name": "0923952283145", "Vocab": []}, {"Name": "0991616480980", "Vocab": []}, {"Name": "1024903483354", "Vocab": []}, {"Name": "1051591791636", "Vocab": []}, {"Name": "1058636240883", "Vocab": []}, {"Name": "1070241090850", "Vocab": []}, {"Name": "1087914369960", "Vocab": []}, {"Name": "1093018745495", "Vocab": []}, {"Name": "1280805183025", "Vocab": []}, {"Name": "1340815246899", "Vocab": []}, {"Name": "1341932296673", "Vocab": []}, {"Name": "1428158923969", "Vocab": []}, {"Name": "1446651796087", "Vocab": []}, {"Name": "1487925355567", "Vocab": []}, {"Name": "1501410608734", "Vocab": []}, {"Name": "1570941332219", "Vocab": []}, {"Name": "1678811025715", "Vocab": []}, {"Name": "1743740904330", "Vocab": []}, {"Name": "1755072847155", "Vocab": []}, {"Name": "1786435584741", "Vocab": []}, {"Name": "1807869438612", "Vocab": []}, {"Name": "1811266267163", "Vocab": []}, {"Name": "1919492985521", "Vocab": []}, {"Name": "1938730869549", "Vocab": []}, {"Name": "1938858378002", "Vocab": []}, {"Name": "1986291262824", "Vocab": []}, {"Name": "2041090521685", "Vocab": []}, {"Name": "2070463599602", "Vocab": []}, {"Name": "2079423352483", "Vocab": []}, {"Name": "2126263130445", "Vocab": []}, {"Name": "2126269125170", "Vocab": []}, {"Name": "2189111979037", "Vocab": []}, {"Name": "2195889337459", "Vocab": []}, {"Name": "2275223084767", "Vocab": []}, {"Name": "2291988159907", "Vocab": []}, {"Name": "2293189526497", "Vocab": []}, {"Name": "2321955394467", "Vocab": []}, {"Name": "2343385599767", "Vocab": []}, {"Name": "2361310331171", "Vocab": []}, {"Name": "2394810608033", "Vocab": []}, {"Name": "2414795498011", "Vocab": []}, {"Name": "2418452917502", "Vocab": []}, {"Name": "2429090182460", "Vocab": []}, {"Name": "2460047956609", "Vocab": []}, {"Name": "2495010956698", "Vocab": []}, {"Name": "2543319403873", "Vocab": []}, {"Name": "2615809496376", "Vocab": []}, {"Name": "2644037251614", "Vocab": []}, {"Name": "2754892449321", "Vocab": []}, {"Name": "2780726941000", "Vocab": []}, {"Name": "2781369644346", "Vocab": []}, {"Name": "2797568618307", "Vocab": []}, {"Name": "2816690967771", "Vocab": []}, {"Name": "2858717054823", "Vocab": []}, {"Name": "2859071770103", "Vocab": []}, {"Name": "2890339719521", "Vocab": []}, {"Name": "2907822792092", "Vocab": []}, {"Name": "2931868584045", "Vocab": []}, {"Name": "2959212002317", "Vocab": []}, {"Name": "2962458090885", "Vocab": []}, {"Name": "2962654189387", "Vocab": []}, {"Name": "2977094350581", "Vocab": []}, {"Name": "3036257462630", "Vocab": []}, {"Name": "3038507762224", "Vocab": []}, {"Name": "3041110158539", "Vocab": []}, {"Name": "3044238742260", "Vocab": []}, {"Name": "3054678289024", "Vocab": []}, {"Name": "3060520115647", "Vocab": []}, {"Name": "3087428948844", "Vocab": []}, {"Name": "3094952305729", "Vocab": []}, {"Name": "3241141654955", "Vocab": []}, {"Name": "3265314001125", "Vocab": []}, {"Name": "3267350176083", "Vocab": []}, {"Name": "3282292804564", "Vocab": []}, {"Name": "3304415087511", "Vocab": []}, {"Name": "3341881780418", "Vocab": []}, {"Name": "3378248118527", "Vocab": []}, {"Name": "3386167876643", "Vocab": []}, {"Name": "3434429340891", "Vocab": []}, {"Name": "3473438588854", "Vocab": []}, {"Name": "3477137095985", "Vocab": []}, {"Name": "3508745418527", "Vocab": []}, {"Name": "3512383727879", "Vocab": []}, {"Name": "3518714435823", "Vocab": []}, {"Name": "3530344828127", "Vocab": []}, {"Name": "3546693739433", "Vocab": []}, {"Name": "3551049127793", "Vocab": []}, {"Name": "3575831390700", "Vocab": []}, {"Name": "3657896778706", "Vocab": []}, {"Name": "3658911697378", "Vocab": []}, {"Name": "3707906945825", "Vocab": []}, {"Name": "3724617034435", "Vocab": []}, {"Name": "3827002331685", "Vocab": []}, {"Name": "3844264177961", "Vocab": []}, {"Name": "3877723586672", "Vocab": []}, {"Name": "3885927465190", "Vocab": []}, {"Name": "3942408512778", "Vocab": []}, {"Name": "3955939511230", "Vocab": []}, {"Name": "3975687929044", "Vocab": []}, {"Name": "4041445546437", "Vocab": []}, {"Name": "4083903676685", "Vocab": []}, {"Name": "4120843294444", "Vocab": []}, {"Name": "4208671804559", "Vocab": []}, {"Name": "4337191928963", "Vocab": []}, {"Name": "4452630296942", "Vocab": []}, {"Name": "4585743315818", "Vocab": []}, {"Name": "4589287974490", "Vocab": []}, {"Name": "4599733577076", "Vocab": []}, {"Name": "4608463936525", "Vocab": []}, {"Name": "4679492594044", "Vocab": []}, {"Name": "4681077801780", "Vocab": []}, {"Name": "4703598314740", "Vocab": []}, {"Name": "4762962710077", "Vocab": []}, {"Name": "4783166018728", "Vocab": []}, {"Name": "4819758339355", "Vocab": []}, {"Name": "4831754286163", "Vocab": []}, {"Name": "4852991740472", "Vocab": []}, {"Name": "4875120838764", "Vocab": []}, {"Name": "4880493578738", "Vocab": []}, {"Name": "4898444760116", "Vocab": []}, {"Name": "4923696462370", "Vocab": []}, {"Name": "4933666550421", "Vocab": []}, {"Name": "4955532050730", "Vocab": []}, {"Name": "4969913290723", "Vocab": []}, {"Name": "5025835871414", "Vocab": []}, {"Name": "5116519386931", "Vocab": []}, {"Name": "5188915833549", "Vocab": []}, {"Name": "5229242798179", "Vocab": []}, {"Name": "5245644242043", "Vocab": []}, {"Name": "5283924558024", "Vocab": []}, {"Name": "5292740835136", "Vocab": []}, {"Name": "5321745373368", "Vocab": []}, {"Name": "5333436324891", "Vocab": []}, {"Name": "5402359515782", "Vocab": []}, {"Name": "5412205739589", "Vocab": []}, {"Name": "5427155748082", "Vocab": []}, {"Name": "5437855879867", "Vocab": []}, {"Name": "5447064039611", "Vocab": []}, {"Name": "5482305441854", "Vocab": []}, {"Name": "5567854127798", "Vocab": []}, {"Name": "5580937709206", "Vocab": []}, {"Name": "5596558103395", "Vocab": []}, {"Name": "5620816619676", "Vocab": []}, {"Name": "5628878219200", "Vocab": []}, {"Name": "5673627278035", "Vocab": []}, {"Name": "5680124213464", "Vocab": []}, {"Name": "5718175523100", "Vocab": []}, {"Name": "5728502636912", "Vocab": []}, {"Name": "5813294410551", "Vocab": []}, {"Name": "5862646975777", "Vocab": []}, {"Name": "5903850953421", "Vocab": []}, {"Name": "5913195203198", "Vocab": []}, {"Name": "5945757930404", "Vocab": []}, {"Name": "5949478827024", "Vocab": []}, {"Name": "5955105063417", "Vocab": []}, {"Name": "5973920655354", "Vocab": []}, {"Name": "6042607711243", "Vocab": []}, {"Name": "6069711763494", "Vocab": []}, {"Name": "6103955131013", "Vocab": []}, {"Name": "6120785842799", "Vocab": []}, {"Name": "6125477731855", "Vocab": []}, {"Name": "6135321948660", "Vocab": []}, {"Name": "6167638328376", "Vocab": []}, {"Name": "6171217898763", "Vocab": []}, {"Name": "6175334156595", "Vocab": []}, {"Name": "6190663629963", "Vocab": []}, {"Name": "6214744262047", "Vocab": []}, {"Name": "6214917197655", "Vocab": []}, {"Name": "6227476425420", "Vocab": []}, {"Name": "6256458537776", "Vocab": []}, {"Name": "6323354303477", "Vocab": []}, {"Name": "6337362987440", "Vocab": []}, {"Name": "6355418798718", "Vocab": []}, {"Name": "6356540872560", "Vocab": []}, {"Name": "6400012574299", "Vocab": []}, {"Name": "6408824307437", "Vocab": []}, {"Name": "6447158755043", "Vocab": []}, {"Name": "6460878287196", "Vocab": []}, {"Name": "6491698195860", "Vocab": []}, {"Name": "6505212664791", "Vocab": []}, {"Name": "6511290677426", "Vocab": []}, {"Name": "6521083687343", "Vocab": []}, {"Name": "6536222685139", "Vocab": []}, {"Name": "6537457881020", "Vocab": []}, {"Name": "6563157431339", "Vocab": []}, {"Name": "6601217558313", "Vocab": []}, {"Name": "6631475433571", "Vocab": []}, {"Name": "6668536630067", "Vocab": []}, {"Name": "6674827283474", "Vocab": []}, {"Name": "6678314180345", "Vocab": []}, {"Name": "6687039153891", "Vocab": []}, {"Name": "6690433777515", "Vocab": []}, {"Name": "6706122863483", "Vocab": []}, {"Name": "6746278961084", "Vocab": []}, {"Name": "6748750643743", "Vocab": []}, {"Name": "6763472359125", "Vocab": []}, {"Name": "6788530520565", "Vocab": []}, {"Name": "6796231451643", "Vocab": []}, {"Name": "6972273036152", "Vocab": []}, {"Name": "7091060176217", "Vocab": []}, {"Name": "7184719796955", "Vocab": []}, {"Name": "7226039050458", "Vocab": []}, {"Name": "7248187769002", "Vocab": []}, {"Name": "7325887969904", "Vocab": []}, {"Name": "7398713799536", "Vocab": []}, {"Name": "7488377380675", "Vocab": []}, {"Name": "7511824098051", "Vocab": []}, {"Name": "7524708100326", "Vocab": []}, {"Name": "7552360404747", "Vocab": []}, {"Name": "7604245406556", "Vocab": []}, {"Name": "7659703869718", "Vocab": []}, {"Name": "7668897350634", "Vocab": []}, {"Name": "7683324534979", "Vocab": []}, {"Name": "7745270767532", "Vocab": []}, {"Name": "7812803059976", "Vocab": []}, {"Name": "7824966920124", "Vocab": []}, {"Name": "7900709909941", "Vocab": []}, {"Name": "7903426682738", "Vocab": []}, {"Name": "7908154867931", "Vocab": []}, {"Name": "7933139631815", "Vocab": []}, {"Name": "7964957933893", "Vocab": []}, {"Name": "7979857251061", "Vocab": []}, {"Name": "7989039975376", "Vocab": []}, {"Name": "8014486657567", "Vocab": []}, {"Name": "8033392133945", "Vocab": []}, {"Name": "8037546881363", "Vocab": []}, {"Name": "8044643361531", "Vocab": []}, {"Name": "8201024765917", "Vocab": []}, {"Name": "8208032280468", "Vocab": []}, {"Name": "8452938194337", "Vocab": []}, {"Name": "8496362311456", "Vocab": []}, {"Name": "8547732350159", "Vocab": []}, {"Name": "8549133033118", "Vocab": []}, {"Name": "8558120467455", "Vocab": []}, {"Name": "8627646942798", "Vocab": []}, {"Name": "8634120967998", "Vocab": []}, {"Name": "8653498267399", "Vocab": []}, {"Name": "8657604631048", "Vocab": []}, {"Name": "8660093315998", "Vocab": []}, {"Name": "8699633754107", "Vocab": []}, {"Name": "8757424293572", "Vocab": []}, {"Name": "8804351444980", "Vocab": []}, {"Name": "8893107913820", "Vocab": []}, {"Name": "8931993984338", "Vocab": []}, {"Name": "8935421108901", "Vocab": []}, {"Name": "8963031213765", "Vocab": []}, {"Name": "9080252017248", "Vocab": []}, {"Name": "9090028535951", "Vocab": []}, {"Name": "9166727283252", "Vocab": []}, {"Name": "9187939227096", "Vocab": []}, {"Name": "9192550837924", "Vocab": []}, {"Name": "9225216759927", "Vocab": []}, {"Name": "9231968813808", "Vocab": []}, {"Name": "9247575740274", "Vocab": []}, {"Name": "9258611119445", "Vocab": []}, {"Name": "9281587577499", "Vocab": []}, {"Name": "9331619235252", "Vocab": []}, {"Name": "9355284606586", "Vocab": []}, {"Name": "9369476674979", "Vocab": []}, {"Name": "9375492621599", "Vocab": []}, {"Name": "9396808792197", "Vocab": []}, {"Name": "9419601778298", "Vocab": []}, {"Name": "9423721440102", "Vocab": []}, {"Name": "9424814655781", "Vocab": []}, {"Name": "9446480046926", "Vocab": []}, {"Name": "9456687696143", "Vocab": []}, {"Name": "9480312778470", "Vocab": []}, {"Name": "9581780953932", "Vocab": []}, {"Name": "9581955694414", "Vocab": []}, {"Name": "9598817002698", "Vocab": []}, {"Name": "9625094906617", "Vocab": []}, {"Name": "9696603691384", "Vocab": []}, {"Name": "9771515480848", "Vocab": []}, {"Name": "9787609949215", "Vocab": []}, {"Name": "9809402570682", "Vocab": []}, {"Name": "9941363798752", "Vocab": []}, {"Name": "9954890552789", "Vocab": []}, {"Name": "9959413611562", "Vocab": []}, {"Name": "9962761746642", "Vocab": []}, {"Name": "9986452112504", "Vocab": []}, {"Name": "01207680035219", "Vocab": []}, {"Name": "01676172322360", "Vocab": []}, {"Name": "02018132086843", "Vocab": []}, {"Name": "02553464312250", "Vocab": []}, {"Name": "04053379399297", "Vocab": []}, {"Name": "04310520631070", "Vocab": []}, {"Name": "05985674473643", "Vocab": []}, {"Name": "06291089330801", "Vocab": []}, {"Name": "07603725497530", "Vocab": []}, {"Name": "08885105897844", "Vocab": []}, {"Name": "10438527274048", "Vocab": []}, {"Name": "10973813955999", "Vocab": []}, {"Name": "10999547528589", "Vocab": []}, {"Name": "11727059870721", "Vocab": []}, {"Name": "12081425650875", "Vocab": []}, {"Name": "15639447182838", "Vocab": []}, {"Name": "15793375607255", "Vocab": []}, {"Name": "16823249692802", "Vocab": []}, {"Name": "17964902275871", "Vocab": []}, {"Name": "18151238174746", "Vocab": []}, {"Name": "18536556805397", "Vocab": []}, {"Name": "18658102256670", "Vocab": []}, {"Name": "19111804565508", "Vocab": []}, {"Name": "20054699214439", "Vocab": []}, {"Name": "20091684495515", "Vocab": []}, {"Name": "20659772996675", "Vocab": []}, {"Name": "20874959152425", "Vocab": []}, {"Name": "21036516703380", "Vocab": []}, {"Name": "21083724830351", "Vocab": []}, {"Name": "21415211201743", "Vocab": []}, {"Name": "22174723870353", "Vocab": []}, {"Name": "22372193864605", "Vocab": []}, {"Name": "22445679422201", "Vocab": []}, {"Name": "23062166905872", "Vocab": []}, {"Name": "24400384256266", "Vocab": []}, {"Name": "24915041145742", "Vocab": []}, {"Name": "25495570752336", "Vocab": []}, {"Name": "26320687097654", "Vocab": []}, {"Name": "26615495794870", "Vocab": []}, {"Name": "26985994919713", "Vocab": []}, {"Name": "27211936816724", "Vocab": []}, {"Name": "27460498523620", "Vocab": []}, {"Name": "27857315704742", "Vocab": []}, {"Name": "29728806321113", "Vocab": []}, {"Name": "29890606483025", "Vocab": []}, {"Name": "32120820612783", "Vocab": []}, {"Name": "32614853916594", "Vocab": []}, {"Name": "33088696623620", "Vocab": []}, {"Name": "33133082495830", "Vocab": []}, {"Name": "33330608188352", "Vocab": []}, {"Name": "33445577710060", "Vocab": []}, {"Name": "36473252011467", "Vocab": []}, {"Name": "36652800904473", "Vocab": []}, {"Name": "38299265849866", "Vocab": []}, {"Name": "38792861474387", "Vocab": []}, {"Name": "39813654877609", "Vocab": []}, {"Name": "40230393950708", "Vocab": []}, {"Name": "41002526272487", "Vocab": []}, {"Name": "41102956568576", "Vocab": []}, {"Name": "41405481499599", "Vocab": []}, {"Name": "41856300854928", "Vocab": []}, {"Name": "42013553155959", "Vocab": []}, {"Name": "42101324610147", "Vocab": []}, {"Name": "42374992244789", "Vocab": []}, {"Name": "42517392519208", "Vocab": []}, {"Name": "42764128726143", "Vocab": []}, {"Name": "43033133836762", "Vocab": []}, {"Name": "44008696267874", "Vocab": []}, {"Name": "44240911785458", "Vocab": []}, {"Name": "44487014088775", "Vocab": []}, {"Name": "47146087912173", "Vocab": []}, {"Name": "48800682915020", "Vocab": []}, {"Name": "49003425702700", "Vocab": []}, {"Name": "49318087393412", "Vocab": []}, {"Name": "49500382322818", "Vocab": []}, {"Name": "49512161396504", "Vocab": []}, {"Name": "50247378215902", "Vocab": []}, {"Name": "51327306513093", "Vocab": []}, {"Name": "51631478209910", "Vocab": []}, {"Name": "51719860930585", "Vocab": []}, {"Name": "51859428952250", "Vocab": []}, {"Name": "52266358288110", "Vocab": []}, {"Name": "52605056143729", "Vocab": []}, {"Name": "52631603579736", "Vocab": []}, {"Name": "54165354873773", "Vocab": []}, {"Name": "54936850665305", "Vocab": []}, {"Name": "55140136993086", "Vocab": []}, {"Name": "55249800482450", "Vocab": []}, {"Name": "56137490005700", "Vocab": []}, {"Name": "56260648230312", "Vocab": []}, {"Name": "56509276896448", "Vocab": []}, {"Name": "57196248749604", "Vocab": []}, {"Name": "57691597766753", "Vocab": []}, {"Name": "58334292857290", "Vocab": []}, {"Name": "60284631824905", "Vocab": []}, {"Name": "60380472143900", "Vocab": []}, {"Name": "60395513626936", "Vocab": []}, {"Name": "61652459143102", "Vocab": []}, {"Name": "61683021046559", "Vocab": []}, {"Name": "61818306044489", "Vocab": []}, {"Name": "61889400394799", "Vocab": []}, {"Name": "64190822236154", "Vocab": []}, {"Name": "64333383015733", "Vocab": []}, {"Name": "64386173462240", "Vocab": []}, {"Name": "64550708890902", "Vocab": []}, {"Name": "66294093243323", "Vocab": []}, {"Name": "66329571099573", "Vocab": []}, {"Name": "66967270854433", "Vocab": []}, {"Name": "67022645367653", "Vocab": []}, {"Name": "67955482175517", "Vocab": []}, {"Name": "68133448048468", "Vocab": []}, {"Name": "68436981855973", "Vocab": []}, {"Name": "68475925425991", "Vocab": []}, {"Name": "68973477501144", "Vocab": []}, {"Name": "69894518787533", "Vocab": []}, {"Name": "70152407377222", "Vocab": []}, {"Name": "70449708674053", "Vocab": []}, {"Name": "70999609077916", "Vocab": []}, {"Name": "71085134908577", "Vocab": []}, {"Name": "72745467015926", "Vocab": []}, {"Name": "73166226213947", "Vocab": []}, {"Name": "73285941120188", "Vocab": []}, {"Name": "74951794091508", "Vocab": []}, {"Name": "75408255094590", "Vocab": []}, {"Name": "75891482486409", "Vocab": []}, {"Name": "75897322400266", "Vocab": []}, {"Name": "76359424200626", "Vocab": []}, {"Name": "76436236219284", "Vocab": []}, {"Name": "77680239544960", "Vocab": []}, {"Name": "78111504693814", "Vocab": []}, {"Name": "78498687587794", "Vocab": []}, {"Name": "79158325977052", "Vocab": []}, {"Name": "79509183626868", "Vocab": []}, {"Name": "80425914703729", "Vocab": []}, {"Name": "80642123324933", "Vocab": []}, {"Name": "80722643937096", "Vocab": []}, {"Name": "81430684639923", "Vocab": []}, {"Name": "81948897875907", "Vocab": []}, {"Name": "84783970918057", "Vocab": []}, {"Name": "85144046139800", "Vocab": []}, {"Name": "85505420565178", "Vocab": []}, {"Name": "85832585539877", "Vocab": []}, {"Name": "86285642767483", "Vocab": []}, {"Name": "86299785789434", "Vocab": []}, {"Name": "86909203126454", "Vocab": []}, {"Name": "87746391546519", "Vocab": []}, {"Name": "88460029059367", "Vocab": []}, {"Name": "88867557987303", "Vocab": []}, {"Name": "89311640408457", "Vocab": []}, {"Name": "90010081258648", "Vocab": []}, {"Name": "91894131042294", "Vocab": []}, {"Name": "92562502660713", "Vocab": []}, {"Name": "92588582512762", "Vocab": []}, {"Name": "93834530559316", "Vocab": []}, {"Name": "93866426125117", "Vocab": []}, {"Name": "93974730866978", "Vocab": []}, {"Name": "94895077223778", "Vocab": []}, {"Name": "95362344632197", "Vocab": []}, {"Name": "96951754300734", "Vocab": []}, {"Name": "97072186075039", "Vocab": []}, {"Name": "97202466306011", "Vocab": []}, {"Name": "97453931173025", "Vocab": []}, {"Name": "97583679389743", "Vocab": []}, {"Name": "97607552140873", "Vocab": []}, {"Name": "97817835359427", "Vocab": []}, {"Name": "98022742202084", "Vocab": []}, {"Name": "98498734957145", "Vocab": []}, {"Name": "99122205427345", "Vocab": []}, {"Name": "99412242717033", "Vocab": []}, {"Name": "061677577281092", "Vocab": []}, {"Name": "204162079727707", "Vocab": []}, {"Name": "243101144858963", "Vocab": []}, {"Name": "278024997718993", "Vocab": []}, {"Name": "343654413394297", "Vocab": []}, {"Name": "354041972606165", "Vocab": []}, {"Name": "379143115673453", "Vocab": []}, {"Name": "458449267014821", "Vocab": []}, {"Name": "473477157317075", "Vocab": []}, {"Name": "606761253347329", "Vocab": []}, {"Name": "622258231979653", "Vocab": []}, {"Name": "624344518867343", "Vocab": []}, {"Name": "677601503796104", "Vocab": []}, {"Name": "739219781764781", "Vocab": []}, {"Name": "754071968460059", "Vocab": []}, {"Name": "828606973565607", "Vocab": []}, {"Name": "831082927631863", "Vocab": []}, {"Name": "873073049930115", "Vocab": []}, {"Name": "884353204287279", "Vocab": []}, {"Name": "948332000340689", "Vocab": []}, {"Name": "984316858809329", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "TEST_ANISingleEntry_DM_dtmf", "Grammars": ["TEST_ANISingleEntry_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "611", "Vocab": []}, {"Name": "228", "Vocab": []}, {"Name": "888", "Vocab": []}, {"Name": "99", "Vocab": []}, {"Name": "DSG", "Vocab": []}, {"Name": "NRH", "Vocab": []}, {"Name": "RCC", "Vocab": []}, {"Name": "228<PERSON><PERSON>", "Vocab": []}, {"Name": "289", "Vocab": []}, {"Name": "FiServ", "Vocab": []}, {"Name": "TestCallMin", "Vocab": []}, {"Name": "611_228_Entry_English", "Vocab": []}, {"Name": "611_228_Entry_Spanish", "Vocab": []}, {"Name": "888_data_collection", "Vocab": []}, {"Name": "Star_99_English", "Vocab": []}, {"Name": "Star_99_Spanish", "Vocab": []}, {"Name": "611_GSM", "Vocab": []}, {"Name": "611_Data_Collection", "Vocab": []}, {"Name": "OMNI", "Vocab": []}, {"Name": "WNP", "Vocab": []}, {"Name": "PWR", "Vocab": []}, {"Name": "PC", "Vocab": []}, {"Name": "VR", "Vocab": []}, {"Name": "490", "Vocab": []}, {"Name": "491", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "VS1045_AnotherFeature_DM_dtmf", "Grammars": ["VS1045_AnotherFeature_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "yes", "Vocab": []}, {"Name": "no", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "XR0015_GhostCallerConfirmOperatorMDE_DM_dtmf", "Grammars": ["XR0015_GhostCallerConfirmOperatorMDE_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "continue", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "XR2005_MaxNoInput_DM_dtmf", "Grammars": ["XR2005_MaxNoInput_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "main_menu", "Vocab": []}], "EntityType": "ClosedList"}, {"Entity": "XR2010_MaxNoMatch_DM_dtmf", "Grammars": ["XR2010_MaxNoMatch_DM_dtmf.grxml"], "SWI_meaning": [{"Name": "main_menu", "Vocab": []}, {"Name": "request-representative", "Vocab": []}], "EntityType": "ClosedList"}]