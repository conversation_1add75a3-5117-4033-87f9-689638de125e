﻿using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class ConditionModel
    {
        public string Condition { get; set; }

        //created a boolean variable to set true for if condition
        public bool IsIfCondition { get; set; } = false;

        public TransitionModel Transitions { get; set; } = new TransitionModel(); // Corrected initialization

        public EventModel eventCalls { get; set; } = new EventModel(); // Corrected initialization

        public ConditionModel() { }
    }
}