﻿using Microsoft.Bot.ObjectModel;
using NDFToCopilotStudioConverter;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using System;
using System.Text.RegularExpressions;
using NUnit.Framework;
using System.Web;

namespace NDFToCopilotStudioConverter
{
    public static class StateUtility
    {

        public static string TransformCondition(string input1)
        {

            
            String input = SimplifyCondition(input1);
            //commenting the below line as it enclose the right side with double quotes
            //input = SurroundRightSideWithSingleQuotes(input);

            // Step 1: Append Global. to all variable names that are not in quotes and do not already have Global. prefix, excluding "true" or "false".
            //TODO: Added undefined in the regex to skip appenidng global in front of it 
            string pattern = @"(?<!['""])(?<!Global\.)\b(?!true\b|false\b|null\b|undefined\b)([a-zA-Z_][a-zA-Z0-9_]*)\b(?!['""])";
            string result = Regex.Replace(input, pattern, "Global.$1");

            // Step 2: Ensure that variables are on the left side of comparison operators.
            string comparisonPattern = @"(Global\.\w+)\s*(==|!=|<|>|<=|>=)\s*(['""][^'""]*['""]|\d+)";
            result = Regex.Replace(result, comparisonPattern, match =>
            {
                // Extract the variable, operator, and literal value.
                string variable = match.Groups[1].Value;
                string op = match.Groups[2].Value;
                string right = match.Groups[3].Value;

                // Remove quotes from the right side
                right = right.Trim('\'', '\"');

                if (right.Contains("GlobalVars"))
                {
                    right = right.Replace("GlobalVars", "Global.GlobalVars.");
                }
                // Handle booleans, numbers, and strings for right-hand side
                else if (bool.TryParse(right, out _) || double.TryParse(right, out _))
                {
                    // If it's a boolean or a number, leave it unquoted.
                }
                else
                {
                    // Otherwise, replace single quotes with double quotes for strings
                    right = $"\"{right}\"";
                }

                // Construct the expression with the variable on the left.
                return $"{variable} {op} {right}";
            });

            // Reverse comparison if the literal value is on the left side.
            string reversePattern = @"(['""][^'""]*['""]|\d+)\s*(==|!=|<|>|<=|>=)\s*(Global\.\w+)";
            result = Regex.Replace(result, reversePattern, match =>
            {
                // Extract the literal, operator, and variable.
                string left = match.Groups[1].Value;
                string op = match.Groups[2].Value;
                string variable = match.Groups[3].Value;

                // Remove quotes from the left side
                left = left.Trim('\'', '\"');

                // Handle booleans, numbers, and strings for left-hand side
                if (bool.TryParse(left, out _) || double.TryParse(left, out _))
                {
                    // If it's a boolean or a number, leave it unquoted.
                }
                else
                {
                    // Otherwise, replace single quotes with double quotes for strings
                    left = $"\"{left}\"";
                }

                // Reverse the comparison to put the variable on the left.
                return $"{variable} {op} {left}";
            });

            // Step 3: Replace empty string comparisons with IsBlank checks.
            string emptyStringPattern = @"(Global\.\w+)\s*==\s*"""""; // Corrected pattern for empty string
            result = Regex.Replace(result, emptyStringPattern, match =>
            {
                string variable = match.Groups[1].Value;
                return $"IsBlank({variable})"; // Use IsBlank for empty string comparison.
            });

            // Optional: Replace the other direction (if needed)
            string emptyStringPatternNot = @"""""\s*==\s*(Global\.\w+)"; // Corrected pattern for empty string
            result = Regex.Replace(result, emptyStringPatternNot, match =>
            {
                string variable = match.Groups[1].Value;
                return $"!IsBlank({variable})"; // Use !IsBlank for empty string comparison.
            });

            // Step 1: Replace equality comparisons (var == "" or var == " " or var == '' or var == ' ' or var == null)
            //TODO:Changed this for null pattern
            string emptyOrNullPattern = @"\b(Global(?:\.\w+)+|GlobalVars(?:\.\w+)+)\s*==\s*(['""]\s*['""]|""\s*""|''|' '|null|undefined)";
            result = Regex.Replace(result, emptyOrNullPattern, match =>
            {
                string variable = match.Groups[1].Value; // Capture 'Global.var'
                return $"IsBlank({variable})"; // Replace with IsBlankOrNull(Global.var)
            });

            // Step 2: Replace equality comparisons in reverse ("" == var or " " == var or '' == var or ' ' == var or null == var)
            //TODO:Changed this for null pattern 
            string emptyOrNullPatternReverse = @"(['""]\s*['""]|""\s*""|''|' '|null|undefined)\s*==\s*(Global(?:\.\w+)+|GlobalVars(?:\.\w+)+)";
            result = Regex.Replace(result, emptyOrNullPatternReverse, match =>
            {
                string variable = match.Groups[2].Value; // Capture 'Global.var'
                return $"IsBlank({variable})"; // Replace with IsBlankOrNull(Global.var)
            });

            // Step 3: Replace inequality comparisons (var != "" or var != " " or var != '' or var != ' ' or var != null)
            //TODO:Changed this for null pattern 
            string notEmptyOrNullPattern = @"\b(Global(?:\.\w+)+|GlobalVars(?:\.\w+)+)\s*!=\s*(['""]\s*['""]|""\s*""|''|' '|null|undefined)";
            result = Regex.Replace(result, notEmptyOrNullPattern, match =>
            {
                string variable = match.Groups[1].Value; // Capture 'Global.var'
                return $"!IsBlank({variable})"; // Replace with !IsBlankOrNull(Global.var)
            });

            // Step 4: Replace inequality comparisons in reverse ("" != var or " " != var or '' != var or ' ' != var or null != var)
            string notEmptyOrNullPatternReverse = @"(['""]\s*['""]|""\s*""|''|' '|null|undefined)\s*!=\s*(Global(?:\.\w+)+|GlobalVars(?:\.\w+)+)";
            result = Regex.Replace(result, notEmptyOrNullPatternReverse, match =>
            {
                string variable = match.Groups[2].Value; // Capture 'Global.var'
                return $"!IsBlank({variable})"; // Replace with !IsBlankOrNull(Global.var)
            });


            // Step 4: Replace == with = and != with <>
            result = Regex.Replace(result, @"\s*==\s*", " = ");
            result = Regex.Replace(result, @"\s*!=\s*", " <> ");

            // Step 5: Trim extra spaces and replace multiple spaces with a single space
            result = Regex.Replace(result.Trim(), @"\s+", " "); // Replace multiple spaces with a single space
            result = Regex.Replace(result.Trim(), @"(?<==\s*)'(.*?)'(?!\s*)", "\"$1\"");

            /*Console.WriteLine("Updated Condition String:");
            Console.WriteLine(result);*/


            // Convert the result into PowerFX before returning
            // result = ConvertToPowerFX(result);



            //if (result.Contains("GlobalVars"))
            //{
            //    // Split the result string by '='
            //    string[] parts = result.Split('=');

            //    // Ensure the split operation returned at least two parts
            //    if (parts.Length >= 2)
            //    {
            //        // Trim and process the right-hand side
            //        string rightSide = parts[1].Trim();
            //        rightSide = Regex.Replace(rightSide, @"\bGlobalVars\.", "Global.GlobalVars.");

            //        // Check if the left-hand side exists and update the result
            //        if (!string.IsNullOrEmpty(parts[0]))
            //        {
            //            result = parts[0] + "=" + rightSide;
            //        }
            //        else
            //        {
            //            result = "=" + rightSide;
            //        }
            //    }
            //    else
            //    {
            //        // If the split operation did not return two parts, handle gracefully
            //        Console.WriteLine("Warning: The result string does not contain a valid '=' separator.");
            //    }
            //}
           

            return result;
            
        }

        /*static string SimplifyCondition(string condition)
        {
            // Regular expression to match any sessionData path like sessionData.<variable>
            string pattern = @"sessionData\.(\w+)(\.\w+)*";

            // Use Regex to replace all sessionData.<variable> with just <variable>
            string simplifiedCondition = Regex.Replace(condition, pattern, match =>
            {
                // Extract the last part of the path (the last word after the last dot)
                var parts = match.Value.Split('.');
                return parts[parts.Length - 1];
            });

            return simplifiedCondition;
        }*/

        static string SurroundRightSideWithSingleQuotes(string str)
        {
            // Split the string at '=' to separate the left-hand and right-hand sides
            var parts = str.Split("==");

            if (parts.Length == 2)
            {
                // Check if the right-hand side is already surrounded by single quotes
                string rightSide = parts[1].Trim();
                if (bool.TryParse(rightSide, out _) || double.TryParse(rightSide, out _))
                {
                    // If it's a boolean or a number, leave it unquoted.
                }
                else
                {
                    //// Otherwise, replace single quotes with double quotes for strings
                    //if (rightSide.Length > 1 && rightSide[0] == '\'' && rightSide[rightSide.Length - 1] == '\'')
                    //{
                    //    return str; // Already surrounded by single quotes, return as is
                    //}
                    //else
                    //{
                        // Surround the right side with single quotes
                        return $"{parts[0]} = \"{rightSide}\"";
                   // }
                }
            }
            return str; // If input is not in the form 'a=b', return it as is
        }
        public static string SimplifyCondition(string condition)
        {
            if (condition != null) { 
            // Regular expression to match sessionData.<variable> or sessionData.<functionName>()
            string pattern = @"sessionData\.(\w+(\.\w+)*)(\(\))?";  // Match sessionData.<path> or sessionData.<functionName>()

            // Use Regex to replace all sessionData.<variable> or sessionData.<functionName> with just <variable> or <functionName> (converted)
            string simplifiedCondition = Regex.Replace(condition, pattern, match =>
            {
                // Extract the full path (e.g., sessionData.ingressData.isDnisRequiresCallerType())
                string fullPath = match.Value;

                // Remove 'sessionData.' and handle nested paths
                string variable = fullPath.Substring("sessionData.".Length);

                // Split by '.' and keep the last part
                var parts = variable.Split('.');
                variable = parts[parts.Length - 1];  // Get the last segment (e.g., 'isDnisRequiresCallerType')

                // If it's a function (ends with '()'), remove the parentheses and convert first letter to lowercase
                if (variable.EndsWith("()"))
                {
                    variable = variable.Substring(0, variable.Length - 2);  // Remove the "()"
                    if (variable.StartsWith("is"))
                    {
                        variable = variable.Substring(2);  // Remove the 'is'

                    }
                    else if (variable.StartsWith("get"))
                    {
                        variable = variable.Substring(3);  // Remove the 'get'

                    }
                    variable = Char.ToLower(variable[0]) + variable.Substring(1);  // Lowercase the first letter
                }


                return variable;
            });

                 return simplifiedCondition;
             }
            return condition;
        }
        private static string ConvertToPowerFX(string input)
        {
            // Replace logical operators with PowerFX equivalents
            input = input.Replace("&&", " And ");
            input = input.Replace("||", " Or ");

            input = input.Replace("GlobalVars.!IsBlank(", "!IsBlank(GlobalVars.");

            input = input.Replace("Global.!IsBlank(", "!IsBlank(Global.");

            input = input.Replace("GlobalVars.IsBlank(", "IsBlank(GlobalVars.");

            input = input.Replace("Global.IsBlank(", "IsBlank(Global.");

            // Correct negation: Replace "!IsBlank(...)" with "Not(IsBlank(...))"
            input = Regex.Replace(input, @"!\s*IsBlank\(([^)]*)\)", "Not(IsBlank($1))");

            // Ensure `undefined` is correctly replaced with `IsBlank(...)`, preserving `GlobalVars.` variable names
            input = Regex.Replace(input, @"(\bGlobalVars\.\w+|\bGlobal\.\w+)\s*==\s*undefined", "IsBlank($1)");

            // Convert single-quoted strings to double-quoted strings while preserving variable names
            input = Regex.Replace(input, @"(?<![""'])'([^']*)'(?![""'])", "\"$1\"");

            // Handle != undefined: !IsBlank(variable)
            input = Regex.Replace(input, @"(\bGlobalVars\.\w+|\bGlobal\.\w+|\b\w+)\s*!=\s*undefined", "!IsBlank($1)");

            // Convert single-quoted strings to double-quoted strings while preserving variable names
            input = Regex.Replace(input, @"(?<![""'])'([^']*)'(?![""'])", "\"$1\"");

            return input;
        }
        /* public static string TransformCondition(string condition)
         {
             // Define a regex pattern to match condition elements, including method calls and quotes
             string pattern = @"(\b\w+(\.\w+)*|'[^']*'|""[^""]*"")\s*(==|!=|<=|>=|<|>)\s*(\b\w+(\.\w+)*|'[^']*'|""[^""]*"")";
             // string pattern = @"(\b\w+(\.\w+)*)(\.\w+\(\))?\s*(==|!=|<=|>=|<|>)\s*('.*?'|\"".*?\""|\b\w+\b)";

             // Define the replacement function
             string ReplaceMatch(Match match)
             {
                 // Left and right sides of the condition
                 string left = match.Groups[1].Value.Trim();
                 string op = match.Groups[3].Value.Trim();
                 string right = match.Groups[4].Value.Trim();

                 // Swap left and right if the left is a string literal and right is not
                 if ((left.StartsWith("'") || left.StartsWith("\"")) && !(right.StartsWith("'") || right.StartsWith("\"")))
                 {
                     string temp = left;
                     left = right;
                     right = temp;
                 }

                 // Remove quotes from right if present
                 right = right.Trim('\'', '\"');

                 // Handle booleans, numbers, and strings for right-hand side
                 if (bool.TryParse(right, out _) || double.TryParse(right, out _))
                 {
                     // If it's a boolean or a number, leave it unquoted.
                 }
                 else if (string.IsNullOrWhiteSpace(right))
                 {
                     // If it's an empty or whitespace-only string, replace it with IsBlank(left)
                     if (op == "==")
                     {
                         return $"IsBlank({EnsureGlobalPrefix(left)})";
                     }
                     else if (op == "!=")
                     {
                         return $"!IsBlank({EnsureGlobalPrefix(left)})";
                     }
                 }
                 else
                 {
                     // Otherwise, replace single quotes with double quotes for strings
                     right = $"\"{right}\"";
                 }

                 // Convert operators
                 string newOperator = op == "==" ? "=" : op == "!=" ? "<>" : op;

                 // Ensure 'Global.' is prepended to left variable if it doesn't start with it
                 left = EnsureGlobalPrefix(left);

                 return $"{left} {newOperator} {right}";
             }

             // Helper method to ensure 'Global.' prefix
             static string EnsureGlobalPrefix(string variable)
             {
                 variable = variable.Trim();
                 if (!string.IsNullOrEmpty(variable) && !variable.StartsWith("Global.") && !variable.StartsWith("sessionData"))
                 {
                     return "Global." + variable;
                 }
                 return variable;
             }

             // Perform the replacement using regex, and ensure logical operators are handled
             string result = Regex.Replace(condition, pattern, new MatchEvaluator(ReplaceMatch));

             // Ensure 'Global.' is prepended to every occurrence of 'sessionData.' and similar variables in the entire condition
             result = Regex.Replace(result, @"\b(Global\.)?sessionData(\.\w+)*\b", m =>
             {
                 string variable = m.Value;
                 if (!variable.StartsWith("Global."))
                 {
                     variable = "Global." + variable;
                 }
                 return variable;
             });

             // Trim any extra spaces between operators and variables
             result = Regex.Replace(result, @"\s+(&&|\|\|)\s+", " $1 ").Trim();

             return result;
         }*/


    }


}
