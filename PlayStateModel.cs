﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace NDFToCopilotStudioConverter
{
    public class PlayStateModel : StateModel
    {
        public Dictionary<string, TransitionModel> ConditionTransitions { get; set; }
        public Dictionary<string, List<PromptModel>> ConditionPrompts { get; set; }
        public List<SessionMappingModel> SessionMappings { get; set; }
        public List<List<ConditionModel>> ConditionTransitionsList { get; set; }
        public List<PromptModel> prompts { get; set; }  
        public List<List<ConditionModel>> ConditionPromptList { get; set; }
        public string gotoDialogElement { get; set; }
        public PlayStateModel()
        {
            ConditionTransitions = new Dictionary<string, TransitionModel>();
            ConditionPrompts = new Dictionary<string, List<PromptModel>>();
            SessionMappings = new List<SessionMappingModel>();
            ConditionTransitionsList = new List<List<ConditionModel>>();
            ConditionPromptList = new List<List<ConditionModel>>();
            prompts = new List<PromptModel>();
        }

        public override void DisplayDetails()
        {
            Console.WriteLine($"Play State ID: {Id}");
            Console.WriteLine($"Type: {Type}");

            /*Console.WriteLine("\n--- Prompts ---");
            foreach (var conditionPrompt in ConditionPrompts)
            {
                Console.WriteLine($"Condition: {conditionPrompt.Key}");
                var prompts = conditionPrompt.Value;
                if (prompts.Any())
                {
                    Console.WriteLine("  Prompts:");
                    foreach (var prompt in prompts)
                    {
                        Console.WriteLine($"  - Prompt ID: {prompt.PromptId}, Text: {prompt.Text}");
                    }
                }
            }

            Console.WriteLine("\n--- Transitions ---");
            foreach (var conditionTransition in ConditionTransitions)
            {
                Console.WriteLine($"Condition: {conditionTransition.Key}");
                var transition = conditionTransition.Value;
                Console.WriteLine($"- Label: {transition.label}, Next: {transition.next}");
                if (transition.sessionMappings.Any())
                {
                    Console.WriteLine("  Session Mappings:");
                    foreach (var mapping in transition.sessionMappings)
                    {
                        Console.WriteLine($"  - {mapping.key}: {mapping.value} ({mapping.type})");
                    }
                }

                if (transition.InnerConditionTransitions.Any())
                {
                    Console.WriteLine("  Inner Conditions:");
                    foreach (var innerCondition in transition.InnerConditionTransitions)
                    {
                        Console.WriteLine($"  - Inner Condition: {innerCondition.Condition}");
                        // Display nested transitions and session mappings
                        if (innerCondition.Transitions != null)
                        {
                            Console.WriteLine($"    Transition Label: {innerCondition.Transitions.label}, Next: {innerCondition.Transitions.next}");
                        }
                    }
                }
            }*/

            Console.WriteLine("Conditions and Prompt:");
            for (int i = 0; i < ConditionPromptList.Count; i++)
            {
                Console.WriteLine($"-- If Block {i + 1} --");
                foreach (var conditionModel in ConditionPromptList[i])
                {
                    DisplayConditionPromptDetails(conditionModel, 1); // Start with level 1 indentation
                }
            }

            Console.WriteLine("Conditions and Transitions:");
            for (int i = 0; i < ConditionTransitionsList.Count; i++)
            {
                Console.WriteLine($"-- If Block {i + 1} --");
                foreach (var conditionModel in ConditionTransitionsList[i])
                {
                    DisplayConditionTransitionDetails(conditionModel, 1); // Start with level 1 indentation
                }
            }
            Console.WriteLine();
        }
        private void DisplayConditionPromptDetails(ConditionModel conditionModel, int level)
        {
            var indent = new string(' ', level * 2); // Indentation for nested levels

            Console.WriteLine($"{indent}Condition: {conditionModel.Condition}"); // Raw condition string

            // Ensure Transitions is not null and process each transition
            var transition = conditionModel.Transitions;
            if (transition != null)
            {
                Console.WriteLine("Prompt text: ");
                if (transition.PromptList != null)
                {
                    foreach (var prompt in transition.PromptList)
                    {
                        Console.WriteLine(prompt.Text);
                        Console.WriteLine();
                    }
                }
                Console.WriteLine();
                if (transition.InnerConditionTransitions != null && transition.InnerConditionTransitions.Count > 0)
                {
                    Console.WriteLine($"{indent}  Inner Conditions:");
                    foreach (var innerConditionModel in transition.InnerConditionTransitions)
                    {
                        DisplayConditionPromptDetails(innerConditionModel, level + 1); // Recurse for inner conditions
                    }
                }
            }
        }
        private void DisplayConditionTransitionDetails(ConditionModel conditionModel, int level)
        {
            var indent = new string(' ', level * 2); // Indentation for nested levels

            Console.WriteLine($"{indent}Condition: {conditionModel.Condition}"); // Raw condition string

            // Ensure Transitions is not null and process each transition
            var transition = conditionModel.Transitions;
            if (transition != null)
            {
                Console.WriteLine($"{indent}- Label: {transition.label}, Next: {transition.next}");

                if (transition.sessionMappings != null && transition.sessionMappings.Count > 0)
                {
                    Console.WriteLine($"{indent}  Session Mappings:");
                    foreach (var mapping in transition.sessionMappings)
                    {
                        Console.WriteLine($"{indent}  - {mapping.key}: {mapping.value}: {mapping.type}");
                    }
                }
                
                Console.WriteLine() ;
                if (transition.InnerConditionTransitions != null && transition.InnerConditionTransitions.Count > 0)
                {
                    Console.WriteLine($"{indent}  Inner Conditions:");
                    foreach (var innerConditionModel in transition.InnerConditionTransitions)
                    {
                        DisplayConditionTransitionDetails(innerConditionModel, level + 1); // Recurse for inner conditions
                    }
                }
            }
        }
    }
}
