﻿using Microsoft.Bot.ObjectModel;
using System;
using System.Collections.Generic;

namespace NDFToCopilotStudioConverter
{
    public class PromptModel 
    {
        public string PromptId { get; set; }
        public string Text { get; set; }

        public string Cond { get; set; }

        public PromptModel()
        {
        }

        public void DisplayDetails()
        {
            Console.WriteLine($"Prompt ID: {PromptId}");
            Console.WriteLine($"Text: {Text}");
        }
    }
}
