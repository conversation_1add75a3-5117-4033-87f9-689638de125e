using System.Collections.Generic;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public static class ConditionTransitionProcessor
    {
        public static void ProcessConditions(XElement parentElement, List<ConditionModel> conditionTransitions, TransitionModel parentTransition = null)
        {
            ProcessTopLevelConditions(parentElement, conditionTransitions, parentTransition);
            ProcessSiblingConditions(parentElement, conditionTransitions, parentTransition);
        }

        private static void ProcessTopLevelConditions(XElement parentElement, List<ConditionModel> conditionTransitions, TransitionModel parentTransition)
        {
            var topIfElement = parentElement.Element("if");
            if (topIfElement != null)
            {
                var topCondition = CreateConditionModel(topIfElement);
                if (parentTransition != null)
                {
                    parentTransition.InnerConditionTransitions.Add(topCondition);
                }
                else
                {
                    conditionTransitions.Add(topCondition);
                }

                ProcessNestedConditions(topIfElement, topCondition);
            }
        }

        private static ConditionModel CreateConditionModel(XElement conditionElement)
        {
            string condition = conditionElement.Attribute("cond")?.Value ?? "default";
            var transition = ProcessConditionElement(conditionElement);

            return new ConditionModel
            {
                Condition = condition,
                Transitions = transition
            };
        }

        private static void ProcessNestedConditions(XElement parentElement, ConditionModel parentCondition)
        {
            var nestedIfElement = parentElement.Element("if");
            if (nestedIfElement != null)
            {
                var nestedCondition = CreateConditionModel(nestedIfElement);
                parentCondition.Transitions.InnerConditionTransitions.Add(nestedCondition);

                ProcessSiblingConditions(nestedIfElement, null, parentCondition.Transitions);
            }
        }

        private static void ProcessSiblingConditions(XElement parentElement, List<ConditionModel> conditionTransitions, TransitionModel parentTransition)
        {
            var elseifElements = parentElement.Elements("elseif");
            foreach (var elseifElement in elseifElements)
            {
                var condition = CreateConditionModel(elseifElement);
                if (parentTransition != null)
                {
                    parentTransition.InnerConditionTransitions.Add(condition);
                }
                else
                {
                    conditionTransitions.Add(condition);
                }
            }

            var elseElement = parentElement.Element("else");
            if (elseElement != null)
            {
                var condition = CreateConditionModel(elseElement);
                condition.Condition = "else"; // Specific condition string for 'else'
                if (parentTransition != null)
                {
                    parentTransition.InnerConditionTransitions.Add(condition);
                }
                else
                {
                    conditionTransitions.Add(condition);
                }
            }
        }

        private static TransitionModel ProcessConditionElement(XElement element)
        {
            var transition = new TransitionModel
            {
                sessionMappings = new List<SessionMappingModel>(),
                InnerConditionTransitions = new List<ConditionModel>()
            };

            var actionElement = element.Element("action");
            if (actionElement != null)
            {
                transition.label = actionElement.Attribute("label")?.Value ?? "fixedtransition";
                transition.next = actionElement.Attribute("next")?.Value;

                var sessionMappingElements = actionElement.Elements("session-mapping");
                transition.sessionMappings.AddRange(SessionMappingHelper.ProcessSessionMappings(sessionMappingElements));
            }

            return transition;
        }
    }
}
