using System;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public class CollectionConfigurationService
    {
        public static CollectionConfigurationModel ReadCollectionConfiguration(XElement collectionConfigurationElement)
        {
            var collectionConfigurationModel = new CollectionConfigurationModel
            {
                Confirmationmode = collectionConfigurationElement.Attribute("confirmationmode")?.Value,
                Highconfidencelevel = collectionConfigurationElement.Attribute("highconfidencelevel")?.Value,
                Inputmodes = collectionConfigurationElement.Attribute("inputmodes")?.Value
            };

            var thresholdConfigurationElement = collectionConfigurationElement.Element("threshold_configuration");
            if (thresholdConfigurationElement != null)
            {
                var thresholdConfigurationModel = new ThresholdConfigurationModel
                {
                    Maxinvalidanswers = thresholdConfigurationElement.Attribute("maxinvalidanswers")?.Value ?? "0",
                    Maxturns = thresholdConfigurationElement.Attribute("maxturns")?.Value ?? "0",
                    Maxnoinputs = thresholdConfigurationElement.Attribute("maxnoinputs")?.Value ?? "0",
                    Maxnomatches = thresholdConfigurationElement.Attribute("maxnomatches")?.Value ?? "0",
                    Maxrepeats = thresholdConfigurationElement.Attribute("maxrepeats")?.Value ?? "0",
                    Maxhelps = thresholdConfigurationElement.Attribute("maxhelps")?.Value ?? "0"
                };

                collectionConfigurationModel.ThresholdConfiguration = thresholdConfigurationModel;
            }

            // Handle both vxml_properties (with attributes) and vxml-properties (with property elements)
            var vxmlPropertiesModel = ProcessVxmlProperties(collectionConfigurationElement);
            if (vxmlPropertiesModel != null)
            {
                collectionConfigurationModel.VxmlProperties = vxmlPropertiesModel;
            }

            var promptconfigurationElement = collectionConfigurationElement.Element("prompt_configuration");
            if (promptconfigurationElement != null)
            {
                collectionConfigurationModel.PromptConfiguration = PromptConfigurationService.ReadCollectionConfiguration(promptconfigurationElement);
            }

            return collectionConfigurationModel;
        }

        private static VxmlPropertiesModel ProcessVxmlProperties(XElement collectionConfigurationElement)
        {
            VxmlPropertiesModel vxmlPropertiesModel = null;

            // First try vxml_properties with attributes (existing format)
            var vxmlPropertiesElement = collectionConfigurationElement.Element("vxml_properties");
            if (vxmlPropertiesElement != null)
            {
                vxmlPropertiesModel = new VxmlPropertiesModel
                {
                    confidencelevel = vxmlPropertiesElement.Attribute("confidencelevel")?.Value ?? "0",
                    timeout = vxmlPropertiesElement.Attribute("timeout")?.Value ?? "0",
                    incompletetimeout = vxmlPropertiesElement.Attribute("incompletetimeout")?.Value ?? "0",
                    maxspeechtimeout = vxmlPropertiesElement.Attribute("maxspeechtimeout")?.Value ?? "0",
                    termtimeout = vxmlPropertiesElement.Attribute("termtimeout")?.Value ?? "0",
                    interdigittimeout = vxmlPropertiesElement.Attribute("interdigittimeout")?.Value ?? "0",
                    inputmodes = vxmlPropertiesElement.Attribute("inputmodes")?.Value ?? "0",
                    // Extract grammar-related attributes
                    speechgrammarname = vxmlPropertiesElement.Attribute("speech-grammar-name")?.Value ??
                                       vxmlPropertiesElement.Attribute("speechgrammarname")?.Value,
                    dtmfgrammarname = vxmlPropertiesElement.Attribute("dtmf-grammar-name")?.Value ??
                                     vxmlPropertiesElement.Attribute("dtmfgrammarname")?.Value
                };
            }

            // Then try vxml-properties with property elements (new format)
            var vxmlPropsWithElements = collectionConfigurationElement.Element("vxml-properties");
            if (vxmlPropsWithElements != null)
            {
                if (vxmlPropertiesModel == null)
                {
                    vxmlPropertiesModel = new VxmlPropertiesModel();
                }

                // Process property elements
                var properties = vxmlPropsWithElements.Elements("property");
                foreach (var property in properties)
                {
                    string name = property.Attribute("name")?.Value;
                    string value = property.Attribute("value")?.Value;

                    if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(value))
                        continue;

                    switch (name.ToLower())
                    {
                        case "confidencelevel":
                            vxmlPropertiesModel.confidencelevel = value;
                            break;
                        case "timeout":
                            vxmlPropertiesModel.timeout = value;
                            break;
                        case "incompletetimeout":
                            vxmlPropertiesModel.incompletetimeout = value;
                            break;
                        case "maxspeechtimeout":
                            vxmlPropertiesModel.maxspeechtimeout = value;
                            break;
                        case "termtimeout":
                            vxmlPropertiesModel.termtimeout = value;
                            break;
                        case "interdigittimeout":
                            vxmlPropertiesModel.interdigittimeout = value;
                            break;
                        case "inputmodes":
                            vxmlPropertiesModel.inputmodes = value;
                            break;
                        case "speechgrammarname":
                        case "speech-grammar-name":
                            vxmlPropertiesModel.speechgrammarname = value;
                            break;
                        case "dtmfgrammarname":
                        case "dtmf-grammar-name":
                            vxmlPropertiesModel.dtmfgrammarname = value;
                            break;
                    }
                }
            }

            // Also check for grammar information stored as attributes on collection-configuration
            // (This would be set by XmlProcessingClass.ProcessGrammarConfigurations)
            if (vxmlPropertiesModel == null)
            {
                // Create VxmlPropertiesModel if it doesn't exist but we have grammar attributes
                string speechGrammarFromAttr = collectionConfigurationElement.Attribute("speechgrammarname")?.Value;
                string dtmfGrammarFromAttr = collectionConfigurationElement.Attribute("dtmfgrammarname")?.Value;

                if (!string.IsNullOrEmpty(speechGrammarFromAttr) || !string.IsNullOrEmpty(dtmfGrammarFromAttr))
                {
                    vxmlPropertiesModel = new VxmlPropertiesModel
                    {
                        speechgrammarname = speechGrammarFromAttr,
                        dtmfgrammarname = dtmfGrammarFromAttr
                    };
                }
            }
            else
            {
                // Update existing VxmlPropertiesModel with grammar attributes if not already set
                if (string.IsNullOrEmpty(vxmlPropertiesModel.speechgrammarname))
                {
                    vxmlPropertiesModel.speechgrammarname = collectionConfigurationElement.Attribute("speechgrammarname")?.Value;
                }
                if (string.IsNullOrEmpty(vxmlPropertiesModel.dtmfgrammarname))
                {
                    vxmlPropertiesModel.dtmfgrammarname = collectionConfigurationElement.Attribute("dtmfgrammarname")?.Value;
                }
            }

            return vxmlPropertiesModel;
        }
    }
}
