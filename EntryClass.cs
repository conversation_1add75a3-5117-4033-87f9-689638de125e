﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml.Linq;
using Microsoft.Bot.ObjectModel;
using Microsoft.Bot.ObjectModel.Yaml;
using NDFToCopilotStudioConverter;
using Microsoft.PS.Tools;

namespace NDFToCopilotStudioConverter
{
    class EntryClass
    {
        public static string Entry(String xmlContent)
        {
            XElement root;
            try
            {
                // Parse the XML content from the string
                root = XElement.Parse(xmlContent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to parse XML content: {ex.Message}");
                return "Error: Invalid XML format.";
            }
            // List to store DialogModel objects
            List<DialogModel> output = new List<DialogModel>();
            List<CustomStateModel> customStateList = new List<CustomStateModel>();

            // Iterate through each <dialog> element in the XML
            foreach (var dialog in root.Elements("dialog"))
            {
                // Process each <dialog> element and add the resulting DialogModel to the list
                DialogModel dialogModel = DialogService.ProcessDialog(dialog);
                output.Add(dialogModel);
                // Optionally, you can display the details of the dialogModel here
                dialogModel.DisplayDetails();
            }

            foreach (var customState in root.Elements("custom-state"))
            {
                CustomStateModel customStateModel = CustomStateService.ProcessState(customState);
                customStateList.Add(customStateModel);
            }

            // Create a DialogList object and set its dialogModels property
            DialogList dialogList = new DialogList
            {
                dialogModels = output,
                customStates = customStateList
            };

            // Create a FeatureFlagOptions object (assuming it is required and available)
            FeatureFlagOptions featureFlags = new FeatureFlagOptions(); // Adjust initialization as needed

            // Pass both required parameters to the ProjectConverter constructor
            ProjectConverter projectConverter = new ProjectConverter(dialogList, featureFlags);

            // Convert the DialogList to a BotDefinition
            BotDefinition botDefinition = projectConverter.ConvertToBotDefinition();

            // Get all collected grammar mappings
            var allGrammarMappings = projectConverter.GetAllGrammarNameToEntityMappings();
            Console.WriteLine($"\n=== COLLECTED GRAMMAR MAPPINGS ===");
            Console.WriteLine($"Total grammar mappings: {allGrammarMappings.Count}");
            foreach (var mapping in allGrammarMappings)
            {
                Console.WriteLine($"Grammar: {mapping.Key} -> Entity: {mapping.Value}");
            }
            Console.WriteLine($"=== END GRAMMAR MAPPINGS ===\n");

            // Serialize the BotDefinition to YAML
            var resultYaml = YamlSerializer.Serialize(botDefinition);

            // Inform the user that the output has been written
            Console.WriteLine("Output has been written to output.yml file.");
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnEscalate();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnFallback();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnReset();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnStartOver();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnEndConversation();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnMultipleTopicsMatch();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnError();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnSignin();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnSilenceDetection();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnSpeechUnrecognized();
            resultYaml += "\n" + SystemTopicsGenerator.GenerateYamlOnUnknownKeyPress();
            return resultYaml;
        }
    }
}
