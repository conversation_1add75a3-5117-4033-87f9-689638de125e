using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using Microsoft.Bot.ObjectModel;

namespace NDFToCopilotStudioConverter
{
    public class GlobalConfigurationService
    {
        public static GlobalConfigurationModel ReadCollectionConfiguration(XElement globalConfigElement)
        {
            var globalConfigModel = new GlobalConfigurationModel
            {
                Confirmationmode = globalConfigElement?.Attribute("confirmationmode")?.Value
            };

            if (globalConfigElement != null)
            {
                foreach (var element in globalConfigElement.Elements())
                {
                    switch (element.Name.LocalName)
                    {
                        case "failureprompt":
                            globalConfigModel.Failureprompts.Add(new FailurePromptModel
                            {
                                Count = element.Attribute("count")?.Value,
                                Filename = element.Attribute("filename")?.Value,
                                Text = element.Attribute("text")?.Value,
                                Id = element.Attribute("id")?.Value
                            });
                            break;
                        case "successprompts":
                            globalConfigModel.SuccessPrompts.Add(new SuccessPromptModel
                            {
                                Count = element.Attribute("count")?.Value,
                                Filename = element.Attribute("filename")?.Value,
                                Text = element.Attribute("text")?.Value,
                                Id = element.Attribute("id")?.Value
                            });
                            break;
                        case "successcorrectedprompt":
                            globalConfigModel.Successcorrectedprompt.Add(new SuccesscorrectedpromptModel
                            {
                                Count = element.Attribute("count")?.Value,
                                Filename = element.Attribute("filename")?.Value,
                                Text = element.Attribute("text")?.Value,
                                Id = element.Attribute("id")?.Value
                            });
                            break;
                    }
                }
            }

            return globalConfigModel;
        }
    }
}
