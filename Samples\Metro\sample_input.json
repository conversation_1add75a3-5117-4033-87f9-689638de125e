{"grammars": {"primary": "AP1005_ACPStart_DM.grxml\r\nAP1005_ACPStart_DM_dtmf.grxml", "secondary": "AP1006_Menu_DM.grxml\r\nAP1006_Menu_DM_dtmf.grxml\r\nAP1006_Menu_DM_extra.grxml"}, "entities": [{"name": "StartEntity", "files": "start_entity.grxml\r\nstart_entity_dtmf.grxml"}, {"name": "MenuEntity", "files": "menu_entity.grxml\r\nmenu_entity_dtmf.grxml\r\nmenu_entity_backup.grxml"}], "config": {"timeout": 5000, "retries": 3, "description": "This is a single line description", "multiline_config": "line1\r\nline2\r\nline3"}, "simple_string": "no_carriage_returns_here", "empty_string": "", "null_value": null, "number_value": 42, "boolean_value": true}