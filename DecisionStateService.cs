﻿using System;
using System.Collections.Generic;
using System.Xml.Linq;

namespace NDFToCopilotStudioConverter
{
    public static class DecisionStateService
    {
        public static DecisionStateModel ProcessState(XElement stateElement)
        {
            var model = new DecisionStateModel
            {
                Id = stateElement.Attribute("id")?.Value,
                Type = stateElement.Name.LocalName,
                ConditionTransitionsList = new List<List<ConditionModel>>(), // List of condition lists
                SessionMappings = new List<SessionMappingModel>()
            };

            //Process gotodialog if it is not inside action
            var gotoElement = stateElement.Element("gotodialog");
            if (gotoElement != null)
            {
                //26638
                var nextAttr = gotoElement.Attribute("next")?.Value;
                if (!string.IsNullOrEmpty(nextAttr)){
                    if (nextAttr.Contains(".dvxml#")){
                        var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                        if (parts.Length == 2)
                        {
                            model.nextGoto = parts[0];      // before .dvxml#
                            SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                key = "nextState",
                                value = parts[1]
                            };
                            model.SessionMappings.Add(sessionMappingModel);
                            //transition.sessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                        }
                    }
                    else{
                        model.nextGoto = nextAttr;
                    }
                }
                //26638
                //transitionModel.next = gotoElement.Attribute("next")?.Value + ".dvxml";
            }




            // Process assignments if present
            ProcessAssignments(stateElement, model);

            // Process multiple 'if' conditions and store each in a separate list
            var ifElements = stateElement.Elements("if");
            if (ifElements != null)
            {
                foreach (var ifElement in ifElements)
                {
                    var conditionList = new List<ConditionModel>(); // List to store conditions for this 'if' block
                    ProcessTopIfCondition(ifElement, conditionList);
                    model.ConditionTransitionsList.Add(conditionList); // Add this list to the model
                }

            }
            

            var actionElements = stateElement.Elements("action");
            int count = 0;
            if (actionElements != null)
            {
                foreach (var actionElement in actionElements)
                {
                    var ifElementsInsideAction = actionElement.Elements("if");
                    if (ifElementsInsideAction != null)
                    {
                        foreach (var ifElement in ifElementsInsideAction)
                        {
                            var conditionList = new List<ConditionModel>(); // List to store conditions for this 'if' block
                            ProcessTopIfCondition(ifElement, conditionList);

                            model.ConditionTransitionsList.Add(conditionList); // Add this list to the model
                        }
                    }
                    // If no <if> elements are present, process the default action
                    if (!ifElementsInsideAction.Any())
                    {
                        var conditionList = new List<ConditionModel>();
                        var conditionalModel = new ConditionModel();
                        string label = actionElement.Attribute("label")?.Value;

                        // Set default value if label is null or empty
                        if (string.IsNullOrEmpty(label))
                        {
                            label = "default"; // Replace "default" with your desired default value
                        }
                        //TODO: Check with Aditi It was creating unnecessary DSDA Node 
                        // if (count > 1)
                        // {
                        //     model.label = true;
                        // }
                        conditionalModel.Condition = $"label = \"{label}\"";
                       // conditionalModel.Condition = $"label = '{label}'";
                        conditionalModel.Transitions = ProcessActionElement(actionElement);
                        conditionList.Add(conditionalModel);
                        model.ConditionTransitionsList.Add(conditionList);
                        count++;
                    }
                }

            }

            return model;
        }
        public static TransitionModel ProcessActionElement(XElement actionElement)
        {
            var gotoElement = actionElement.Element("gotodialog");

            var transitionModel = new TransitionModel
            {
                label = actionElement.Attribute("label")?.Value,  // Add label if available
                next = actionElement.Attribute("next")?.Value
            };
            if (gotoElement != null)
            {
                //26638
                var nextAttr = gotoElement.Attribute("next")?.Value;
                if (!string.IsNullOrEmpty(nextAttr)){
                    if (nextAttr.Contains(".dvxml#")){
                        var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                        if (parts.Length == 2)
                        {
                           transitionModel.next = parts[0]+ ".dvxml";      // before .dvxml#
                            SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                key = "nextState",
                                value = parts[1]
                            };
                            transitionModel.sessionMappings.Add(sessionMappingModel);
                            //transition.sessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                        }
                    }
                    else{
                        transitionModel.next = nextAttr+ ".dvxml";
                    }
                }
                //26638
                //transitionModel.next = gotoElement.Attribute("next")?.Value + ".dvxml";
            }
            // Check for session mappings (if any)
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("session-mapping"));
            transitionModel.sessionMappings.AddRange(sessionMappings);
            var varMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("var"));
            transitionModel.sessionMappings.AddRange(varMappings);

            return transitionModel;
        }

        private static void ProcessAssignments(XElement stateElement, DecisionStateModel model)
        {
            // Use SessionMappingHelper to process session-mapping elements
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(stateElement.Elements("session-mapping"));
            model.SessionMappings.AddRange(sessionMappings);
            var varMapping = SessionMappingHelper.ProcessSessionMappings(stateElement.Elements("var"));
            model.SessionMappings.AddRange(varMapping);
        }

        private static void ProcessTopIfCondition(XElement ifElement, List<ConditionModel> conditionList)
        {
            string condition = ifElement.Attribute("cond")?.Value;
            var transition = ProcessConditionElement(ifElement);

            // Create a ConditionModel for the top-level 'if'
            var topCondition = new ConditionModel
            {
                Condition = condition,
                Transitions = transition
            };

            // Process nested conditions
            ProcessNestedConditions(ifElement, topCondition);

            // Add the top condition to the list
            conditionList.Add(topCondition);

            // Process sibling 'elseif' and 'else' conditions
            ProcessSiblingConditions(ifElement, conditionList);
        }

        private static void ProcessNestedConditions(XElement parentElement, ConditionModel parentCondition)
        {
            // Extract nested 'if' condition from the top 'if' element
            //var nestedIfElement = parentElement.Element("if");
            //if (nestedIfElement != null)
            //{
            // Add nested 'if' condition to the parent's InnerConditionTransitions
            foreach (var nestedIfElement in parentElement.Elements())

            {
                // Add nested 'if' condition to the parent's InnerConditionTransitions
                string nestedCondition = nestedIfElement.Attribute("cond")?.Value;
                var nestedTransition = ProcessConditionElement(nestedIfElement);
                if (nestedIfElement != null && nestedIfElement.Name == "if")
                {
                    ConditionModel nestedConditionModel = new ConditionModel
                    {
                        Condition = nestedCondition,
                        Transitions = nestedTransition
                    };

                    parentCondition.Transitions.InnerConditionTransitions.Add(nestedConditionModel);

                    //Adding the below code for nested if condition
                    ProcessNestedConditions(nestedIfElement, parentCondition);

                    // Process nested 'elseif' and 'else' within the nested 'if'
                    ProcessSiblingConditions(nestedIfElement, parentCondition.Transitions);

                    


                }
                }
          //  }
        }

        private static void ProcessSiblingConditions(XElement parentElement, List<ConditionModel> conditionList)
        {
            // Process 'elseif' elements
            var elseifElements = parentElement.Elements("elseif");
            foreach (var elseifElement in elseifElements)
            {
                string condition = elseifElement.Attribute("cond")?.Value;
                var transition = ProcessConditionElement(elseifElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };
                ProcessNestedConditions(elseifElement, condObj);

                // Add to conditionList
                conditionList.Add(condObj);
            }

            // Process 'else' element
            var elseElement = parentElement.Element("else");
            if (elseElement != null)
            {
                string condition = "else";
                var transition = ProcessConditionElement(elseElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };
                ProcessNestedConditions(elseElement, condObj);
                // Add to conditionList
                conditionList.Add(condObj);
            }
        }

        private static void ProcessSiblingConditions(XElement parentElement, TransitionModel parentTransition)
        {
            // Process 'elseif' elements
            var elseifElements = parentElement.Elements("elseif");
            foreach (var elseifElement in elseifElements)
            {
                string condition = elseifElement.Attribute("cond")?.Value;
                var transition = ProcessConditionElement(elseifElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };

                // Add to InnerConditionTransitions
                parentTransition.InnerConditionTransitions.Add(condObj);
            }

            // Process 'else' element
            var elseElement = parentElement.Element("else");
            if (elseElement != null)
            {
                string condition = "else";
                var transition = ProcessConditionElement(elseElement);

                ConditionModel condObj = new ConditionModel
                {
                    Condition = condition,
                    Transitions = transition
                };

                //Adding the below code for transition in else
                ProcessNestedConditions(elseElement, condObj);

                // Add to InnerConditionTransitions
                parentTransition.InnerConditionTransitions.Add(condObj);
            }
        }

        private static TransitionModel ProcessConditionElement(XElement element)
        {
            var transition = new TransitionModel
            {
                sessionMappings = new List<SessionMappingModel>(),
                InnerConditionTransitions = new List<ConditionModel>() // Ensure it's empty for flat conditions
            };

            // Use SessionMappingHelper to process session-mapping elements directly under the condition element
            var sessionMappings = SessionMappingHelper.ProcessSessionMappings(element.Elements("session-mapping"));
            transition.sessionMappings.AddRange(sessionMappings);
            var varMapping = SessionMappingHelper.ProcessSessionMappings(element.Elements("var"));
            transition.sessionMappings.AddRange(varMapping);

            // Process action element if present
            var actionElement = element.Element("action");
            var gotoElement = element.Element("gotodialog");
            if (actionElement != null)
            {
                transition.label = actionElement.Attribute("label")?.Value ?? "fixedtransition";
                transition.next = actionElement.Attribute("next")?.Value;
               

                // Process session-mapping elements inside the action
                var actionSessionMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("session-mapping"));
                transition.sessionMappings.AddRange(actionSessionMappings);
                var actionVarMappings = SessionMappingHelper.ProcessSessionMappings(actionElement.Elements("var"));
                transition.sessionMappings.AddRange(actionVarMappings);
            }
            if (gotoElement != null)
            {
                //26638
                var nextAttr = gotoElement.Attribute("next")?.Value;
                if (!string.IsNullOrEmpty(nextAttr)){
                    if (nextAttr.Contains(".dvxml#")){
                        var parts = nextAttr.Split(new[] { ".dvxml#" }, StringSplitOptions.None);
                        if (parts.Length == 2)
                        {
                           transition.next = parts[0]+ ".dvxml";      // before .dvxml#
                            SessionMappingModel sessionMappingModel = new SessionMappingModel{
                                key = "nextState",
                                value = parts[1]
                            };
                            transition.sessionMappings.Add(sessionMappingModel);
                            //transition.sessionMappings.Add(sessionMappingModel);
                            //model.gotoDialogReference = parts[1];    // after #
                        }
                    }
                    else{
                        transition.next = nextAttr+ ".dvxml";
                    }
                }
                //26638
                //transition.next = gotoElement.Attribute("next")?.Value + ".dvxml";
            }

            return transition;
        }
    }
}
